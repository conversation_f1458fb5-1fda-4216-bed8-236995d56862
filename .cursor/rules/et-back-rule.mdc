---
description: 
globs: 
alwaysApply: true
---
这是一个ECS框架的后台系统，系统是为unity游戏打造的，由于使用C#开发，所以可以和客户端共享很多代码。以下为关于此项目的详细介绍：
一、项目文件架构
  1. 我将我的代码分成了两大块，分别存储在Server_Part和Shared_Part文件夹中，其中Server_Part为服务器独有的代码Shared_Part为客户端和服务器共用的c#代码，修改这部分代码的时候要注意兼容性，因为客户端也需要引用这部分代码，所以Shared_Part不可以引用Server_Part中的内容，但是Server_Part可以引用Shared_Part中的内容。
  2. Shared_Part和Server_Part中的代码主要分布在子目录Hotfix和Model下面，Model下面的代码定义了项目的基础数据结构，Hotfix下面的代码定义了所有的功能函数，这么分类是为了方便服务器进行热更新，直接替换掉Hotfix.dll的内容就可以修改服务器逻辑但是不影响服务器的底层数据结构。所以功能函数都以静态扩展函数的形式表现出来了。
  3. 在Hotfix和Model文件夹下面定义了很多子文件夹，如Map、User、Task等等，这些子文件就包含了每个功能需要的代码，如地图功能、用户功能、任务功能等，分工是十分明确的，在修改相关功能的代码时候请你去对应的子文件夹下面查找。下面将这些子文件夹对应的功能范围都详细说明一下：
    * Activity文件夹：存储了所有的活动相关功能代码
    * Attack文件夹：存储了所有和战斗逻辑相关的代码，发起战斗、释放技能、结束战斗等逻辑都在这里面
    * Chat文件夹：存储了和聊天相关的代码
    * Equip文件夹：存储了装备系统相关的代码，包括强化装备、穿戴和卸下装备等功能
    * Global文件件：存储了全局代码，比如系统公告等
    * Mall文件夹：存储了所有和游戏商城、商店相关的代码
    * Map文件夹：存储了和地图相关的代码，如用户、怪物移动等
    * Npc文件夹：存储了怪物和NPC的代码
    * Relation文件夹：存储了用户和村落的关系以及用户之间关系处理的逻辑代码
    * Rode文件夹：坐骑系统相关的代码，目前还没开放
    * Skill文件夹：技能系统相关的代码
    * Task文件夹：任务系统相关的代码
    * Team文件夹：队伍系统相关的代码
    * Thing文件夹：物品系统代码，物品的使用、获得、出售和背包仓库管理都在这里面
    * User文件夹：用户的基础属性、用户状态管理等都在这里面
    * Vip文件夹：vip功能的关系，如挂机、充值等系统都在这里面
    注意部分功能可能有重叠，比如User中可能实现了和聊天相关的代码（向单个用户发送消息），而Chat文件夹中也实现了向单个用户发送消息的功能，你要考虑相关性判断哪些文件夹之间的功能可能存在一定的重叠。
  4. 前后端通信分为两种类型，一种是请求与响应，另一种为消息，请求与响应即客户端发起请求后异步等待服务器回复的响应，然后执行以后的逻辑。消息有客户端给服务器和服务器给客户端的消息，消息发送后继续执行之前的代码逻辑，不会异步等待响应。前后端通信的数据结构定义在每个Shared_Part文件夹下每个功能文件的Net文件中，包括客户端请求、服务器响应、客户端给服务器端的消息、服务器端给客户端的消息，消息传输类定义（DaoInfo）都在这个文件夹下。
二、项目设计原理
  整个项目采用ECS架构，基于ET框架（github上一个很火的unity前后端框架）改造而来。这个框架的核心代码存储在Shared_Part/Core中，其中Entity文件夹和Fiber文件夹下面的代码是重点，你在实现的时候要额外关注。项目最核心的代码文件为Shared_Part/Core/Entity/Entity.cs，这是ECS框架的基础，所有的组件都要继承自Entity并实现对应的事件系统和功能代码。以下是我的设计理念：
  1. 不完全采用ECS架构，对一些没必要的部分（比如活动公告、商城商品等）还是使用了普通的类（没有继承自Entity），这是因为这些部分功能逻辑简单，继承自Entity会使代码变复杂
  2. 牵涉到复杂功能的数据结构都继承自Entity，比如用户、活动、任务、战斗、地图数据全都是Entity的子类，然后实现了事件系统。项目中分为Child（孩子）和Component（组件）两种数据，一个Entity可以拥有多个同类型的Child，但是只能拥有一个同类型的Component，这就是二者的区别，除此之外，其它功能基本都差不多，因为他们都继承自Entity。
  3. 架构中抽象出了一个Fiber的概念，即逻辑处理的协程，框架要求尽量保证相同逻辑的代码都在同一个Fiber中进行（因为是单线程，可以避免并发冲突），但是我突破了框架的限制，将部分数据改成全局共享的，所有的Fiber共享的数据我都存放在单例Server_Part/DotNet/Model/Global/GlobalInfoCache.cs中，你可以查看这个代码看哪些数据可以直接获得。所有的用户、MapNode、怪物、NPC都存储在这里面。
  4. 严格遵循框架要求，Model层只定义数据结构（可以增加极少部分的底层功能函数如Clone），所有的功能函数代码都强制放到Hotfix文件夹下。