namespace MaoYouJi
{
    public static partial class NetComponentSystem
    {
        [EntitySystem]
        public class NetComponent_System_Net_Sockets_AddressFamily_MaoYouJi_NetworkProtocol_AwakeSystem: AwakeSystem<NetComponent, System.Net.Sockets.AddressFamily, MaoYouJi.NetworkProtocol>
        {   
            protected override void Awake(NetComponent self, System.Net.Sockets.AddressFamily addressFamily, MaoYouJi.NetworkProtocol protocol)
            {
                self.Awake(addressFamily, protocol);
            }
        }
    }
}