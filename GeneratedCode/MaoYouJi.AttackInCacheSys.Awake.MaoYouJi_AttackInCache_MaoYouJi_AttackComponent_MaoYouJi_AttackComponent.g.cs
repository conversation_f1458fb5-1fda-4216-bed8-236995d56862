namespace MaoYouJi
{
    public static partial class AttackInCacheSys
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON>i_AttackInCache_MaoYouJi_AttackComponent_Mao<PERSON><PERSON><PERSON>i_AttackComponent_AwakeSystem: AwakeSystem<MaoYouJi.AttackInCache, MaoYouJi.AttackComponent, MaoYouJi.AttackComponent>
        {   
            protected override void Awake(MaoYouJi.AttackInCache self, <PERSON><PERSON><PERSON><PERSON><PERSON>.AttackComponent src, <PERSON><PERSON><PERSON><PERSON><PERSON>.AttackComponent target)
            {
                self.Awake(src, target);
            }
        }
    }
}