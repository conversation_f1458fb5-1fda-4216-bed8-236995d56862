namespace MaoYouJi
{
    public static partial class LockInfoSystem
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON>i_LockInfo_ActorId_CoroutineLock_AwakeSystem: AwakeSystem<MaoYouJi.LockInfo, ActorId, CoroutineLock>
        {   
            protected override void Awake(Mao<PERSON>ouJi.LockInfo self, ActorId lockActorId, CoroutineLock coroutineLock)
            {
                self.Awake(lockActorId, coroutineLock);
            }
        }
    }
}