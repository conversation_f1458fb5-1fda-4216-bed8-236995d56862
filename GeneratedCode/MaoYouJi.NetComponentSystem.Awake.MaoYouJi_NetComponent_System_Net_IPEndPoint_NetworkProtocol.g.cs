namespace MaoYouJi
{
    public static partial class NetComponentSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON><PERSON>_NetComponent_System_Net_IPEndPoint_NetworkProtocol_AwakeSystem: AwakeSystem<MaoYouJi.NetComponent, System.Net.IPEndPoint, NetworkProtocol>
        {   
            protected override void Awake(MaoYouJi.NetComponent self, System.Net.IPEndPoint address, NetworkProtocol protocol)
            {
                self.Awake(address, protocol);
            }
        }
    }
}