namespace MaoYouJi
{
    public static partial class GenMonComponentSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON><PERSON>_GenMonComponent_ConcurrentCollections_ConcurrentHashSet_MonsterGen__System_Collections_Concurrent_ConcurrentDictionary_MonBaseType_int__AwakeSystem: AwakeSystem<MaoYouJi.GenMonComponent, ConcurrentCollections.ConcurrentHashSet<MonsterGen>, System.Collections.Concurrent.ConcurrentDictionary<MonBaseType, int>>
        {   
            protected override void Awake(MaoYouJi.GenMonComponent self, ConcurrentCollections.ConcurrentHashSet<MonsterGen> monsterGens, System.Collections.Concurrent.ConcurrentDictionary<MonBaseType, int> fixedMonsters)
            {
                self.Awake(monsterGens, fixedMonsters);
            }
        }
    }
}