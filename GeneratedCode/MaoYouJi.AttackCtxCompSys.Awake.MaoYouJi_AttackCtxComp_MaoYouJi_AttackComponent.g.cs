namespace MaoYouJi
{
    public static partial class AttackCtxCompSys
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON>i_AttackCtxComp_Mao<PERSON>ouJi_AttackComponent_AwakeSystem: AwakeSystem<<PERSON><PERSON>ouJi.AttackCtxComp, MaoYouJi.AttackComponent>
        {   
            protected override void Awake(MaoYouJi.AttackCtxComp self, <PERSON><PERSON>ou<PERSON><PERSON>.AttackComponent attackComponent)
            {
                self.Awake(attackComponent);
            }
        }
    }
}