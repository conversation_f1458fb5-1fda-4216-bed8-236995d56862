namespace MaoYouJi
{
    public static partial class NetComponentSystem
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON>i_NetComponent_System_Net_Sockets_AddressFamily_NetworkProtocol_AwakeSystem: AwakeSystem<MaoYouJi.NetComponent, System.Net.Sockets.AddressFamily, NetworkProtocol>
        {   
            protected override void Awake(MaoYouJi.NetComponent self, System.Net.Sockets.AddressFamily addressFamily, NetworkProtocol protocol)
            {
                self.Awake(addressFamily, protocol);
            }
        }
    }
}