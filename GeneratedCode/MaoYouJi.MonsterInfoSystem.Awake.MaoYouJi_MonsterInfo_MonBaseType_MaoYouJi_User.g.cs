namespace MaoYouJi
{
    public static partial class MonsterInfoSystem
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON><PERSON>_MonsterInfo_MonBaseType_Mao<PERSON>ouJi_User_AwakeSystem: AwakeSystem<Mao<PERSON>ouJi.MonsterInfo, MonBaseType, MaoYouJi.User>
        {   
            protected override void Awake(Mao<PERSON>ouJi.MonsterInfo self, MonBaseType monBaseType, Mao<PERSON>ouJi.User user)
            {
                self.Awake(monBaseType, user);
            }
        }
    }
}