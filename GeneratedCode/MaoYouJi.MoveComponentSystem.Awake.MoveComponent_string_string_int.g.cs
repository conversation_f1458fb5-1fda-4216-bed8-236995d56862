namespace MaoYouJi
{
    public static partial class MoveComponentSystem
    {
        [EntitySystem]
        public class MoveComponent_string_string_int_AwakeSystem: AwakeSystem<MoveComponent, string, string, int>
        {   
            protected override void Awake(MoveComponent self, string nowMap, string nowPoint, int stepLimit)
            {
                self.Awake(nowMap, nowPoint, stepLimit);
            }
        }
    }
}