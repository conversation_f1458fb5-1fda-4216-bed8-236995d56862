namespace MaoYouJi
{
    public static partial class KilledComponentSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON><PERSON>_KilledComponent_DestroySystem: DestroySystem<MaoYouJi.KilledComponent>
        {   
            protected override void Destroy(Mao<PERSON>ouJi.KilledComponent self)
            {
                self.Destroy();
            }
        }
    }
}