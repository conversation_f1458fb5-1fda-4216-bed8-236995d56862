namespace MaoYouJi
{
    public static partial class AutoMoveComponentSystem
    {
        [EntitySystem]
        public class AutoMoveComponent_long_string_string_AwakeSystem: AwakeSystem<AutoMoveComponent, long, string, string>
        {   
            protected override void Awake(AutoMoveComponent self, long time, string targetMap, string targetPoint)
            {
                self.Awake(time, targetMap, targetPoint);
            }
        }
    }
}