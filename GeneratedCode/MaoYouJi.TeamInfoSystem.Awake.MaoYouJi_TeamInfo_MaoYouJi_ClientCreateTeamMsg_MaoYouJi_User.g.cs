namespace MaoYouJi
{
    public static partial class TeamInfoSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON>i_TeamInfo_MaoYouJi_ClientCreateTeamMsg_MaoYouJi_User_AwakeSystem: AwakeSystem<MaoYouJi.TeamInfo, MaoYouJi.ClientCreateTeamMsg, MaoYouJi.User>
        {   
            protected override void Awake(MaoYouJi.TeamInfo self, MaoYouJi.ClientCreateTeamMsg msg, MaoYouJi.User user)
            {
                self.Awake(msg, user);
            }
        }
    }
}