namespace MaoYouJi
{
    public static partial class NetComponentSystem
    {
        [EntitySystem]
        public class NetComponent_System_Net_IPEndPoint_MaoYouJi_NetworkProtocol_AwakeSystem: AwakeSystem<NetComponent, System.Net.IPEndPoint, MaoYouJi.NetworkProtocol>
        {   
            protected override void Awake(NetComponent self, System.Net.IPEndPoint address, MaoYouJi.NetworkProtocol protocol)
            {
                self.Awake(address, protocol);
            }
        }
    }
}