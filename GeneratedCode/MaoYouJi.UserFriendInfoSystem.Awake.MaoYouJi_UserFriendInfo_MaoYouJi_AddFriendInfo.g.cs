namespace MaoYouJi
{
    public static partial class UserFriendInfoSystem
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON>i_UserFriendInfo_MaoYouJi_AddFriendInfo_AwakeSystem: AwakeSystem<MaoYouJi.UserFriendInfo, MaoYouJi.AddFriendInfo>
        {   
            protected override void Awake(MaoYouJi.UserFriendInfo self, MaoYouJi.AddFriendInfo addFriendInfo)
            {
                self.Awake(addFriendInfo);
            }
        }
    }
}