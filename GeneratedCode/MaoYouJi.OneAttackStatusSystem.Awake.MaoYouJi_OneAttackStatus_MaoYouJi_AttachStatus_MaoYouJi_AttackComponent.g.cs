namespace MaoYouJi
{
    public static partial class OneAttackStatusSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON>i_OneAttackStatus_MaoYouJi_AttachStatus_MaoYouJi_AttackComponent_AwakeSystem: AwakeSystem<<PERSON><PERSON><PERSON><PERSON><PERSON>.OneAttackStatus, Mao<PERSON>ou<PERSON>i.AttachStatus, MaoYouJi.AttackComponent>
        {   
            protected override void Awake(MaoYouJi.OneAttackStatus self, <PERSON><PERSON><PERSON><PERSON><PERSON>.AttachStatus attachStatus, <PERSON><PERSON>ouJi.AttackComponent addSrc)
            {
                self.Awake(attachStatus, addSrc);
            }
        }
    }
}