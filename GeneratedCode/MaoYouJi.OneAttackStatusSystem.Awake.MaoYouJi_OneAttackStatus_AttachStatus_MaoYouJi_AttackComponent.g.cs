namespace MaoYouJi
{
    public static partial class OneAttackStatusSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON><PERSON>_OneAttackStatus_AttachStatus_MaoYouJi_AttackComponent_AwakeSystem: AwakeSystem<MaoYouJi.OneAttackStatus, AttachStatus, Mao<PERSON>ouJi.AttackComponent>
        {   
            protected override void Awake(MaoYouJi.OneAttackStatus self, AttachStatus attachStatus, <PERSON><PERSON>ou<PERSON><PERSON>.AttackComponent addSrc)
            {
                self.Awake(attachStatus, addSrc);
            }
        }
    }
}