namespace MaoYouJi
{
    public static partial class AttackStatusComponentSystem
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON>i_AttackStatusComponent_AwakeSystem: AwakeSystem<MaoYouJi.AttackStatusComponent>
        {   
            protected override void Awake(MaoYouJi.AttackStatusComponent self)
            {
                self.Awake();
            }
        }
    }
}