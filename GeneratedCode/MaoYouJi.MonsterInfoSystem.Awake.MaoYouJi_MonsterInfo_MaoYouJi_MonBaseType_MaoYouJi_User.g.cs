namespace MaoYouJi
{
    public static partial class MonsterInfoSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON><PERSON>_MonsterInfo_MaoYouJi_MonBaseType_MaoYouJi_User_AwakeSystem: AwakeSystem<MaoYouJi.MonsterInfo, MaoYouJi.MonBaseType, MaoYouJi.User>
        {   
            protected override void Awake(MaoYouJi.MonsterInfo self, <PERSON><PERSON>ou<PERSON><PERSON>.MonBaseType monBaseType, MaoYouJi.User user)
            {
                self.Awake(monBaseType, user);
            }
        }
    }
}