namespace MaoYouJi
{
    public static partial class MonsterInfoSystem
    {
        [EntitySystem]
        public class longArray_long_longArray_GetRandValSystem: GetRandValSystem<long[], long, long[]>
        {   
            protected override long GetRandVal(long[] range, long level, long[] levelRange)
            {
                return range.GetRandVal(level, levelRange);
            }
        }
    }
}