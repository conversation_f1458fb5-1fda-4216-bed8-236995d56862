namespace MaoYouJi
{
    public static partial class TeamInfoSystem
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON>i_TeamInfo_ClientCreateTeamMsg_MaoYouJi_User_AwakeSystem: AwakeSystem<MaoYouJi.TeamInfo, ClientCreateTeamMsg, MaoYouJi.User>
        {   
            protected override void Awake(MaoYouJi.TeamInfo self, ClientCreateTeamMsg msg, MaoYouJi.User user)
            {
                self.Awake(msg, user);
            }
        }
    }
}