namespace MaoYouJi
{
    public static partial class AttackComponentSystem
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON>i_AttackComponent_FightInfo_AwakeSystem: AwakeSystem<MaoYouJi.AttackComponent, FightInfo>
        {   
            protected override void Awake(MaoYouJi.AttackComponent self, FightInfo fightInfo)
            {
                self.Awake(fightInfo);
            }
        }
    }
}