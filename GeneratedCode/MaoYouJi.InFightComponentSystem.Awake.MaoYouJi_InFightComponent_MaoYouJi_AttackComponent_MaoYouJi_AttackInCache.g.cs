namespace MaoYouJi
{
    public static partial class InFightComponentSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON><PERSON>_InFightComponent_<PERSON><PERSON><PERSON><PERSON><PERSON>_AttackComponent_<PERSON><PERSON><PERSON><PERSON>i_AttackInCache_AwakeSystem: AwakeSystem<<PERSON><PERSON><PERSON><PERSON><PERSON>.InFightComponent, MaoYouJi.AttackComponent, MaoYouJi.AttackInCache>
        {   
            protected override void Awake(MaoYouJi.InFightComponent self, <PERSON><PERSON><PERSON><PERSON><PERSON>.AttackComponent target, MaoYouJi.AttackInCache attackInCache)
            {
                self.Awake(target, attackInCache);
            }
        }
    }
}