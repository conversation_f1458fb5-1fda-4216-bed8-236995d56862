namespace MaoYouJi
{
    public static partial class KilledComponentSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON><PERSON>_KilledComponent_<PERSON><PERSON><PERSON><PERSON><PERSON>_AttackComponent_AwakeSystem: AwakeSystem<<PERSON><PERSON>ou<PERSON><PERSON>.KilledComponent, <PERSON><PERSON>ou<PERSON><PERSON>.AttackComponent>
        {   
            protected override void Awake(<PERSON><PERSON><PERSON><PERSON><PERSON>.KilledComponent self, <PERSON><PERSON><PERSON><PERSON><PERSON>.AttackComponent killedBy)
            {
                self.Awake(killedBy);
            }
        }
    }
}