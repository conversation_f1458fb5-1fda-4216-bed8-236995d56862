namespace MaoYouJi
{
    public static partial class OneTaskSystem
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON>Ji_OneTask_MaoYouJi_BaseTask_AwakeSystem: AwakeSystem<MaoYouJi.OneTask, MaoYouJi.BaseTask>
        {   
            protected override void Awake(Mao<PERSON>ouJ<PERSON>.OneTask self, <PERSON><PERSON><PERSON>J<PERSON>.BaseTask myTask)
            {
                self.Awake(myTask);
            }
        }
    }
}