namespace MaoYouJi
{
    public static partial class MonsterInfoSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON><PERSON>_MonsterInfo_MaoYouJi_BaseMonster_MaoYouJi_User_AwakeSystem: AwakeSystem<MaoYouJi.MonsterInfo, MaoYouJi.BaseMonster, MaoYouJi.User>
        {   
            protected override void Awake(MaoYouJi.MonsterInfo self, <PERSON><PERSON>ou<PERSON><PERSON>.BaseMonster baseMonster, MaoYouJi.User user)
            {
                self.Awake(baseMonster, user);
            }
        }
    }
}