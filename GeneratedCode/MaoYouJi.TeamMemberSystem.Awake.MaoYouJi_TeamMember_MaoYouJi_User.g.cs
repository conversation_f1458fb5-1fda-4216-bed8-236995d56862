namespace MaoYouJi
{
    public static partial class TeamMemberSystem
    {
        [EntitySystem]
        public class <PERSON><PERSON><PERSON><PERSON>i_TeamMember_MaoYouJi_User_AwakeSystem: AwakeSystem<MaoYouJi.TeamMember, MaoYouJi.User>
        {   
            protected override void Awake(MaoYouJi.TeamMember self, MaoYouJi.User user)
            {
                self.Awake(user);
            }
        }
    }
}