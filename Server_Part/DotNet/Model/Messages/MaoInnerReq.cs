using MemoryPack;

namespace MaoYouJi
{
  // 地图内网请求
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerMoveUserReq)]
  [ResponseType(nameof(InnerMoveUserResp))]
  public partial class InnerMoveUserReq : MaoYouInMessage, IRequest
  {
    public long TargetUserId { get; set; }
    public string OutNowMapName { get; set; } // 外部获取到的当前地图，为null时不判断
    public string OutNowPointName { get; set; } // 外部获取到的当前点，为null时不判断
    public string TargetMapName { get; set; } // 要移动到的目标地图，不能为null
    public string TargetPointName { get; set; } // 要移动到的目标点，不能为null
    public bool IsForceMove { get; set; } = false; // 是否强制移动，如果为true，则强制退出战斗并移动
  }

  // 战斗内网请求
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerStartAttackReq)]
  [ResponseType(nameof(InnerStartAttackResp))]
  public partial class InnerStartAttackReq : MaoYouInMessage, IRequest
  {
    public long attackId; // 指定attackId
    public FightInfo srcFightInfo;
    public FightInfo targetFightInfo;
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerJoinAttackReq)]
  [ResponseType(nameof(InnerJoinAttackResp))]
  public partial class InnerJoinAttackReq : MaoYouInMessage, IRequest
  {
    public long attackId;
    public FightInfo srcFightInfo;
    public FightInfo targetFightInfo;
  }
}