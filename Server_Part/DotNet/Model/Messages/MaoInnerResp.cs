using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON><PERSON>
{
  // 地图内网响应
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerMoveUserResp)]
  public partial class InnerMoveUserResp : MaoYouOutMessage, IResponse
  {
  }

  // 战斗内网响应
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerStartAttackResp)]
  public partial class InnerStartAttackResp : MaoYouOutMessage, IResponse
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerJoinAttackResp)]
  public partial class InnerJoinAttackResp : MaoYouOutMessage, IResponse
  {
  }
}