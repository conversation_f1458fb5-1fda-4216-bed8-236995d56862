using System.Collections.Concurrent;
using System.Threading;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [ChildOf(typeof(TeamInfo))]
  public class TeamMember : Entity, IAwake<User>, ID<PERSON>roy
  {
    public long userId; // 用户ID
  }
  [ChildOf]
  public partial class TeamInfo : Entity, IAwake, IAwake<ClientCreateTeamMsg, User>, IDestroy
  {
    // 创建读写锁实例
    [MemoryPackIgnore]
    [BsonIgnore]
    public ReaderWriterLockSlim memberInfosLock { get; } = new ReaderWriterLockSlim();
  }
}