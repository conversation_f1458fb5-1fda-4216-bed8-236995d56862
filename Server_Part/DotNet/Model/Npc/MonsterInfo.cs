using System.Collections.Generic;

namespace MaoYouJi
{
  public partial class MonsterInfo : Entity, IAwake<BaseMonster, User>, IAwake<MonBaseType, User>, IDestroy
  {
    [StaticField]
    public static readonly List<string> cityNpcPres = ["行人", "路人", "武者", "过客", "商贩", "乞丐", "村民", "老者", "地痞", "浪客",
      "游民", "隐士"];
    [StaticField]
    public static readonly List<string> cityNpcNames = ["李", "王", "张", "赵", "陈", "杨", "吴", "刘", "黄", "周", "孙", "林",
        "钱","冯", "朱", "秦", "魏", "蒋", "韩", "柳"];
    [StaticField]
    public static readonly List<string> cityNpcAfters = ["二", "三", "四", "五", "六", "七", "八", "九", "大叔", "小二",
        "大郎", "阿福", "阿强", "阿牛", "阿虎", "阿龙", "阿宝", "阿才", "阿义", "阿亮", "阿旺", "铁柱", "狗剩", "金娃", "喜儿", "来福", "有才", "大壮", "三儿",
        "富贵", "铁蛋", "大壮", "小满", "福娃", "顺子", "喜娃"];
  }
}