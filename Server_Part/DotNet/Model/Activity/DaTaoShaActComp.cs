using System.Collections.Generic;
using ConcurrentCollections;

namespace MaoYouJi
{
  [ComponentOf(typeof(User))]
  public class UserDaTaoShaInfoComp : Entity, IAwake<DaTaoShaActComp>, IDestroy, ISerializeToEntity
  {
    // 进入时间
    public long enterTime { get; set; }
    // 总击杀数
    public int totalKill { get; set; } = 0;
    // 击杀超过6级的次数
    public int killOver6 { get; set; } = 0;
    // 大逃杀排名
    public int ranking { get; set; } = 100000;
    // 获得奖章数量
    public int jiangZhangCount { get; set; } = 0;
    // 增加的属性信息
    public int addStrength { get; set; } = 0;
    public int addPower { get; set; } = 0;
    public int addQuick { get; set; } = 0;
    public int addMind { get; set; } = 0;
    public int addIq { get; set; } = 0;
    // 保存的属性信息
    public AttackDaoInfo attackDaoInfo { get; set; }
    public BagDaoInfo bagDaoInfo { get; set; }
  }

  [ComponentOf(typeof(Scene))]
  public class DaTaoShaActComp : Entity, IAwake<DaTaoShaActConf>, ISerializeToEntity
  {
    // 停止进入时间，单位为毫秒
    public long StopEnterTime { get; set; } = 10 * 60 * 1000L;
    // 增加禁区的时间间隔，单位为毫秒
    public long AddForbiddenInterval { get; set; } = 60 * 1000L;
    // 隔断时间击杀用户，单位为毫秒
    public long KillUserInterval { get; set; } = 18 * 1000L;
    // 禁区列表
    public List<ForbiddenArea> ForbiddenAreas { get; set; }
    // 大逃杀活动状态，0：未开始，1：进行中，2：开启击杀
    public int State { get; set; } = 0;
    // 是否预计算子区域
    public bool PreCalcSubArea { get; set; } = false;
    // 当前禁区索引
    public int NowForbiddenAreaIndex { get; set; } = 0;
    // 大逃杀用户列表
    public ConcurrentHashSet<long> DaTaoShaUserList { get; set; } = new();
  }
}