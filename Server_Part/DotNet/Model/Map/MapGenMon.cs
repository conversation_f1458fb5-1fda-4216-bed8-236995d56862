using System.Collections.Concurrent;
using ConcurrentCollections;

namespace MaoYouJi
{
  [ComponentOf(typeof(MapNode))]
  public class GenMonComponent : Entity, IAwake<ConcurrentHashSet<MonsterGen>, ConcurrentDictionary<MonBaseType, int>>
  {
    public ConcurrentHashSet<MonsterGen> monsterGens { get; set; } = new();
    public ConcurrentDictionary<MonBaseType, int> fixedMonsters { get; set; } = new(); // 固定刷新的怪物
  }
}