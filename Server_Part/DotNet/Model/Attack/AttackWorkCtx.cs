using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [EnableClass]
  public class AttackFlowContext
  {
    // 攻击者
    public AttackComponent ReplaceSrc;
    // 目标
    public AttackComponent ReplaceTarget;
  }

  public delegate LogicRet CheckCondFunc(CheckCondCtx ctx);

  public class CheckCondCtx : AttackFlowContext
  {
    public List<AttackState> NeedSelfState = new(); // 需要自身拥有状态才能释放
    public List<AttackState> NotSelfState = new(); // 自身不在对应状态下才能释放
    public List<AttackState> NeedTargetState = new(); // 需要目标拥有对应的状态才能释放
    public Dictionary<AttackState, int> MinSelfStateNum = new(); // 限定自身状态的层数才能释放
    public int BlueExpend = 0; // 蓝量消耗
    public List<CheckCondFunc> UniqCheck = new();
  }

  public class StartSongCtx : AttackFlowContext
  {
  }

  public class PreProcCtx : AttackFlowContext
  {
  }

  public class ChangeTargetCtx : AttackFlowContext
  {
    // 切换的新目标
    public AttackComponent newTarget;
  }

  public class SetStateCtx : AttackFlowContext
  {
    public SetStateCtx(AttackComponent target, bool isAdd, AttachStatus add, AttackState remove)
    {
      this.ReplaceTarget = target;
      this.isAdd = isAdd;
      if (add != null)
      {
        addList.Add(add.Clone() as AttachStatus);
      }
      if (remove != AttackState.None)
      {
        removeList.Add(remove);
      }
    }

    public SetStateCtx(AttackComponent target, bool isAdd, List<AttachStatus> add, List<AttackState> remove)
    {
      this.ReplaceTarget = target;
      this.isAdd = isAdd;
      if (add != null)
      {
        this.addList = add.Select(item => item.Clone() as AttachStatus).ToList();
      }
      if (remove != null)
      {
        this.removeList = remove;
      }
    }

    public AttachStatus GetAddStatus(AttackState attackState)
    {
      foreach (AttachStatus status in addList)
      {
        if (status.state == attackState)
        {
          return status;
        }
      }
      return null;
    }

    public void RemoveStateList(bool isAdd, params AttackState[] states)
    {
      HashSet<AttackState> checkList = new(states);
      if (isAdd)
      {
        foreach (AttachStatus next in addList)
        {
          if (checkList.Contains(next.state))
            addList.Remove(next);
        }
      }
      else
      {
        foreach (AttackState next in removeList)
        {
          if (checkList.Contains(next))
            removeList.Remove(next);
        }
      }
    }

    // 是增加还是移除，true为增加，false为移除
    public bool isAdd = true;
    // 状态类型
    public List<AttachStatus> addList = new();
    // 状态类型
    public List<AttackState> removeList = new();
    // 是否自动删除的
    public bool isAutoRemove;
    // 状态来源
    public FightInfo stateSrc = null;
    // 来源描述
    public string srcDesc = null;
  }

  public delegate void ChangeHitFunc(IsHitCtx ctx);
  public class IsHitCtx : AttackFlowContext
  {
    public long hitRate;
    public long miss;
    public ChangeHitFunc changeHit = null;
  }

  public delegate LogicRet CalcDmgFunc(CalcDmgCtx ctx);
  public delegate void ExecDmgFunc(CalcDmgCtx ctx);

  [EnableClass]
  // 计算伤害前注入
  public class BeforeCalcDmgInject
  {
    public CalcDmgFunc procBeforeCalcDmg = null;
    public int priority = 0;
  }

  [EnableClass]
  // 计算伤害后注入
  public class BeforeExecDmgInject
  {
    public CalcDmgFunc procBeforeExecDmg = null;
    public int priority = 0;
  }

  [EnableClass]
  // 执行伤害的注入
  public class ExecDmgInject
  {
    public ExecDmgFunc execDmg = null;
    public int priority = 0;
  }

  [EnableClass]
  public class AfterDmgInject
  {
    public CalcDmgFunc procAfterDmg = null;
    public int priority = 0;
  }

  public class CalcDmgCtx : AttackFlowContext
  {
    // 是否百分比攻击
    public bool IsPercentDmg = false;
    // 是否增加基础攻击，不是百分比攻击的时候才生效
    public bool IsAddBaseDmg = true;
    // 状态类型
    public AttackState State = AttackState.None;
    // 伤害具体值
    public long DmgNum = 0;
    // 附加的伤害
    public long ExtraDmg = 0;
    // 真实伤害比例，将普通伤害一定比例转化成真实伤害，默认为0
    public long TranRealPercent = 0;
    // 外界状态决定的增伤系数
    public long AddPercent = 0;
    // 初始伤害值
    public long InitDmgNum = 0;
    // 真实伤害值
    public long RealDmgNum = 0;
    // 最终总伤害
    public long FinalDmgNum = 0;
    // 被抵挡的伤害
    public long DefendNum = 0;
    // 实际伤害比例，在最后结算，主要用于特殊技能如一夫当关
    public long FinalDmgPercent = 1000;
    // 是否击杀
    public bool Killed;
    // 伤害类型
    public DmgType DmgType = DmgType.Physics;
    // 是否可以触发重击
    public bool CanHeavyHit = true;
    // 是否暴击
    public bool HasCrit = false;
    // 计算伤害前
    public List<BeforeCalcDmgInject> BeforeCalcDmg = new();
    // 修改伤害
    public List<BeforeExecDmgInject> BeforExecDmg = new();
    // 执行伤害
    public ExecDmgInject ExecDmg = null;
    // 造成伤害后
    public List<AfterDmgInject> AfterDmg = new();

    public void AddBeforeCalcDmgInject(BeforeCalcDmgInject inject)
    {
      BeforeCalcDmg.Add(inject);
      BeforeCalcDmg.Sort((a, b) => a.priority - b.priority);
    }

    public void AddBeforeExecDmgInject(BeforeExecDmgInject inject)
    {
      BeforExecDmg.Add(inject);
      BeforExecDmg.Sort((a, b) => a.priority - b.priority);
    }

    public void AddAfterDmgInject(AfterDmgInject inject)
    {
      AfterDmg.Add(inject);
      AfterDmg.Sort((a, b) => a.priority - b.priority);
    }

    public void SetExecDmgInject(ExecDmgInject inject)
    {
      if (ExecDmg == null)
        ExecDmg = inject;
      else if (inject.priority > ExecDmg.priority)
        ExecDmg = inject;
    }
  }

  public class CureTargetCtx : AttackFlowContext
  {
    // 治疗量
    public long cureHpNum = 0;
    // 治疗量2
    public long cureMpNum = 0;
    // 治疗类型
    public CureType cureType = CureType.HP;
  }

  public delegate void SelfDefineFunc(AttackCtxComp ctx);

  public class SelfDefineCtx : AttackFlowContext
  {
    public SelfDefineFunc selfDefine;
  }
}