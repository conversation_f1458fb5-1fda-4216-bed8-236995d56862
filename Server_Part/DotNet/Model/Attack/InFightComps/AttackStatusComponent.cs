using System.Collections.Generic;

namespace MaoYouJi
{
  [ComponentOf(typeof(InFightComponent))]
  public class AttackStatusComponent : <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IDestroy
  {

  }
  [ChildOf(typeof(AttackStatusComponent))]
  public class OneAttackStatus : En<PERSON><PERSON>, IAwake<AttachStatus, AttackComponent>, ID<PERSON>roy
  {
    public AttackState attackState;
    public string jobId;
  }

  public static class AttackStateHelper
  {
    [StaticField]
    private static readonly HashSet<AttackState> NegativeStates = new()
    {
        AttackState.TAKE_WEAPON,
        AttackState.SILENT,
        AttackState.DIZZY,
        AttackState.POISON,
        AttackState.Loss_Blood,
        AttackState.ARMOR_BREAK,
        AttackState.BLADE_BREAK,
        AttackState.Hua_Shang,
        AttackState.Si_Lie,
        AttackState.Chu_Xue,
        AttackState.Shen_Pan,
        AttackState.Chui_Fei,
        AttackState.Kuang<PERSON>eng_<PERSON>,
        AttackState.An<PERSON><PERSON>_<PERSON><PERSON>,
        AttackState.LingHun_<PERSON><PERSON><PERSON>,
        AttackState.ChanRao,
        AttackState.Chi_Hu<PERSON>,
        AttackState.BinShuang_XinXing,
        AttackState.Tao_Pao,
        AttackState.Rong_Jia,
    };

    [StaticField]
    public static readonly HashSet<AttackState> ChangeAutoAttackStates = new()
    {
      AttackState.KuangFeng_LongZhao,
      AttackState.BinShuang_XinXing,
      AttackState.HaoRen_Ka,
      AttackState.Ji_Feng,
      AttackState.Tu_Jin,
      AttackState.HeiAnZhi_Zhou,
      AttackState.Chi_Huan,
    };

    [StaticField]
    public static readonly HashSet<AttackState> MianYiStates = new()
    {
      AttackState.TAKE_WEAPON,
      AttackState.SILENT,
      AttackState.DIZZY,
      AttackState.Chui_Fei,
      AttackState.ChanRao,
    };

    [StaticField]
    public static readonly HashSet<AttackState> CanFreshStates = new()
    {
      AttackState.LieYan_ZhuoShao,
    };

    [StaticField]
    public static readonly HashSet<AttackState> CanStackStates = new()
    {
      AttackState.Shen_Pan,
      AttackState.MoFa_Dun,
      AttackState.LieYan_ZhuoShao,
      AttackState.HuoYan_PinZhang,
      AttackState.ShenDong_ZhiHan,
    };

    [StaticField]
    public static readonly HashSet<AttackState> PosionStates = new()
    {
      AttackState.POISON,
      AttackState.Loss_Blood,
      AttackState.Hua_Shang,
      AttackState.Si_Lie,
      AttackState.AnYin_Jian,
      AttackState.LingHun_ZhengJi,
    };

    [StaticField]
    public static readonly HashSet<AttackState> CureStates = new()
    {
      AttackState.Cure,
    };

    [StaticField]
    public static readonly HashSet<AttackState> BreakSongStates = new()
    {
      AttackState.SILENT,
      AttackState.DIZZY,
      AttackState.Chui_Fei,
      AttackState.ChanRao,
      AttackState.FROZEN,
      AttackState.Tao_Pao,
    };

    public static bool IsNegativeState(AttackState state) => NegativeStates.Contains(state);
    public static bool IsPositiveState(AttackState state) => !IsNegativeState(state);
    public static bool IsChangeAutoAttackState(AttackState state) => ChangeAutoAttackStates.Contains(state);
    public static bool IsMianYiState(AttackState state) => MianYiStates.Contains(state);
    public static bool IsCanStackState(AttackState state) => CanStackStates.Contains(state);
    public static bool IsPosionState(AttackState state) => PosionStates.Contains(state);
    public static bool IsCureState(AttackState state) => CureStates.Contains(state);
    public static bool IsBreakSongState(AttackState state) => BreakSongStates.Contains(state);
  }
}