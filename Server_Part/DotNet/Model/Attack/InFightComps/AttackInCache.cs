using System.Collections.Concurrent;
using System.Collections.Generic;
using ConcurrentCollections;

namespace MaoYouJi
{
  [EnableClass]
  public class StaticAttackInfo
  {
    // 战斗对象的战斗组件
    public EntityRef<AttackComponent> attackComponent;
    // 用户激活的天赋技能
    public Skill activeTalent;
    // 用户激活的职业被动
    public Skill activeJobPassive = null;
    // 用户当前的武器
    public Equipment weapon = null;
    // 当前激活的在战斗中有效的套装效果
    public ConcurrentDictionary<EquipBonusName, EquipBonus> activeEquipBonus = new();
    // 主动攻击的用户列表
    public ConcurrentHashSet<long> activeAttackUsers = new();
  }

  [ChildOf(typeof(Scene))]
  public class AttackInCache : Entity, IAwake<AttackComponent, AttackComponent>, IDestroy
  {
    // 战斗地图信息
    public string MapName { get; set; }
    public string PointName { get; set; }
    // 参与战斗的列表
    public ConcurrentDictionary<long, EntityRef<AttackComponent>> FightList { get; set; } = new();
    // 开始时间
    public long StartTime { get; set; }
    // 更新时间
    public long UpdateTime { get; set; }
    public bool IsEnd { get; set; } = false;
  }
}