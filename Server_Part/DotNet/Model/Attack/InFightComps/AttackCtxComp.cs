using System.Collections.Generic;

namespace MaoYouJi
{
  [ComponentOf(typeof(AttackInCache))]
  public class AttackCtxComp : Entity, IAwake<AttackComponent>, IDestroy
  {
    // 当前的工作流节点
    public AttackFlowNode NowFlowNode { get; set; } = null;
    // 根节点，用来打印工作流
    public AttackFlowNode RootFlowNode { get; set; } = null;
    // 注入的信息
    public List<string> InjectInfos { get; set; } = new();
    // 战斗对象
    public EntityRef<AttackComponent> Src { get; set; }
    // 攻击目标
    public EntityRef<AttackComponent> NowTarget { get; set; }
    // 当前使用的技能
    public Skill Skill { get; set; }
    // 当前使用的物品
    public Thing Thing { get; set; }
    // 当前生效的状态
    public AttachStatus NowStatus { get; set; } = null;
    // 攻击类型
    public AttackType AttackType { get; set; } = AttackType.Normal;
    // 战斗标签
    public long CtxFlag { get; set; } = 0;
  }
}