namespace MaoYouJi
{
  public struct UserKillUserEvent
  {
    public User deader;
    public User killedBy;
    public AttackInCache attackInCache;
  }

  public struct UserKillMonsterEvent
  {
    public MonsterInfo deader;
    public User killedBy;
    public AttackInCache attackInCache;
  }

  public struct MonsterKillUserEvent
  {
    public User deader;
    public MonsterInfo killedBy;
    public AttackInCache attackInCache;
  }

  public struct MonsterKillMonsterEvent
  {
    public MonsterInfo deader;
    public MonsterInfo killedBy;
    public AttackInCache attackInCache;
  }

  public struct UserDeadEvent
  {
    public User deader;
    public AttackComponent killedBy;
    public AttackInCache attackInCache;
  }

  public struct MonsterDeadEvent
  {
    public MonsterInfo deader;
    public AttackComponent killedBy;
    public AttackInCache attackInCache;
  }
}