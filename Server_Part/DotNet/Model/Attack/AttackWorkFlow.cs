// 工作流节点基类
namespace MaoYouJi
{
  [EnableClass]
  public abstract class AttackFlowNode
  {
    public AttackFlowEnum FlowType { get; set; }
    public AttackFlowNode ParentNode { get; set; } // 父节点
    public AttackFlowNode LeftNode { get; set; } // 左节点
    public AttackFlowNode RightNode { get; set; } // 右节点
    public AttackFlowNode ChildNode { get; set; } // 子节点
    public AttackFlowContext NodeCtx { get; set; } // 节点上下文

    // 节点执行的抽象方法
    public abstract LogicRet Execute();

    // 执行节点树的方法
    public LogicRet ExecuteTree()
    {
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      attackCtx.NowFlowNode = this;
      LogicRet result;

      // 执行当前节点
      result = Execute();
      if (result != LogicRet.Success)
        return result;

      // 优先执行右节点
      if (RightNode != null)
      {
        result = RightNode.ExecuteTree();
        if (result != LogicRet.Success)
          return result;
      }

      // 执行子节点
      if (ChildNode != null)
      {
        result = ChildNode.ExecuteTree();
      }

      return result;
    }
  }

  public struct AttackFlowExec
  {
    public AttackFlowNode Node;
  }

  public class CheckPreCondNode : AttackFlowNode
  {
    public CheckPreCondNode(CheckCondCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Check_Pre_Cond;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Check_Pre_Cond, new AttackFlowExec() { Node = this });
    }
  }

  public class StartSongNode : AttackFlowNode
  {
    public StartSongNode(StartSongCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Start_Song;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Start_Song, new AttackFlowExec() { Node = this });
    }
  }

  public class PreProcNode : AttackFlowNode
  {
    public PreProcNode(PreProcCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Pre_Proc;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Pre_Proc, new AttackFlowExec() { Node = this });
    }
  }

  public class ChangeTargetNode : AttackFlowNode
  {
    public ChangeTargetNode(ChangeTargetCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Change_Target;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Change_Target, new AttackFlowExec() { Node = this });
    }
  }

  public class SetStateNode : AttackFlowNode
  {
    public SetStateNode(SetStateCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Set_State;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Set_State, new AttackFlowExec() { Node = this });
    }
  }

  public class IsHitNode : AttackFlowNode
  {
    public IsHitNode(IsHitCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Is_Hit;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Is_Hit, new AttackFlowExec() { Node = this });
    }
  }

  public class CalcDmgNode : AttackFlowNode
  {
    public CalcDmgNode(CalcDmgCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Calc_Dmg;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Calc_Dmg, new AttackFlowExec() { Node = this });
    }
  }

  public class CureTargetNode : AttackFlowNode
  {
    public CureTargetNode(CureTargetCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Cure_Target;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Cure_Target, new AttackFlowExec() { Node = this });
    }
  }

  public class DmgTargetNode : AttackFlowNode
  {
    public DmgTargetNode(CalcDmgCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Dmg_Target;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Dmg_Target, new AttackFlowExec() { Node = this });
    }
  }

  public class KillTargetNode : AttackFlowNode
  {
    public KillTargetNode(CalcDmgCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Kill_Target;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Kill_Target, new AttackFlowExec() { Node = this });
    }
  }

  public class QuitAttackNode : AttackFlowNode
  {
    public QuitAttackNode(AttackFlowContext ctx)
    {
      this.FlowType = AttackFlowEnum.Quit_Attack;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Quit_Attack, new AttackFlowExec() { Node = this });
    }
  }

  public class EndAttackNode : AttackFlowNode
  {
    public EndAttackNode(AttackFlowContext ctx)
    {
      this.FlowType = AttackFlowEnum.End_Attack;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.End_Attack, new AttackFlowExec() { Node = this });
    }
  }

  public class SelfDefineNode : AttackFlowNode
  {
    public SelfDefineNode(SelfDefineCtx ctx)
    {
      this.FlowType = AttackFlowEnum.Self_Define;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Self_Define, new AttackFlowExec() { Node = this });
    }
  }

  public class ProcRltNode : AttackFlowNode
  {
    public ProcRltNode(AttackFlowContext ctx)
    {
      this.FlowType = AttackFlowEnum.Proc_Rlt;
      this.NodeCtx = ctx;
    }

    public override LogicRet Execute()
    {
      return EventSystem.Instance.Invoke<AttackFlowExec, LogicRet>((long)AttackFlowEnum.Proc_Rlt, new AttackFlowExec() { Node = this });
    }
  }
}