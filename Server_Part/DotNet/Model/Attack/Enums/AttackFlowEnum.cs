namespace MaoYouJi
{
  // 工作流类型
  public enum AttackFlowEnum
  {
    Check_Pre_Cond, // 检查前提条件
    Start_Song, // 开始咏唱
    Pre_Proc, // 预处理
    Is_Hit, // 是否命中
    Set_State, // 设置状态
    Calc_Dmg, // 计算攻击伤害
    Cure_Target, // 治疗目标
    Dmg_Target, // 伤害目标
    Change_Target, // 变换攻击目标
    Kill_Target, // 击杀目标
    Quit_Attack, // 退出战斗
    End_Attack, // 结束战斗
    Self_Define, // 自定义处理
    Proc_Rlt, // 处理最终结果
  }
}