using System.Collections.Generic;

namespace MaoYouJi
{
  [EnableClass]
  public static class AttackFlowInjectInfo
  {
    public static Dictionary<AttackState, AttackFlowEnum> SelfStateInjects { get; set; } = new()
    {
      { AttackState.ShenD<PERSON>_<PERSON>, AttackFlowEnum.Pre_Proc }
    };
    public static Dictionary<SkillIdEnum, AttackFlowEnum> SkillInjects { get; set; } = new()
    {
      { SkillIdEnum.QiangLi_DaJi, AttackFlowEnum.Check_Pre_Cond },
      { SkillIdEnum.Heng_Sao, AttackFlowEnum.Check_Pre_Cond },
      { SkillIdEnum.Ya_<PERSON>hi, AttackFlowEnum.Check_Pre_Cond },
      { SkillIdEnum.NuFeng_ZhiJi, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.NuFeng_ZhanSha, AttackFlowEnum.Check_Pre_Cond },
      { SkillIdEnum.MengQin_YiJi, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.<PERSON><PERSON><PERSON>_<PERSON>, AttackFlowEnum.Check_Pre_Cond },
      { SkillIdEnum.GuangMing_Shen<PERSON>an, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.TianFa_ZhiLei, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.AnYin_BaoZha, AttackFlowEnum.Check_Pre_Cond },
      { SkillIdEnum.AnYin_JiaoSha, AttackFlowEnum.Check_Pre_Cond },
      { SkillIdEnum.SiWang_ChanRao, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.ShengLong_LieYan, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.RongYan_ChongJi, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.JuLangZhi_Shi, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.DaPeng_ZhanChi, AttackFlowEnum.Pre_Proc },
      { SkillIdEnum.Re_Lang, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.HuanYingZhi_Ji, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.YanShiZhi_Quan, AttackFlowEnum.Calc_Dmg },
      { SkillIdEnum.ZhiYanZhi_Xi, AttackFlowEnum.Check_Pre_Cond },
      // 以下为BOSS技能注入
      { SkillIdEnum.WuYing_Jiao, AttackFlowEnum.Pre_Proc },
      { SkillIdEnum.Boss_AnYin_BaoZha, AttackFlowEnum.Check_Pre_Cond },
      { SkillIdEnum.Boss_SiWang_ChanRao, AttackFlowEnum.Calc_Dmg },
    };
    public static HashSet<AttackState> TargetStateInjects { get; set; } = new()
    {
      AttackState.Chui_Fei, AttackState.FROZEN, AttackState.KongShouRu_BaiRen, AttackState.KongShouRu_BaiRen2,AttackState.MoFa_Dun, AttackState.LieYan_ZhuoShao, AttackState.HuoYan_PinZhang, AttackState.TongQiang_TieBi, AttackState.Jie_Jing, AttackState.DaPeng_ZhanChi, AttackState.HaiYao_ZhiGe
    };
  }

  public struct SelfStateInjectInfo(AttackState state, AttackCtxComp attackCtx, AttackFlowNode node)
  {
    public AttackState State { get; set; } = state;
    public AttackCtxComp AttackCtx { get; set; } = attackCtx;
    public AttackFlowNode Node { get; set; } = node;
  }

  public struct TargetStateInjectInfo(AttackState state, AttackCtxComp attackCtx, AttackFlowNode node)
  {
    public AttackState State { get; set; } = state;
    public AttackCtxComp AttackCtx { get; set; } = attackCtx;
    public AttackFlowNode Node { get; set; } = node;
  }

  public struct SkillInjectInfo(Skill skill, AttackCtxComp attackCtx, AttackFlowNode node)
  {
    public Skill Skill { get; set; } = skill;
    public AttackCtxComp AttackCtx { get; set; } = attackCtx;
    public AttackFlowNode Node { get; set; } = node;
  }
}