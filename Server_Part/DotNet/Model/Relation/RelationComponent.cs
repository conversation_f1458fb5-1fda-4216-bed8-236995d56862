using System.Collections.Concurrent;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;

namespace Mao<PERSON>ouJi
{
  public partial class RelationComponent : Entity, IAwake, ILoad, ISerializeToEntity
  {
    [BsonIgnore]
    [MemoryPackIgnore]
    public ConcurrentDictionary<long, EntityRef<UserFriendInfo>> UserFriendInfoMap { get; set; } = new(); // 用户好友信息

    [BsonIgnore]
    [MemoryPackIgnore]
    public EntityRef<UserFriendInfo> UserLoveInfo { get; set; } // 用户情侣信息

    [BsonIgnore]
    [MemoryPackIgnore]
    public EntityRef<UserFriendInfo> UserTeacherInfo { get; set; } // 用户师傅信息

    [BsonIgnore]
    [MemoryPackIgnore]
    public ConcurrentDictionary<long, EntityRef<UserFriendInfo>> UserStudentInfo { get; set; } = new(); // 用户徒弟信息
  }
}