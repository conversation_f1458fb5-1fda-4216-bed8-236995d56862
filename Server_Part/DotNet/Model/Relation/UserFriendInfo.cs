using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [ChildOf(typeof(RelationManageComponent))]
  public class UserFriendInfo : Entity, IAwake, IAwake<AddFriendInfo>, IDestroy
  {
    public long SrcUserId { get; set; } // 用户ID，只在师徒关系中有意义，这个位置只能放徒弟ID
    public long TargetUserId { get; set; } // 目标用户ID，只在师徒关系中使用, 这个位置只能放师傅ID
    public FriendTypeEnum FriendType { get; set; } // 好友类型
    public long CreateTime { get; set; } // 添加时间

    [BsonIgnore]
    [MemoryPackIgnore]
    public EntityRef<User> SrcUser { get; set; }
    [BsonIgnore]
    [MemoryPackIgnore]
    public EntityRef<User> TargetUser { get; set; }
  }
}