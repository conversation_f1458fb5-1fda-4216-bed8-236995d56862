using MongoDB.Bson.Serialization.Attributes;
using NLog.Fluent;

namespace MaoYouJi
{
  [EnableClass]
  public class MonsterConf
  {
    // 特殊怪物的尸体保存时间
    // public long specialMonBodyTime = TimeUtil.getDayMills(30);
    // 怪物相关参数
    public long bodyTime = 3 * 1000; // 尸体时间
    public long freshTime = 2 * 1000; // 刷新时间
  }

  [EnableClass]
  public class AttackConf
  {
    // 战斗线程的缓存最大值
    public int attackCacheNum = 2000;
  }
  [EnableClass]
  public class TaskConf
  {
    public int maxTaskNum = 20; // 最大进行中任务数量
  }
  [EnableClass]
  public class UserConf
  {
    // 最大同时在线人数
    public int maxOnlineNum = 2500;
  }
  [EnableClass]
  public class VipConf
  {
    // 支付回调地址
    public string payCallbackUrl = "http://***************";
    // 猫豆兑换比例，{充值猫豆数量，100个猫豆对应的金钱数量}
    // public long[][] catBeanExchangeRate = new long[][] { new Lo{ 10, 98 }, { 1000, 10000 }, { 10000, 100000 } };
  }
  [EnableClass]
  public class SystemConf
  {
    // 灰度日志服务器名称
    public string graylogHostName = "新梦长空";
    // 日志等级
    public int logLevel = 2;
    // 最低客户端版本
    public long minClientVersion = 0;
  }

  [EnableClass]
  public class Config
  {
    [BsonId]
    public long Id;
    public MonsterConf monConf = new MonsterConf();
    public AttackConf attackConf = new AttackConf();
    public TaskConf taskConf = new TaskConf();
    public UserConf userConf = new UserConf();
    public VipConf payConf = new VipConf();
    public SystemConf systemConf = new SystemConf();
    public DaTaoShaActConf daTaoShaConf = new DaTaoShaActConf();
  }
}
