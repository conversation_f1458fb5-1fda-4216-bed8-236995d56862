using System.Threading;

namespace MaoYouJi
{
  [EnableClass]
  public class ThreadParams
  {
    public EntityRef<AttackCtxComp> AttackCtx { get; set; }
  }

  public static class ThreadHelper
  {
    // 定义一个 ThreadLocal 变量
    [StaticField]
    private static ThreadLocal<ThreadParams> threadVariable = new();
    public static AttackCtxComp GetAttackCtx()
    {
      threadVariable.Value ??= new ThreadParams();
      return threadVariable.Value.AttackCtx;
    }

    public static void SetAttackCtx(AttackCtxComp attackCtx)
    {
      threadVariable.Value ??= new ThreadParams();
      threadVariable.Value.AttackCtx = attackCtx;
      if (attackCtx != null)
      {
        string attackId = attackCtx.GetParent<AttackInCache>().Id.ToString();
        MaoAsyncLocal.AttackId = attackId.Substring(attackId.Length - 8, 8);
      }
      else
      {
        MaoAsyncLocal.AttackId = null;
      }
    }
  }
}