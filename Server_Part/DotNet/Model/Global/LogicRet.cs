using MemoryPack;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class LogicRet(bool isSuccess, string message)
  {
    [StaticField]
    public static LogicRet Success = new(true, null);
    public static LogicRet Failed(string message)
    {
      return new(false, message);
    }
    public bool IsSuccess { get; set; } = isSuccess;
    public string Message { get; set; } = message;
  }
}