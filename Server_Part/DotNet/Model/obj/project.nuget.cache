{"version": 2, "dgSpecHash": "RvcIv4XhdO8=", "success": true, "projectFilePath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Model/DotNet.Model.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/commandlineparser/2.8.0/commandlineparser.2.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/concurrenthashset/1.3.0/concurrenthashset.1.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/dnsclient/1.6.1/dnsclient.1.6.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/epplus/5.8.8/epplus.5.8.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/memorypack/1.10.0/memorypack.1.10.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/memorypack.core/1.10.0/memorypack.core.1.10.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/memorypack.generator/1.10.0/memorypack.generator.1.10.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.analyzers/3.3.2/microsoft.codeanalysis.analyzers.3.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.common/4.0.1/microsoft.codeanalysis.common.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.csharp/4.0.1/microsoft.codeanalysis.csharp.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/8.0.0/microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.memory/8.0.0/microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/5.0.0/microsoft.extensions.configuration.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/5.0.0/microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.fileextensions/5.0.0/microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.json/5.0.0/microsoft.extensions.configuration.json.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.0/microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/5.0.0/microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.physical/5.0.0/microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.filesystemglobbing/5.0.0/microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.0/microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/8.0.0/microsoft.extensions.options.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/8.0.0/microsoft.extensions.primitives.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.io.recyclablememorystream/1.4.1/microsoft.io.recyclablememorystream.1.4.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/5.0.0/microsoft.netcore.platforms.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.registry/5.0.0/microsoft.win32.registry.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.systemevents/5.0.0/microsoft.win32.systemevents.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mongodb.bson/2.17.1/mongodb.bson.2.17.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/mongodb.driver/2.17.1/mongodb.driver.2.17.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/mongodb.driver.core/2.17.1/mongodb.driver.core.2.17.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/mongodb.libmongocrypt/1.5.5/mongodb.libmongocrypt.1.5.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/nlog/4.7.15/nlog.4.7.15.nupkg.sha512", "/Users/<USER>/.nuget/packages/quartz/3.14.0/quartz.3.14.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/sharpcompress/0.30.1/sharpcompress.0.30.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/sharpziplib/1.3.3/sharpziplib.1.3.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.buffers/4.5.1/system.buffers.4.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.immutable/5.0.0/system.collections.immutable.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.componentmodel.annotations/5.0.0/system.componentmodel.annotations.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.drawing.common/5.0.0/system.drawing.common.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.formats.asn1/5.0.0/system.formats.asn1.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.4/system.memory.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.metadata/5.0.0/system.reflection.metadata.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/5.0.0/system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.accesscontrol/5.0.0/system.security.accesscontrol.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.cng/5.0.0/system.security.cryptography.cng.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.pkcs/5.0.0/system.security.cryptography.pkcs.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.principal.windows/5.0.0/system.security.principal.windows.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.codepages/5.0.0/system.text.encoding.codepages.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.5.4/system.threading.tasks.extensions.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.14/microsoft.netcore.app.ref.8.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/8.0.14/microsoft.aspnetcore.app.ref.8.0.14.nupkg.sha512"], "logs": []}