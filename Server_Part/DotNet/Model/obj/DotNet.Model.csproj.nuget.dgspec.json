{"format": 1, "restore": {"/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Model/DotNet.Model.csproj": {}}, "projects": {"/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Core/DotNet.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Core/DotNet.Core.csproj", "projectName": "Core", "projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Core/DotNet.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/ThirdParty/DotNet.ThirdParty.csproj": {"projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/ThirdParty/DotNet.ThirdParty.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.14, 8.0.14]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.202/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Model/DotNet.Model.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Model/DotNet.Model.csproj", "projectName": "Model", "projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Model/DotNet.Model.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Model/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Core/DotNet.Core.csproj": {"projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/Core/DotNet.Core.csproj"}, "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/ThirdParty/DotNet.ThirdParty.csproj": {"projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/ThirdParty/DotNet.ThirdParty.csproj"}, "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Analyzer/Share.Analyzer.csproj": {"projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Analyzer/Share.Analyzer.csproj"}, "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Share.SourceGenerator/Share.SourceGenerator.csproj": {"projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Share.SourceGenerator/Share.SourceGenerator.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConcurrentHashSet": {"target": "Package", "version": "[1.3.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[8.0.0, )"}, "Quartz": {"target": "Package", "version": "[3.14.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.14, 8.0.14]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.202/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/ThirdParty/DotNet.ThirdParty.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/ThirdParty/DotNet.ThirdParty.csproj", "projectName": "ThirdParty", "projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/ThirdParty/DotNet.ThirdParty.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/DotNet/ThirdParty/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "noWarn": ["NU1903"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CommandLineParser": {"target": "Package", "version": "[2.8.0, )"}, "EPPlus": {"target": "Package", "version": "[5.8.8, )"}, "MemoryPack": {"target": "Package", "version": "[1.10.0, )"}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[4.0.1, )"}, "Microsoft.CodeAnalysis.Common": {"target": "Package", "version": "[4.0.1, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.17.1, )"}, "NLog": {"target": "Package", "version": "[4.7.15, )"}, "SharpZipLib": {"target": "Package", "version": "[1.3.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.14, 8.0.14]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.202/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Analyzer/Share.Analyzer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Analyzer/Share.Analyzer.csproj", "projectName": "Share.Analyzer", "projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Analyzer/Share.Analyzer.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Analyzer/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.CodeAnalysis.CSharp.Workspaces": {"suppressParent": "All", "target": "Package", "version": "[4.0.1, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.202/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Share.SourceGenerator/Share.SourceGenerator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Share.SourceGenerator/Share.SourceGenerator.csproj", "projectName": "Share.SourceGenerator", "projectPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Share.SourceGenerator/Share.SourceGenerator.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/work/unitytest/ET_Back/My_ET_Back/Server_Part/Share/Share.SourceGenerator/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.3.3, )"}, "Microsoft.CodeAnalysis.CSharp": {"suppressParent": "All", "target": "Package", "version": "[3.9.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.202/RuntimeIdentifierGraph.json"}}}}}