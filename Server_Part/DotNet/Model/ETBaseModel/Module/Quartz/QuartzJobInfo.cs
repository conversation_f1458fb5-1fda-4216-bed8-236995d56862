using System;
using System.Threading.Tasks;
using Quartz;

namespace Mao<PERSON>ouJ<PERSON>
{
  public enum TimerJobGroupEnum
  {
    GLOBAL_GROUP = 1,
    ACT_GROUP = 2,
    MAP_GROUP = 3,
    NPC_GROUP = 4,
    MONSTER_GROUP = 5,
    Auto_Move_GROUP = 6,
    ATTACK_GROUP = 7,
    USER_GROUP = 8,
    VIP_GROUP = 9,
  }

  public struct MaoScheduleJobInfo
  {
    public MaoScheduleJobInfo(ActorId actorId, MaoYouMessage message)
    {
      this.ActorId = actorId;
      this.Message = message;
    }

    public MaoScheduleJobInfo(FightInfo fightInfo, MaoYouMessage message)
    {
      this.FightInfo = fightInfo;
      this.Message = message;
    }

    public ActorId ActorId { get; set; }
    public MaoYouMessage Message { get; set; }
    public FightInfo FightInfo { get; set; } = null;
  }

  [EnableClass]
  public class MaoScheduleJob : IJob
  {
    public Task Execute(IJobExecutionContext context)
    {
      MaoScheduleJobInfo jobInfo = (MaoScheduleJobInfo)context.JobDetail.JobDataMap.Get("message");
      EventSystem.Instance.Invoke(jobInfo);
      return Task.CompletedTask;
    }
  }
}