﻿using System.Collections.Generic;
using System.ComponentModel;
using System.Net;

namespace MaoYouJi
{
  public partial class StartSceneConfigCategory
  {
    public MultiMap<int, StartSceneConfig> Gates = new();

    public MultiMap<int, StartSceneConfig> ProcessScenes = new();

    public Dictionary<long, Dictionary<string, StartSceneConfig>> ClientScenesByName = new();

    public StartSceneConfig LocationConfig = null;

    public StartSceneConfig SocialConfig = null;

    public List<StartSceneConfig> Realms = new();

    public List<StartSceneConfig> Routers = new();

    public List<StartSceneConfig> Maps = new();

    public StartSceneConfig Match;

    public StartSceneConfig Benchmark;

    public List<StartSceneConfig> GetByProcess(int process)
    {
      return this.ProcessScenes[process];
    }

    public StartSceneConfig GetSceneConfigByType(int zone, SceneType type = SceneType.Social)
    {
      if (type == SceneType.Social)
      {
        if (this.SocialConfig != null)
        {
          return this.SocialConfig;
        }
      }
      Dictionary<string, StartSceneConfig> socialSceneConfigs = this.ClientScenesByName[zone];
      if (socialSceneConfigs.Count == 0)
      {
        return null;
      }
      StartSceneConfig sceneConfig = null;
      foreach (StartSceneConfig socialSceneConfig in socialSceneConfigs.Values)
      {
        if (socialSceneConfig.Type == type)
        {
          sceneConfig = socialSceneConfig;
        }
      }
      if (sceneConfig != null)
      {
        if (type == SceneType.Social)
        {
          this.SocialConfig = sceneConfig;
        }
      }
      return sceneConfig;
    }

    public StartSceneConfig GetBySceneName(int zone, string name)
    {
      return this.ClientScenesByName[zone][name];
    }

    public override void EndInit()
    {
      foreach (StartSceneConfig startSceneConfig in this.GetAll().Values)
      {
        this.ProcessScenes.Add(startSceneConfig.Process, startSceneConfig);

        if (!this.ClientScenesByName.ContainsKey(startSceneConfig.Zone))
        {
          this.ClientScenesByName.Add(startSceneConfig.Zone, new Dictionary<string, StartSceneConfig>());
        }
        this.ClientScenesByName[startSceneConfig.Zone].Add(startSceneConfig.Name, startSceneConfig);

        switch (startSceneConfig.Type)
        {
          case SceneType.Realm:
            this.Realms.Add(startSceneConfig);
            break;
          case SceneType.Gate:
            this.Gates.Add(startSceneConfig.Zone, startSceneConfig);
            break;
          case SceneType.Location:
            this.LocationConfig = startSceneConfig;
            break;
          case SceneType.Router:
            this.Routers.Add(startSceneConfig);
            break;
          case SceneType.Map:
            this.Maps.Add(startSceneConfig);
            break;
          case SceneType.Match:
            this.Match = startSceneConfig;
            break;
          case SceneType.BenchmarkServer:
            this.Benchmark = startSceneConfig;
            break;
        }
      }
    }
  }

  public partial class StartSceneConfig : ISupportInitialize
  {
    public ActorId ActorId;

    public SceneType Type;

    public StartProcessConfig StartProcessConfig
    {
      get
      {
        return StartProcessConfigCategory.Instance.Get(this.Process);
      }
    }

    public StartZoneConfig StartZoneConfig
    {
      get
      {
        return StartZoneConfigCategory.Instance.Get(this.Zone);
      }
    }

    // 内网地址外网端口，通过防火墙映射端口过来
    private IPEndPoint innerIPPort;

    public IPEndPoint InnerIPPort
    {
      get
      {
        if (this.innerIPPort == null)
        {
          this.innerIPPort = NetworkHelper.ToIPEndPoint($"{this.StartProcessConfig.InnerIP}:{this.Port}");
        }

        return this.innerIPPort;
      }
    }

    private IPEndPoint outerIPPort;

    // 外网地址外网端口
    public IPEndPoint OuterIPPort
    {
      get
      {
        if (this.outerIPPort == null)
        {
          this.outerIPPort = NetworkHelper.ToIPEndPoint($"{this.StartProcessConfig.OuterIP}:{this.Port}");
        }

        return this.outerIPPort;
      }
    }

    public override void EndInit()
    {
      this.ActorId = new ActorId(this.Process, this.Id, 1);
      this.Type = EnumHelper.FromString<SceneType>(this.SceneType);
    }
  }
}