using System.Collections.Generic;
using MongoDB.Driver;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(GlobalManageComp))]
  [FriendOf(typeof(GlobalManageComp))]
  public static partial class GlobalManageCompSys
  {
    [EntitySystem]
    private static void Awake(this GlobalManageComp self)
    {
      ETLog.Info($"初始化全局数据，Zone: {self.Zone()}");
      self.Init().Coroutine();
    }

    private static async ETTask Init(this GlobalManageComp self)
    {
      Config config = await self.LoadConfig();
      self.Root().AddComponent<DaTaoShaActComp, DaTaoShaActConf>(config.daTaoShaConf);
    }

    public static async ETTask<Config> LoadConfig(this GlobalManageComp self)
    {
      DBManagerComponent dbManagerComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>();
      DBComponent dbComponent = dbManagerComponent.GetMyZoneDB();
      FilterDefinitionBuilder<Config> filter = new();
      Config config = await dbComponent.QueryClass<Config>(filter.Eq(n => n.Id, 8888));
      GlobalInfoCache.Instance.Config = config;
      return config;
    }
  }

}
