using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using MongoDB.Bson;
using MongoDB.Driver;
using System;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(FiberUsersComponent))]
  [FriendOf(typeof(FiberUsersComponent))]
  [FriendOf(typeof(User))]
  [FriendOf(typeof(AttackComponent))]
  public static partial class FiberUsersComponentSystem
  {
    [EntitySystem]
    private static void Awake(this FiberUsersComponent self)
    {
    }

    public static void AddUser(this FiberUsersComponent self, User user, bool addToGlobalCache = false)
    {
      self.Users.Add(user.Id, user);
      if (addToGlobalCache)
      {
        GlobalInfoCache.Instance.AddOnlineUser(user.Id, user);
      }
    }

    public static void RemoveUser(this FiberUsersComponent self, long userId, bool removeFromGlobalCache = false)
    {
      self.Users.Remove(userId);
      if (removeFromGlobalCache)
      {
        GlobalInfoCache.Instance.RemoveOnlineUser(userId);
      }
    }

    public static User GetUser(this FiberUsersComponent self, long userId, bool getFromGlobalCache = false)
    {
      if (getFromGlobalCache)
      {
        return GlobalInfoCache.Instance.GetOnlineUser(userId);
      }
      self.Users.TryGetValue(userId, out EntityRef<User> user);
      return user;
    }

    public static async ETTask<User> GetUserFromDb(this FiberUsersComponent self, long userId)
    {
      DBComponent dBComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      User user = await dBComponent.Query<User>(userId);
      return user;
    }

    public static async ETTask<ShowSimpleUser> GetSimpleUserById(this FiberUsersComponent self, long userId)
    {
      var simpleUser = await self.GetSimpleUserByIds(userId);
      if (simpleUser.TryGetValue(userId, out ShowSimpleUser user))
      {
        return user;
      }
      return null;
    }

    public static async ETTask<Dictionary<long, ShowSimpleUser>> GetSimpleUserByIds(this FiberUsersComponent self, params long[] userIds)
    {
      Dictionary<long, ShowSimpleUser> retUsers = new();
      if (userIds == null || userIds.Length == 0)
      {
        return retUsers;
      }
      var pipeline = new[]{
          new BsonDocument("$match", new BsonDocument("Id", new BsonDocument("$in", new BsonArray(userIds)))),
          new BsonDocument("$project", new BsonDocument
          {
              { "Id", 1 },
              { "nickname", 1 },
              { "lastLogInTime", 1 },
              { "offlineState", 1 },
              { "attackComponent", new BsonDocument("$arrayElemAt", new BsonArray
                  {
                      new BsonDocument("$filter", new BsonDocument
                      {
                          { "input", "$C" },
                          { "as", "comp" },
                          { "cond", new BsonDocument("$eq", new BsonArray { "$$comp._t", "AttackComponent" }) }
                      }),
                      0
                  })
              }
          })
      };
      var collection = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<User>();
      var result = await collection.AggregateAsync<BsonDocument>(pipeline);

      var dict = new Dictionary<long, ShowSimpleUser>();
      await result.ForEachAsync(doc =>
      {
        var showSimpleUser = new ShowSimpleUser();
        showSimpleUser.id = doc["Id"].AsInt64;
        showSimpleUser.name = doc.GetValue("nickname", "").AsString;
        showSimpleUser.lastLogInTime = doc.GetValue("lastLogInTime", 0).ToInt64();
        // offlineState 字符串转枚举
        var offlineStateStr = doc.GetValue("offlineState", "OFFLINE").AsString;
        showSimpleUser.offlineState = (OfflineStateEnum)Enum.Parse(typeof(OfflineStateEnum), offlineStateStr);
        var attackComponent = doc.GetValue("attackComponent", null) as BsonDocument;
        if (attackComponent != null)
        {
          showSimpleUser.level = attackComponent.GetValue("level", 0).ToInt32();
          // NowSkin 字符串转枚举
          var nowSkinStr = attackComponent.GetValue("NowSkin", "default_0").AsString;
          showSimpleUser.nowSkin = (SkinIdEnum)Enum.Parse(typeof(SkinIdEnum), nowSkinStr);
        }

        dict[showSimpleUser.id] = showSimpleUser;
      });
      return dict;
    }

    public static async ETTask<User> GetOnlineUserBuyIdOrName(this FiberUsersComponent self, string targetUserId)
    {
      var collection = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<User>();
      if (long.TryParse(targetUserId, out long userId))
      {
        var filter = Builders<User>.Filter.Eq(u => u.Id, userId);
        var projection = Builders<User>.Projection.Include(u => u.Id);
        var doc = await collection.Find(filter).Project(projection).FirstOrDefaultAsync();
        if (doc != null)
        {
          long id = doc["Id"].AsInt64;
          return GlobalInfoCache.Instance.GetOnlineUser(id);
        }
      }
      else
      {
        var filter = Builders<User>.Filter.Eq(u => u.nickname, targetUserId);
        var projection = Builders<User>.Projection.Include(u => u.Id);
        var doc = await collection.Find(filter).Project(projection).FirstOrDefaultAsync();
        if (doc != null)
        {
          long id = doc["Id"].AsInt64;
          return GlobalInfoCache.Instance.GetOnlineUser(id);
        }
      }
      return null;
    }

    public static bool CheckNickName(this FiberUsersComponent self, string nickname, out string msg)
    {
      DBComponent dBComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      IMongoCollection<User> userCollection = dBComponent.GetCollection<User>();
      if (string.IsNullOrEmpty(nickname))
      {
        msg = "昵称不能为空";
        return false;
      }
      if (nickname.Length > 7)
      {
        msg = "昵称不能超过7个字符";
        return false;
      }

      // 检查特殊字符
      string specialCharsPattern = @"[`~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】'；：""'。，、？]";
      if (Regex.IsMatch(nickname, specialCharsPattern))
      {
        msg = "昵称不能包含特殊字符";
        return false;
      }

      // 检查空白字符
      if (nickname.Contains(" ") || nickname.Contains("\t") || nickname.Contains("\n") || nickname.Contains("\r"))
      {
        msg = "昵称不能包含空格等空白字符";
        return false;
      }

      // 检查是否全是数字
      if (Regex.IsMatch(nickname, @"^[0-9]+$"))
      {
        msg = "昵称不能全为数字";
        return false;
      }

      // 敏感词检查
      if (nickname.Contains("管理") || nickname.Contains("客服") || nickname.Contains("官方"))
      {
        msg = "昵称不能包含管理、客服、官方等敏感词";
        return false;
      }
      if (nickname.Contains("测试"))
      {
        msg = "昵称不能包含测试";
        return false;
      }

      // 检查数据库是否已存在
      var filter = Builders<User>.Filter.Eq(u => u.nickname, nickname);
      bool exists = userCollection.Find(filter).Any();
      if (exists)
      {
        msg = "昵称已存在";
        return false;
      }

      msg = "昵称可用";
      return true;
    }

    public static async ETTask<User> CreateUser(this FiberUsersComponent self, long netAccountId, string nickname, BaseJob job, SkinIdEnum defaultSkin)
    {
      User user = self.AddChild<User>();
      user.netAccountId = netAccountId;
      user.nickname = nickname;
      user.userType = UserType.NORMAL;
      user.AddComponent<MoveComponent, string, string, int>("幼稚园", "休息室", 1);
      user.skinList.Add(defaultSkin);
      AttackComponent attackComponent = user.AddComponent<AttackComponent, FightInfo>(new FightInfo()
      {
        fightId = user.Id,
        fightName = nickname,
        monBaseType = MonBaseType.None,
        liveType = LiveType.ROLE,
      });
      attackComponent.job = job;
      attackComponent.level = 1;
      attackComponent.NowSkin = defaultSkin;
      attackComponent.AddComponent<SkillComponent>(); // 技能组件
      user.AddComponent<EquipComponent>(); // 装备组件
      user.AddComponent<RelationComponent>(); // 关系组件
      user.AddComponent<TaskComponent>(); // 任务组件
      user.AddComponent<BagComponent, BagType, int>(BagType.Bag, 100); // 背包组件
      user.AddComponent<UserGetInfoCnt>(); // 获取信息组件
      user.AddComponent<UserDailyCntInfo>(); // 每日信息组件
      user.AddComponent<UserVipFuncInfo>(); // 会员功能组件
      user.RecalcUserAttrs();
      attackComponent.blood = attackComponent.maxBlood;
      attackComponent.blue = attackComponent.maxBlue;

      ETLog.Info($"创建用户: {user.Id} {user.netAccountId} {user.nickname} {job} {defaultSkin}");

      user.BeginInit();
      // 攻击组件
      DBComponent dBComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      await dBComponent.Insert(user.Id, user);
      return user;
    }

    public static User TransferUser(this FiberUsersComponent self, long userId, Fiber targetFiber)
    {
      User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
      if (user == null)
      {
        ETLog.Error($"用户不存在: {userId} {self.Fiber().Id}");
        return null;
      }
      return self.TransferUser(user, targetFiber);
    }

    // 在不同地图之间传送用户时也移除MailBoxComponent
    public static User TransferUser(this FiberUsersComponent self, User user, Fiber targetFiber)
    {
      Fiber parentFiber = self.Fiber();
      int preFiberId = parentFiber.Id;
      int nowFiberId = user.Fiber().Id;
      if (nowFiberId != preFiberId)
      {
        ETLog.Error($"用户已转移: {user.Id} {user.InstanceId} {nowFiberId} {preFiberId}");
        return null;
      }
      ETLog.Info($"转移用户: {user.Id} {user.InstanceId} {nowFiberId} {targetFiber.Id}");
      Scene targetScene = targetFiber.Root;
      self.RemoveUser(user.Id);
      targetScene.GetComponent<FiberUsersComponent>().AddUser(user);
      // MailBoxComponent mailBoxComponent = user.GetComponent<MailBoxComponent>();
      // if (mailBoxComponent == null)
      // {
      //   ETLog.Error($"用户没有MailBoxComponent: {user.Id} {user.InstanceId}");
      //   return null;
      // }
      // targetFiber.Mailboxes.Add(mailBoxComponent);
      // parentFiber.Mailboxes.Remove(user.InstanceId);
      return user;
    }

    // // 将地图邮箱赋值到战斗邮箱中
    // public static bool AddMailBox(this FiberUsersComponent self, User user, Fiber targetFiber)
    // {
    //   if (user.Fiber().Id != self.Fiber().Id)
    //   {
    //     ETLog.Error($"用户不在当前地图: {user.Id} {user.InstanceId} {user.Fiber().Id} {self.Fiber().Id}");
    //     return false;
    //   }
    //   if (targetFiber.Root.SceneType != SceneType.Attack)
    //   {
    //     ETLog.Error($"目标地图不是战斗地图: {user.Id} {user.InstanceId} {targetFiber.Id} {targetFiber.Root.SceneType}");
    //     return false;
    //   }
    //   MailBoxComponent mailBoxComponent = user.GetComponent<MailBoxComponent>();
    //   if (mailBoxComponent == null)
    //   {
    //     ETLog.Error($"用户没有MailBoxComponent: {user.Id} {user.InstanceId}");
    //     return false;
    //   }
    //   targetFiber.Mailboxes.Add(mailBoxComponent);
    //   ETLog.Info($"添加用户邮箱: {user.Id} {user.InstanceId} {user.Fiber().Id} {targetFiber.Id} {targetFiber.Mailboxes.Count}");
    //   return true;
    // }

    // // 战斗结束后，从战斗纤程中移除用户邮箱
    // public static bool RemoveMailBox(this FiberUsersComponent self, User user)
    // {
    //   MailBoxComponent mailBoxComponent = user.GetComponent<MailBoxComponent>();
    //   if (mailBoxComponent == null)
    //   {
    //     ETLog.Error($"用户没有MailBoxComponent: {user.Id} {user.InstanceId}");
    //     return false;
    //   }
    //   Fiber fiber = self.Fiber();
    //   if (fiber.Root.SceneType != SceneType.Attack)
    //   {
    //     ETLog.Error($"用户邮箱不在战斗地图: {user.Id} {user.InstanceId} {fiber.Root.SceneType}");
    //     return false;
    //   }
    //   fiber.Mailboxes.Remove(user.InstanceId);
    //   ETLog.Info($"移除用户邮箱: {user.Id} {user.InstanceId} {fiber.Id} {fiber.Mailboxes.Count}");
    //   return true;
    // }
  }
}