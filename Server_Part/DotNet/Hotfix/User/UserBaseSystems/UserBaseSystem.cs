using System;

namespace MaoYouJi
{
  [FriendOf(typeof(User))]
  [FriendOf(typeof(UserActorComponent))]
  public static partial class UserBaseSystem
  {
    // 是否在挂机系统中
    public static bool IsInVfxSystem(this User self)
    {
      // return self.HasUserState(UserStateEnum.Vfx_State);
      return false;
    }

    public static void AddUserStatus(this User self, UserStateDetail userStateDetail)
    {
      self.GetComponent<UserStateComp>().AddChildWithId<OneUserStateComp, UserStateDetail>((long)userStateDetail.userState, userStateDetail);
    }

    public static void RemoveUserStatus(this User self, UserStateEnum userState)
    {
      self.GetComponent<UserStateComp>().RemoveChild((long)userState);
    }

    /**
    * 增加或减少玩家的罪恶值
    * 
    * @param queryUserId 用户id
    * @param addNum      增加的罪恶值
    */
    public static void AddEvilNum(this User user, int addNum)
    {
      int oldEvilNum = user.evilNum;
      user.evilNum += addNum;
      if (user.evilNum < 0)
      {
        user.evilNum = 0;
      }
      if (user.evilNum > 9999)
      {
        user.evilNum = 9999;
      }
      bool evilStateChange = false;
      if (oldEvilNum < 100 && user.evilNum >= 100 && !user.HasUserState(UserStateEnum.Evil_State))
      {
        UserStateDetail userStateDetail = new UserStateDetail();
        userStateDetail.userState = UserStateEnum.Evil_State;
        user.AddUserStatus(userStateDetail);
        evilStateChange = true;
      }
      if (oldEvilNum >= 100 && user.evilNum < 100 && user.HasUserState(UserStateEnum.Evil_State))
      {
        user.RemoveUserStatus(UserStateEnum.Evil_State);
        evilStateChange = true;
      }
      if (Math.Abs(addNum) > 1)
      {
        ETLog.Info($"addEvilNum: {user.Id}, {addNum}, {user.evilNum}, {evilStateChange}");
      }
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      user.SendChat($"您的罪恶值{(addNum > 0 ? "增加" : "减少")}{Math.Abs(addNum)}点");
      if (evilStateChange)
      {
        if (user.evilNum >= 100)
        {
          ChatProSystem.SendMessageToAllUser(new ServerSendChatMsg
          {
            content = $"<u>{user.nickname}</u>在<u>{moveComponent.nowMap}/{moveComponent.nowPoint}</u>过度杀戮，罪恶深重，已红名，请全服的义士们前往讨伐！维护公平和正义！",
            chatType = ChatType.World_Chat,
          });
          user.SendToast("您的罪恶值过多，已红名");
          user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.User_State));
        }
        else
        {
          user.SendToast("您的红名状态已消失！");
          user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.User_State));
        }
        MapNode mapNode = user.GetParent<MapNode>();
        mapNode.PutUserInMapNode(user);
      }
    }
  }
}
