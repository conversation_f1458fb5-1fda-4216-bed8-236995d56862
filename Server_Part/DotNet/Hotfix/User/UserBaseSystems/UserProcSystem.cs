namespace MaoYouJi
{
  public static class UserProcSystem
  {
    public static LogicRet GetUserWithCheck(long userId, out User user, bool checkAlive = false)
    {
      user = GlobalInfoCache.Instance.GetOnlineUser(userId);
      if (user == null)
      {
        ETLog.Warning($"用户不存在: {userId}");
        return LogicRet.Failed("用户不存在");
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (checkAlive)
      {
        if (attackComponent.LiveState != LiveStateEnum.ALIVE)
        {
          return LogicRet.Failed("当前状态无法进行该操作");
        }
      }
      return LogicRet.Success;
    }

    public static ActorId GetParentActorId(FightInfo fightInfo)
    {
      if (fightInfo.liveType == LiveType.ROLE)
      {
        User user = GlobalInfoCache.Instance.GetOnlineUser(fightInfo.fightId);
        if (user == null)
        {
          return default;
        }
        return user.GetParent<MapNode>().GetActorId();
      }
      else if (fightInfo.liveType == LiveType.MONSTER)
      {
        MonsterInfo monsterInfo = GlobalInfoCache.Instance.GetMonsterInfo(fightInfo.fightId);
        if (monsterInfo == null)
        {
          return default;
        }
        return monsterInfo.GetParent<MapNode>().GetActorId();
      }
      else if (fightInfo.liveType == LiveType.NPC)
      {
        NpcInfo npcInfo = GlobalInfoCache.Instance.GetNpcInfo(fightInfo.fightId);
        if (npcInfo == null)
        {
          return default;
        }
        return npcInfo.GetParent<MapNode>().GetActorId();
      }
      ETLog.Error($"战斗类型错误: {fightInfo.liveType}");
      return default;
    }
  }
}
