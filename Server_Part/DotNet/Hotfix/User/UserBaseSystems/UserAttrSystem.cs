using System;

namespace MaoYouJi
{
  [FriendOf(typeof(User))]
  [FriendOf(typeof(UserActorComponent))]
  public static partial class UserAttrSystem
  {
    /**
    * 获取职业技能加成, 同时只有一个职业技能能生效
    * 
    * @param user       用户
    * @param baseAttack 基础攻击
    */
    private static void GetBaseJobSkillAdd(this User self, BaseAttack baseAttack)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      Skill targetSkill = skillComponent.GetNowBaseJobSkill();
      if (targetSkill == null)
        return;
      long addNum = targetSkill.damage;
      if (targetSkill.skillId == SkillIdEnum.LongKui_BiRen)
      {
        baseAttack.hitRate += addNum;
      }
      else if (targetSkill.skillId == SkillIdEnum.BaWan_Qiang)
      {
        baseAttack.minAttack += addNum;
        baseAttack.maxAttack += addNum;
      }
      else if (targetSkill.skillId == SkillIdEnum.ShiZi_Jian)
      {
        baseAttack.crit += addNum;
      }
      else if (targetSkill.skillId == SkillIdEnum.JuFeng_DaoFa)
      {
        baseAttack.attackRate = (long)(baseAttack.attackRate * 1000 / (double)(addNum + 1000));
      }
      else if (targetSkill.skillId == SkillIdEnum.YouMin_Zhua)
      {
        baseAttack.critDmg += addNum;
      }
      else if (targetSkill.skillId == SkillIdEnum.Sheng_MoFa)
      {
        baseAttack.maxBlue += addNum;
      }
      else if (targetSkill.skillId == SkillIdEnum.FengXi_FaShu)
      {
        baseAttack.defense += addNum;
      }
      else if (targetSkill.skillId == SkillIdEnum.HuoXi_FaShu)
      {
        baseAttack.critDmg += addNum;
      }
      else if (targetSkill.skillId == SkillIdEnum.An_MoFa)
      {
        baseAttack.maxBlood += addNum;
      }
      else if (targetSkill.skillId == SkillIdEnum.BinXi_FaShu)
      {
        baseAttack.crit += addNum;
      }
    }

    public static void GetTalentSkillAdd(this User self, BaseAttack baseAttack)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      Skill talentSkill = skillComponent.GetNowTalentSkill(); ;
      if (talentSkill == null)
        return;
      if (talentSkill.skillId == SkillIdEnum.JinLi_ChongPei)
      { // 默认被动
        baseAttack.mind = (long)(baseAttack.mind * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.ShengLongZhi_Xue)
      { // 巴哈姆特被动
        baseAttack.power = (long)(baseAttack.power * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.Jian_Ren)
      { // 白色宠物蛋被动
        baseAttack.strength = (long)(baseAttack.strength * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.WuBian_DaDi)
      { // 地元素被动
        baseAttack.strength = (long)(baseAttack.strength * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.XunJie_ZhiFeng)
      { // 风元素被动
        baseAttack.iq = (long)(baseAttack.iq * (1000d + talentSkill.vals[0]) / 1000);
        baseAttack.quick = (long)(baseAttack.quick * (1000d + talentSkill.vals[1]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.Bo_Tao)
      { // 海妖蛋被动
        baseAttack.maxBlue = (long)(baseAttack.maxBlue * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.RongYanZhi_Li)
      { // 熔岩之力被动
        baseAttack.maxBlood = (long)(baseAttack.maxBlood * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.YunShi_BaoFa)
      { // 陨石爆发被动
        baseAttack.power = (long)(baseAttack.power * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.Long_Wei)
      {
        baseAttack.hitRate += talentSkill.vals[0];
      }
      else if (talentSkill.skillId == SkillIdEnum.Zhen_Shi)
      {
        baseAttack.hitRate += talentSkill.vals[0];
      }
      else if (talentSkill.skillId == SkillIdEnum.YuanSu_JingZhun)
      {
        baseAttack.hitRate += talentSkill.vals[0];
      }
      else if (talentSkill.skillId == SkillIdEnum.EMo_PiFu)
      {// 恶魔泡泡增加闪避率
        baseAttack.miss += talentSkill.vals[1];
      }
      else if (talentSkill.skillId == SkillIdEnum.KongJuZhi_Ling)
      { // 魅影被动
        baseAttack.miss += talentSkill.vals[0];
      }
      else if (talentSkill.skillId == SkillIdEnum.YanShi_JiaKe)
      { // 魔像被动
        baseAttack.defense = (long)(baseAttack.defense * (1000d + talentSkill.vals[0]) / 1000);
        baseAttack.magicDefense = (long)(baseAttack.magicDefense * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.ZiRanZhi_Li)
      { // 年兽被动
        baseAttack.maxBlood = (long)(baseAttack.maxBlood * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.MeiHuo_DiYu)
      { // 塞壬被动
        baseAttack.iq = (long)(baseAttack.iq * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.BingShuangZhi_Zhu)
      { // 圣诞泡泡被动
        baseAttack.crit += talentSkill.vals[0];
      }
      else if (talentSkill.skillId == SkillIdEnum.Ling_Qiao)
      { // 圣地灵猴被动
        baseAttack.miss += talentSkill.vals[0];
      }
      else if (talentSkill.skillId == SkillIdEnum.HaiYangZhi_Sheng)
      { // 水元素蛋被动
        baseAttack.maxBlue = (long)(baseAttack.maxBlue * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.XiongZhi_LiLiang)
      { // 玩具熊被动
        baseAttack.power = (long)(baseAttack.power * (1000d + talentSkill.vals[0]) / 1000);
      }
      else if (talentSkill.skillId == SkillIdEnum.NeiXi_GuangHuan)
      { // 无尾熊被动
        baseAttack.maxBlue = (long)(baseAttack.maxBlue * (1000d + talentSkill.vals[0]) / 1000);
        baseAttack.hitRate += talentSkill.vals[1];
      }
      else if (talentSkill.skillId == SkillIdEnum.Xun_Jie)
      { // 小木猴被动
        baseAttack.miss += talentSkill.vals[0];
      }
      else if (talentSkill.skillId == SkillIdEnum.YeXing_ChengZhang)
      { // 小叶子被动
        baseAttack.miss += talentSkill.vals[0];
      }
    }

    /**
    * 获取生活技能加成, 同时只有一个生活技能能生效
    * 
    * @param user       用户
    * @param baseAttack 基础攻击
    */
    private static void GetLifeSkillAdd(this User self, BaseAttack baseAttack)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      Skill targetSkill = null;
      Skill chanSkill = skillComponent.GetSkill(SkillIdEnum.Chan);
      Skill daoSkill = skillComponent.GetSkill(SkillIdEnum.Dao);
      if (chanSkill != null && chanSkill.isActive)
      {
        targetSkill = chanSkill;
      }
      else if (daoSkill != null && daoSkill.isActive)
      {
        targetSkill = daoSkill;
      }
      if (targetSkill == null || targetSkill.vals == null || targetSkill.vals.Length < 2)
      {
        return;
      }
      if (targetSkill.skillId == SkillIdEnum.Chan)
      {
        baseAttack.defense += targetSkill.vals[0];
        baseAttack.maxBlood += targetSkill.vals[1];
      }
      else if (targetSkill.skillId == SkillIdEnum.Dao)
      {
        baseAttack.mind += targetSkill.vals[0];
        baseAttack.maxBlue += targetSkill.vals[1];
      }
    }

    private static void GetUserStateAdd(this User self, BaseAttack baseAttack)
    {
      UserStateDetail userStateDetail = self.GetUserState(UserStateEnum.Add_All_Attr);
      if (userStateDetail != null)
      {
        baseAttack.strength += userStateDetail.val[0];
        baseAttack.power += userStateDetail.val[0];
        baseAttack.quick += userStateDetail.val[0];
        baseAttack.iq += userStateDetail.val[0];
        baseAttack.mind += userStateDetail.val[0];
      }
    }

    public static void RecalcUserAttrs(this User self)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      EquipComponent equipComponent = self.GetComponent<EquipComponent>();
      BaseAttack baseAttack = GlobalInfoCache.Instance.GetBaseAttack(attackComponent.job, attackComponent.level);
      UserDaTaoShaInfoComp daTaoShaActComp = self.GetComponent<UserDaTaoShaInfoComp>();
      if (daTaoShaActComp != null)
      {
        baseAttack.strength += daTaoShaActComp.addStrength;
        baseAttack.power += daTaoShaActComp.addPower;
        baseAttack.quick += daTaoShaActComp.addQuick;
        baseAttack.iq += daTaoShaActComp.addIq;
        baseAttack.mind += daTaoShaActComp.addMind;
      }
      foreach (Equipment equipment in equipComponent.equipList)
      {
        if (equipment.remainUseCnt <= 0)
        {
          continue;
        }
        EquipAttrInfo equipAttrInfo = equipment.nowAttrInfo ?? equipment.baseAttrInfo;
        if (equipAttrInfo.minAttack != 0)
        {
          baseAttack.minAttack += equipAttrInfo.minAttack;
        }
        if (equipAttrInfo.maxAttack != 0)
        {
          baseAttack.maxAttack += equipAttrInfo.maxAttack;
        }
        if (equipAttrInfo.attackRate != 0 && equipment.equipPart == EquipPart.Wu_Qi)
        {
          baseAttack.attackRate = equipAttrInfo.attackRate;
        }
        if (equipAttrInfo.realAttack != 0)
        {
          baseAttack.realAttack += equipAttrInfo.realAttack;
        }
        if (equipAttrInfo.crit != 0)
        {
          baseAttack.crit += equipAttrInfo.crit;
        }
        if (equipAttrInfo.critDamage != 0)
        {
          baseAttack.critDmg += equipAttrInfo.critDamage;
        }
        if (equipAttrInfo.hitRate != 0)
        {
          baseAttack.hitRate += equipAttrInfo.hitRate;
        }
        if (equipAttrInfo.miss != 0)
        {
          baseAttack.miss += equipAttrInfo.miss;
        }
        if (equipAttrInfo.defense != 0)
        {
          baseAttack.defense += equipAttrInfo.defense;
        }
        if (equipAttrInfo.magicDefense != 0)
        {
          baseAttack.magicDefense += equipAttrInfo.magicDefense;
        }
        if (equipAttrInfo.blood != 0)
        {
          baseAttack.maxBlood += equipAttrInfo.blood;
        }
        if (equipAttrInfo.blue != 0)
        {
          baseAttack.maxBlue += equipAttrInfo.blue;
        }
        if (equipAttrInfo.strength != 0)
        {
          baseAttack.strength += equipAttrInfo.strength;
        }
        if (equipAttrInfo.power != 0)
        {
          baseAttack.power += equipAttrInfo.power;
        }
        if (equipAttrInfo.quick != 0)
        {
          baseAttack.quick += equipAttrInfo.quick;
        }
        if (equipAttrInfo.iq != 0)
        {
          baseAttack.iq += equipAttrInfo.iq;
        }
        if (equipAttrInfo.mind != 0)
        {
          baseAttack.mind += equipAttrInfo.mind;
        }
      }
      self.GetBaseJobSkillAdd(baseAttack);
      self.GetTalentSkillAdd(baseAttack);
      self.GetLifeSkillAdd(baseAttack);
      // self.getEquipBonusAdd(baseAttack);
      self.GetUserStateAdd(baseAttack);
      attackComponent.SetUserAttack(baseAttack);
      if (attackComponent.blood > attackComponent.maxBlood)
      {
        attackComponent.blood = attackComponent.maxBlood;
      }
      if (attackComponent.blue > attackComponent.maxBlue)
      {
        attackComponent.blue = attackComponent.maxBlue;
      }
    }

    public static void AddUserExp(this User self, long exp, bool canMultiLevel)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      // 如果用户等级达到99级，且经验值达到最大值，则不增加经验
      if (attackComponent.level >= 60 && attackComponent.exp >= attackComponent.maxExp)
      {
        return;
      }
      self.SendChat("获得<u>" + exp + "</u>点经验", ChatType.Sys_Chat);
      // 如果用户等级达到99级，则直接增加经验
      if (attackComponent.level >= 60)
      {
        attackComponent.exp += exp;
        if (attackComponent.exp >= attackComponent.maxExp)
        {
          attackComponent.exp = attackComponent.maxExp;
        }
        return;
      }
      // 如果用户等级小于99级，则需要计算经验值，如果经验值达到最大值，则升级
      long remainExp = exp;
      if (!canMultiLevel)
      {
        remainExp = Math.Min(remainExp, attackComponent.maxExp - attackComponent.exp);
      }
      bool isLevelUp = false;
      while (remainExp > 0)
      {
        long nextExp = attackComponent.maxExp - attackComponent.exp;
        if (remainExp >= nextExp)
        {
          attackComponent.exp = 0;
          self.LevelUp();
          isLevelUp = true;
          remainExp -= nextExp;
        }
        else
        {
          attackComponent.exp += remainExp;
          remainExp = 0;
        }
      }
      if (!isLevelUp)
      {
        self.SendMessage(ServerUpdatePartUserInfoMsg.Create(self, UserUpdateFlagEnum.Exp));
      }
      else
      {
        self.SendMessage(ServerUpdatePartUserInfoMsg.Create(self, UserUpdateFlagEnum.Exp,
            UserUpdateFlagEnum.Base_Attack, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      }
    }

    public static void LevelUp(this User self)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      attackComponent.level++;
      self.SendChat("您的宠物升到了" + attackComponent.level + "级！", ChatType.Sys_Chat);
      self.RecalcUserAttrs();
      attackComponent.exp = 0;
      attackComponent.blood = attackComponent.maxBlood;
      attackComponent.blue = attackComponent.maxBlue;
      ETLog.Info($"User Level Up: {self.Id} {attackComponent.level}");
      // 发布升级事件
      EventSystem.Instance.Publish(self.Scene(), new LevelUpEvent
      {
        user = self
      });
    }

  }
}