namespace MaoYouJi
{
  [EntitySystemOf(typeof(UserActorComponent))]
  [FriendOf(typeof(UserActorComponent))]
  public static partial class UserActorComponentSystem
  {
    [EntitySystem]
    private static void Awake(this UserActorComponent self)
    {
    }

    public static void SetSessionActorId(this UserActorComponent self, ActorId actorId)
    {
      self.UserSessionActorId = actorId;
    }

    public static ActorId GetNowActorId(this UserActorComponent self, bool isAttack = false)
    {
      if (isAttack && self.UserAttackActorId != default)
      {
        return self.UserAttackActorId;
      }
      return self.GetParent<User>().GetParent<MapNode>().GetActorId();
    }

    public static ActorId GetAttackActorId(this UserActorComponent self)
    {
      return self.UserAttackActorId;
    }
  }
}