namespace MaoYouJi
{
  [EntitySystemOf(typeof(UserStateComp))]
  [FriendOf(typeof(UserStateComp))]
  public static partial class UserStateCompSys
  {
    [EntitySystem]
    private static void Awake(this UserStateComp self)
    {
      User user = self.GetParent<User>();
      foreach (UserStateDetail userStateDetail in user.userStates.Values)
      {
        self.AddChild<OneUserStateComp, UserStateDetail>(userStateDetail);
      }
    }
  }

  [EntitySystemOf(typeof(OneUserStateComp))]
  [FriendOf(typeof(OneUserStateComp))]
  public static partial class OneUserStateCompSys
  {
    [EntitySystem]
    private static void Awake(this OneUserStateComp self, UserStateDetail userStateDetail)
    {
      self.userState = userStateDetail.userState;
      self.AddUserStateDetail(userStateDetail);
    }

    private static void AddUserStateDetail(this OneUserStateComp self, UserStateDetail userStateDetail)
    {
      UserStateEnum stateEnum = userStateDetail.userState;
      if (stateEnum != UserStateEnum.ADD_HP && stateEnum != UserStateEnum.ADD_SP && userStateDetail.existTime > 10)
      {
        // RemoveUserStateIn removeUserStateIn = new RemoveUserStateIn();
        // removeUserStateIn.setAllBizStr(AllBizEnum.USER_BIZ.toString());
        // removeUserStateIn.setSubBizStr(UserBizEnum.Remove_User_State.toString());
        // removeUserStateIn.userId = user.id;
        // removeUserStateIn.userStateEnum = stateEnum;
        // schedulerUtil.addScheduleJob(removeUserStateIn, userStateDetail.existTime, userStateDetail.schedulerId,
        //     JobIdUtil.USER_GROUP);
      }
    }

    [EntitySystem]
    private static void Destroy(this OneUserStateComp self)
    {
      User user = self.GetParent<UserStateComp>().GetParent<User>();
      user.userStates.TryRemove(self.userState, out _);
      if (self.schedulerId != null)
      {
        QuartzScheduler.Instance.DeleteJob(JobIdHelper.USER_GROUP, self.schedulerId);
      }
    }
  }
}