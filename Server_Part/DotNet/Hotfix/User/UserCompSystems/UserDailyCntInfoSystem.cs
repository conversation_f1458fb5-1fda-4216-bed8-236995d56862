namespace MaoYouJi
{
  [EntitySystemOf(typeof(UserDailyCntInfo))]
  [FriendOf(typeof(UserDailyCntInfo))]
  public static partial class DailyCntInfoSystem
  {
    [EntitySystem]
    private static void Awake(this UserDailyCntInfo self)
    {
    }

    public static void AddBossKillCnt(this UserDailyCntInfo self, MonBaseType monBaseType)
    {
      self.bossKillCnt.AddOrUpdate(monBaseType, 1, (key, oldValue) => oldValue + 1);
    }
  }
}
