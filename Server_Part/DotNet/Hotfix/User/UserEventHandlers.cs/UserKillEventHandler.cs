using System;

namespace MaoYouJi
{
  [Event(SceneType.Map)]
  public class UserKillMonsterEventHandler : AEvent<Scene, UserKillMonsterEvent>
  {
    protected override async ETTask Run(Scene root, UserKillMonsterEvent args)
    {
      User killedBy = args.killedBy;
      MonsterInfo deader = args.deader;
      if (deader.monDescType == MonDescType.NpcMon)
      {
        // 击杀NPC怪物后增加邪恶值
        killedBy.AddEvilNum(1);
      }
      else if (deader.monBaseType == MonBaseType.BiaoChe)
      {
        killedBy.AddEvilNum(8);
      }
      else if (deader.monDescType == MonDescType.BOSS)
      {
        // 召唤怪物不广播击杀信息
        if (deader.ownUser == 0)
        {
          if (killedBy.teamInfo != null)
          {
            ChatProSystem.SendMessageToAllUser(new ServerSendChatMsg
            {
              content = $"<u>{killedBy.nickname}</u>所在的小队击杀了<u>{deader.monName}</u>。他们的光辉事迹将永远被传颂！",
              chatType = ChatType.Sys_Chat,
            });
          }
          else
          {
            killedBy.GetComponent<UserDailyCntInfo>().AddBossKillCnt(deader.monBaseType);
            ChatProSystem.SendMessageToAllUser(new ServerSendChatMsg
            {
              content = $"<u>{killedBy.nickname}</u>孤身一人击杀了<u>{deader.monName}</u>。他的光辉事迹将永远被传颂！",
              chatType = ChatType.Sys_Chat,
            });
          }
        }
      }
      AttackComponent killedByAttack = killedBy.GetComponent<AttackComponent>();
      AttackComponent deaderAttack = deader.GetComponent<AttackComponent>();
      SkillComponent skillComponent = killedByAttack.GetComponent<SkillComponent>();
      long addExp = deaderAttack.exp;
      Skill skill = skillComponent.GetNowTalentSkill();
      // 如果用户有加经验状态，则增加经验
      if (killedBy.HasUserState(UserStateEnum.ADD_EXP))
      {
        UserStateDetail addExpState = killedBy.GetUserState(UserStateEnum.ADD_EXP);
        long realAdd = addExp * (addExpState.val[0] - 1);
        if (addExpState.val.Length > 2)
        {
          realAdd = Math.Min(realAdd, addExpState.val[2]);
          addExpState.val[2] = addExpState.val[2] - realAdd;
          if (addExpState.val[2] <= 0)
          {
            killedBy.RemoveUserStatus(UserStateEnum.ADD_EXP);
          }
        }
        addExp += realAdd;
      }
      // 幸运能量加经验
      if (skill != null && skill.skillId == SkillIdEnum.XinYun_NengLiang)
      {
        addExp += addExp * skill.vals[0] / 1000;
      }
      killedBy.AddUserExp(addExp, false);
      await ETTask.CompletedTask;
    }
  }

  [Event(SceneType.Map)]
  public class UserKillUserEventHandler : AEvent<Scene, UserKillUserEvent>
  {
    protected override async ETTask Run(Scene root, UserKillUserEvent args)
    {
      User killedBy = args.killedBy;
      User deader = args.deader;
      AttackInCache attackInCache = args.attackInCache;
      await ETTask.CompletedTask;
    }
  }
}