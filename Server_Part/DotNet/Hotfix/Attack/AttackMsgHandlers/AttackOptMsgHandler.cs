namespace MaoYouJi
{
  [MessageHandler(SceneType.Map)] // 开始战斗
  public class UserStartAttackHandler : MessageLocationHandler<MapNode, ClientStartAttackMsg>
  {
    protected override async ETTask Run(MapNode mapNode, ClientStartAttackMsg msg)
    {
      LogicRet logicRet = mapNode.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }
      MapAttackManage mapAttackManage = mapNode.GetComponent<MapAttackManage>();
      AttackComponent target = AttackProcSys.GetAttackComponent(msg.fightInfo);
      if (target == null)
      {
        user?.SendToast("攻击目标不存在");
        return;
      }
      LogicRet ret = await mapAttackManage.StartAttackBase(user.GetComponent<AttackComponent>(), target);
      if (!ret.IsSuccess)
      {
        user?.SendToast(ret.Message);
        return;
      }
    }
  }

  [MessageHandler(SceneType.MapAndAttack)] // 使用技能
  public class UserUseSkillHandler : MessageLocationHandler<Entity, ClientUseSkillMsg>
  {
    protected override async ETTask Run(Entity entity, ClientUseSkillMsg msg)
    {
      User user = null;
      bool isAttack = false;
      if (entity is MapNode mapNode)
      {
        isAttack = false;
        LogicRet logicRet = mapNode.GetUserWithCheck(msg.UserId, out user, true, true);
        if (!logicRet.IsSuccess)
        {
          user?.SendToast(logicRet.Message);
          return;
        }
      }
      else if (entity is AttackInCache attackInCache)
      {
        isAttack = true;
        LogicRet logicRet = UserProcSystem.GetUserWithCheck(msg.UserId, out user);
        if (!logicRet.IsSuccess)
        {
          user?.SendToast(logicRet.Message);
          return;
        }
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      AttackComponent target = AttackProcSys.GetAttackComponent(msg.target);
      if (target == null)
      {
        user?.SendToast("攻击目标不存在");
        return;
      }
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      Skill skill = skillComponent.GetSkill(msg.skillId);
      if (skill == null)
      {
        user?.SendToast("技能不存在");
        return;
      }
      if (!skillComponent.CheckSkillEquip(msg.skillId))
      {
        user?.SendToast("技能未装备");
        return;
      }
      if (!isAttack && attackComponent.LiveState != LiveStateEnum.ALIVE)
      {
        user?.SendToast("当前状态无法进行该操作");
        return;
      }
      if (isAttack && attackComponent.LiveState != LiveStateEnum.FIGHTING)
      {
        user?.SendToast("战斗已结束");
        return;
      }
      if (!isAttack)
      {
        if (msg.skillId == SkillIdEnum.Fu_Ji)
        {
          LogicRet ret = await attackComponent.OutUseFuJi(skill, target);
          if (!ret.IsSuccess)
          {
            user?.SendToast(ret.Message);
            return;
          }
        }
        else
        {
          user.SendToast("您不在战斗中！");
          return;
        }
      }
    }
  }

  [MessageHandler(SceneType.Attack)] // 逃跑
  public class UserEscapeHandler : MessageHandler<AttackInCache, ClientEscapeMsg>
  {
    protected override async ETTask Run(AttackInCache attackInCache, ClientEscapeMsg msg)
    {
      AttackComponent srcAttack = attackInCache.GetFightTarget(msg.UserId);
      if (srcAttack == null)
      {
        ETLog.Warning($"战斗对象不存在: {msg.UserId}");
        return;
      }
      User user = srcAttack.GetParent<User>();
      InFightComponent inFightComponent = srcAttack.GetComponent<InFightComponent>();
      if (inFightComponent == null)
      {
        user?.SendToast("您不在战斗中！");
        return;
      }
      LogicRet ret = inFightComponent.StartEscape();
      if (!ret.IsSuccess)
      {
        user?.SendToast(ret.Message);
        return;
      }
      else
      {
        attackInCache.SendMessageToAllFightUser(new ServerShowToastMsg(srcAttack.fightInfo.fightName + "开始逃跑"));
      }
      await ETTask.CompletedTask;
    }
  }
}