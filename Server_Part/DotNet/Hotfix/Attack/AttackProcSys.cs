namespace MaoYouJi
{
  public static class AttackProcSys
  {
    public static AttackComponent GetAttackComponent(FightInfo fightInfo)
    {
      if (fightInfo == null)
      {
        return null;
      }
      if (fightInfo.liveType == LiveType.ROLE)
      {
        User user = GlobalInfoCache.Instance.GetOnlineUser(fightInfo.fightId);
        return user?.GetComponent<AttackComponent>();
      }
      else if (fightInfo.liveType == LiveType.MONSTER)
      {
        MonsterInfo monsterInfo = GlobalInfoCache.Instance.GetMonsterInfo(fightInfo.fightId);
        return monsterInfo?.GetComponent<AttackComponent>();
      }
      return null;
    }
  }
}