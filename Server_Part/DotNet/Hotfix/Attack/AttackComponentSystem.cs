namespace MaoYouJi
{
  [FriendOf(typeof(AttackComponent))]
  public static partial class AttackComponentSystem
  {
    [EntitySystem]
    public static void Load(this AttackComponent self)
    {
      self.RemoveComponent<InFightComponent>();
    }

    public static async ETTask<LogicRet> OutUseFuJi(this AttackComponent self, Skill skill, AttackComponent target)
    {
      SkillComponent skillComponent = self.GetComponent<SkillComponent>();
      bool isCooling = skillComponent.skillCool.TryGetValue(SkillIdEnum.Fu_Ji, out long readyTime);
      if (isCooling && readyTime > TimeInfo.Instance.ServerNow())
      {
        return LogicRet.Failed("技能还有" + (readyTime - TimeInfo.Instance.ServerNow()) / 1000 + "s冷却完成！");
      }
      if (target == null)
      {
        return LogicRet.Failed("伏击的目标不能为空！");
      }
      if (self.blue < skill.expend)
      {
        return LogicRet.Failed("蓝量不足！");
      }
      <PERSON><PERSON>ob job = skillComponent.GetNowMaoJob();
      if (job == MaoJob.None || job != skill.job)
      {
        return LogicRet.Failed("职业基础技能未激活");
      }
      EquipComponent equipComponent = self.GetParent<User>().GetComponent<EquipComponent>();
      Equipment equipment = equipComponent.GetEquipByPart(EquipPart.Wu_Qi);
      if (equipment == null || equipment.weaponType != WeaponType.Dagger || equipment.remainUseCnt <= 0)
      {
        return LogicRet.Failed("必须装备匕首才能使用伏击");
      }
      MapAttackManage mapAttackManage = self.GetParent<User>().GetParent<MapNode>().GetComponent<MapAttackManage>();
      LogicRet ret = await mapAttackManage.StartAttackBase(self, target);
      if (!ret.IsSuccess)
      {
        return ret;
      }
      return LogicRet.Success;
    }

    public static void GetPos(this AttackComponent self, out string mapName, out string pointName)
    {
      MoveComponent moveComponent = self.fightInfo.liveType == LiveType.ROLE ? self.GetParent<User>().GetComponent<MoveComponent>() : self.GetParent<MonsterInfo>().GetComponent<MoveComponent>();
      mapName = moveComponent.nowMap;
      pointName = moveComponent.nowPoint;
    }

    public static void UpdateMapInfo(this AttackComponent self)
    {
      MapNode mapNode = self.Parent.GetParent<MapNode>();
      if (self.fightInfo.liveType == LiveType.ROLE)
      {
        User user = self.GetParent<User>();
        mapNode.PutUserInMapNode(user);
      }
      else
      {
        MonsterInfo monsterInfo = self.GetParent<MonsterInfo>();
        mapNode.PutMonInMapNode(monsterInfo);
      }
    }

    public static void SetUserAttack(this AttackComponent self, BaseAttack baseAttack)
    {
      self.realAttack = baseAttack.realAttack;
      self.attackRate = baseAttack.attackRate;
      self.defense = baseAttack.defense;
      self.magicDefense = baseAttack.magicDefense;
      self.strength = baseAttack.strength;
      self.power = baseAttack.power;
      self.quick = baseAttack.quick;
      self.iq = baseAttack.iq;
      self.mind = baseAttack.mind;
      self.level = baseAttack.level;
      self.maxExp = baseAttack.maxExp;
      self.damageReduce = baseAttack.damageReduce;
      self.damageAdd = baseAttack.damageAdd;
      self.critDmg = baseAttack.critDmg;
      if (self.job == BaseJob.SOLDIER)
      {
        self.maxBlood = baseAttack.maxBlood + baseAttack.strength * 12;
        self.maxBlue = baseAttack.maxBlue + baseAttack.iq * 12;
        self.minAttack = (long)(baseAttack.minAttack + baseAttack.power * 1.2);
        self.maxAttack = (long)(baseAttack.maxAttack + baseAttack.power * 1.2);
        self.crit = baseAttack.crit + baseAttack.power * 1;
        self.hitRate = baseAttack.hitRate + baseAttack.quick * 2;
        self.miss = baseAttack.miss + baseAttack.quick * 1;
      }
      else
      {
        self.maxBlood = baseAttack.maxBlood + baseAttack.strength * 12;
        self.maxBlue = baseAttack.maxBlue + baseAttack.iq * 12;
        self.minAttack = (long)(baseAttack.minAttack + baseAttack.mind * 1.2);
        self.maxAttack = (long)(baseAttack.maxAttack + baseAttack.mind * 1.2);
        self.crit = baseAttack.crit + baseAttack.mind * 1;
        self.hitRate = baseAttack.hitRate + baseAttack.quick * 2;
        self.miss = baseAttack.miss + baseAttack.quick * 1;
      }
      self.attackNum = self.CalcAttackNum();
    }

    public static long CalcAttackNum(this AttackComponent self)
    {
      long attackNum = 0;
      double critRate = (double)(1000 + self.crit) * self.critDmg / (1000 * 1000),
          hitRate = (double)self.hitRate / 1000, attackRate = (double)20f / self.attackRate;
      if (hitRate > 1.3)
      {
        hitRate = 1.3;
      }
      attackNum += (self.minAttack + self.maxAttack) * 3;
      attackNum += self.realAttack * 5;
      attackNum = (long)(attackNum * (critRate + hitRate) * attackRate);
      attackNum += self.miss * 5;
      attackNum += (self.defense + self.magicDefense) / 4;
      attackNum += (long)(self.blood * 1.5);
      if (self.job == BaseJob.MAGIC)
      {
        attackNum += (long)(self.blue * 1.5);
      }
      else
      {
        attackNum += (long)(self.blue * 1.0);
      }
      return attackNum;
    }
  }
}