using System;

namespace MaoYouJi
{
  [FriendOf(typeof(InFightComponent))]
  public static partial class InFightAttrSystem
  {
    public static long GetNowAttackRate(this InFightComponent self)
    {
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      long subRate = 1000, addRate = 1000;
      // 狂风笼罩攻速减少
      AttachStatus kuangFengStatus = self.GetState(AttackState.KuangFeng_LongZhao);
      if (kuangFengStatus != null)
      {
        subRate += kuangFengStatus.val;
      }
      // 冰霜新星攻速减少
      AttachStatus binShuangStatus = self.GetState(AttackState.BinShuang_XinXing);
      if (binShuangStatus != null)
      {
        subRate += binShuangStatus.val;
      }
      // 好人卡攻速减少
      AttachStatus haoRenKaStatus = self.GetState(AttackState.HaoRen_Ka);
      if (haoRenKaStatus != null)
      {
        subRate += haoRenKaStatus.val;
      }
      // 疾风攻速增加
      AttachStatus jiFengStatus = self.GetState(AttackState.Ji_Feng);
      if (jiFengStatus != null)
      {
        addRate += jiFengStatus.val;
      }
      // 突进攻速增加
      AttachStatus tuJinStatus = self.GetState(AttackState.Tu_Jin);
      if (tuJinStatus != null)
      {
        addRate += tuJinStatus.subVals[0];
      }
      // 黑暗之咒攻速增加
      AttachStatus darkCurse = self.GetState(AttackState.HeiAnZhi_Zhou);
      if (darkCurse != null)
      {
        addRate += darkCurse.subVals[0];
      }
      // 迟缓攻速减少
      AttachStatus chiHuan = self.GetState(AttackState.Chi_Huan);
      if (chiHuan != null)
      {
        subRate += chiHuan.val;
      }
      long attackRate = attackComponent.attackRate * subRate / addRate;
      if (attackRate < 5)
      {
        attackRate = 5;
      }
      return attackRate;
    }

    public static long GetRealHitRate(this InFightComponent self)
    {
      long hitRate = self.GetParent<AttackComponent>().hitRate;
      return hitRate;
    }

    public static long GetRealMiss(this InFightComponent self)
    {
      long miss = self.GetParent<AttackComponent>().miss;
      // 暴怒闪避率降低
      AttachStatus baoNu = self.GetState(AttackState.Bao_Nu);
      if (baoNu != null)
      {
        miss -= baoNu.subVals[0];
      }
      // 敏捷闪避率增加
      AttachStatus minJie = self.GetState(AttackState.Min_Jie);
      if (minJie != null)
      {
        miss += minJie.val;
      }
      return miss;
    }

    public static long GetBaseDmg(this InFightComponent self)
    {
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      if (attackComponent.minAttack == attackComponent.maxAttack)
      {
        return attackComponent.minAttack;
      }
      long baseVal = attackComponent.minAttack + RandomGenerator.RandomNumber(0, (int)(attackComponent.maxAttack - attackComponent.minAttack));
      // 如果有小月月的内衣状态，则基础伤害减少
      AttachStatus yueYueNeiYi = self.GetState(AttackState.YueYue_NeiYi);
      if (yueYueNeiYi != null)
      {
        baseVal -= yueYueNeiYi.val;
      }
      AttachStatus luoLiYuanNian = self.GetState(AttackState.LuoLi_YuanNian);
      if (luoLiYuanNian != null)
      {
        baseVal += luoLiYuanNian.subVals[0];
      }
      if (baseVal < 1)
      {
        baseVal = 1;
      }
      return baseVal;
    }

    public static long GetDelayTime(this InFightComponent self, Skill skill)
    {
      long delayTime = skill.delayTime;
      long addRate = 1000, subRate = self.GetParent<AttackComponent>().skillSpeed;
      // 黑暗之咒延迟时间减少
      AttachStatus darkCurse = self.GetState(AttackState.HeiAnZhi_Zhou);
      if (darkCurse != null)
      {
        subRate += darkCurse.subVals[0];
      }
      return delayTime * addRate / subRate;
    }

    public static long GetDefense(this InFightComponent self, bool isMagic)
    {
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      long defense = isMagic ? attackComponent.magicDefense : attackComponent.defense;
      long addNum = 0, addPercent = 1000;
      foreach (AttachStatus attachStatus in self.attackState.Values)
      {
        if (attachStatus.state == AttackState.Shen_Dun)
        { // 圣盾防御增加
          addNum += attachStatus.val;
        }
        else if (attachStatus.state == AttackState.KuLouGe_AiMu)
        { // 骷髅哥的爱意防御减少
          addNum -= attachStatus.val;
        }
        else if (attachStatus.state == AttackState.NvShen_PiYou)
        { // 女神庇佑防御增加
          addNum += attachStatus.val;
        }
        else if (attachStatus.state == AttackState.LuoLi_YuanNian)
        { // 萝莉控的怨念防御增加
          addNum += attachStatus.subVals[0];
        }
        else if (attachStatus.state == AttackState.Tu_Jin)
        { // 突进防御力降低
          addPercent -= attachStatus.subVals[0];
        }
        else if (attachStatus.state == AttackState.Rong_Jia)
        { // 熔甲防御力降低
          addPercent -= attachStatus.val;
        }
        else if (attachStatus.state == AttackState.ARMOR_BREAK)
        { // 破甲防御力降低
          addPercent -= attachStatus.val;
        }
        else if (attachStatus.state == AttackState.Gang_Ti)
        { // 钢体防御力增加
          addPercent += attachStatus.val;
        }
      }
      defense = defense * addPercent / 1000 + addNum;
      if (defense < 0)
      {
        defense = 0;
      }
      return defense;
    }

    public static bool CanCrit(this InFightComponent self, InFightComponent target)
    {
      long critRate = self.GetParent<AttackComponent>().crit;
      // 燃烧之心增加暴击概率
      AttachStatus ranShaoZhiXin = self.GetState(AttackState.RanShaoZhi_Xin);
      if (ranShaoZhiXin != null)
      {
        critRate += ranShaoZhiXin.subVals[0];
      }
      if (critRate == 0)
      {
        return false;
      }
      long random = RandomGenerator.RandomNumber(0, 1000);
      return random < critRate;
    }

    public static long GetCritDmg(this InFightComponent self, InFightComponent target, long finalDmgNum)
    {
      long critDmgPercent = self.GetParent<AttackComponent>().critDmg;
      // 燃烧之心增加暴击伤害
      AttachStatus ranShaoZhiXin = self.GetState(AttackState.RanShaoZhi_Xin);
      if (ranShaoZhiXin != null)
      {
        critDmgPercent += ranShaoZhiXin.subVals[0];
      }
      long critDmgNum = finalDmgNum * critDmgPercent / 1000;
      return critDmgNum;
    }

    public static long GetRealAttack(this InFightComponent self)
    {
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      long realAttack = attackComponent.realAttack;
      return realAttack;
    }

    public static long GetTargetReduce(this InFightComponent self, DmgType dmgType)
    {
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      long reduce = self.GetParent<AttackComponent>().damageReduce;
      foreach (AttachStatus status in self.attackState.Values)
      {
        if (status.state == AttackState.Xue_KuangBao)
        {// 血狂暴受到伤害增加
          reduce -= status.subVals[0];
        }
        else if (status.state == AttackState.TOUGH)
        {// 坚韧受到伤害减少
          reduce += status.val;
        }
        else if (status.state == AttackState.HeiAnZhi_Zhou)
        {// 黑暗之咒受到伤害增加
          reduce -= status.subVals[2];
        }
        else if (status.state == AttackState.FangHu_JieJie)
        {// 防护结界减免所有伤害
          reduce += status.val;
        }
        else if (status.state == AttackState.Chu_Xue)
        {// 出血状态下受到的伤害增加
          reduce -= status.val;
        }
      }
      ActiveJobInfo activeJobInfo = self.GetComponent<ActiveJobInfo>();
      Skill talentSkill = activeJobInfo.ActiveTalentSkill;
      if (talentSkill != null)
      {
        if (talentSkill.skillId == SkillIdEnum.HuoYan_JieJie)
        {
          // 火焰结界减免火系伤害
          if (attackCtx.Skill != null && attackCtx.Skill.job == MaoJob.Yan_ShuShi)
          {
            reduce += talentSkill.vals[0];
          }
        }
        else if (talentSkill.skillId == SkillIdEnum.ShenSheng_JieJie)
        {
          // 神圣结界减免圣系伤害
          if (attackCtx.Skill != null && attackCtx.Skill.job == MaoJob.Tian_DaoShi)
          {
            reduce += talentSkill.vals[0];
          }
        }
        else if (talentSkill.skillId == SkillIdEnum.FengZhi_Wu)
        {
          // 风之舞减免风系伤害
          if (attackCtx.Skill != null && attackCtx.Skill.job == MaoJob.Feng_ShuShi)
          {
            reduce += talentSkill.vals[0];
          }
        }
        else if (talentSkill.skillId == SkillIdEnum.HanLeng_ShiYing)
        {
          // 寒冷适应减免冰系伤害
          if (attackCtx.Skill != null && attackCtx.Skill.job == MaoJob.Bin_ShuShi)
          {
            reduce += talentSkill.vals[0];
          }
        }
        if (dmgType == DmgType.Magic)
        {
          // 天使和恶魔皮肤减免魔法伤害
          if (talentSkill.skillId == SkillIdEnum.TianShi_ShengGe)
          {
            reduce += talentSkill.vals[0];
          }
          else if (talentSkill.skillId == SkillIdEnum.EMo_PiFu)
          {
            reduce += talentSkill.vals[0];
          }
        }
      }
      return reduce;
    }

    public static long GetSrcAdd(this InFightComponent self, DmgType dmgType)
    {
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      long damageAdd = self.GetParent<AttackComponent>().damageAdd;
      foreach (AttachStatus status in self.attackState.Values)
      {
        if (status.state == AttackState.Xue_KuangBao)
        {// 血狂暴造成伤害增加
          damageAdd += status.subVals[1];
        }
        else if (status.state == AttackState.BLADE_BREAK)
        {// 断刃造成伤害减少
          damageAdd -= status.val;
        }
        else if (status.state == AttackState.STRONG)
        {// 强壮造成伤害增加
          damageAdd += status.val;
        }
        else if (status.state == AttackState.Xue_Xing && dmgType == DmgType.Physics)
        {// 血腥增加物理伤害
          damageAdd += status.val;
        }
        else if (status.state == AttackState.Bao_Nu)
        {// 暴怒受到伤害增加
          damageAdd += status.subVals[0];
        }
        else if (status.state == AttackState.XinLing_JianXiao)
        {// 心灵尖啸增加普攻伤害
          if (attackCtx.AttackType == AttackType.Normal)
          {
            damageAdd += status.val;
          }
        }
        else if (status.state == AttackState.Feng_ZhiLi)
        {// 风之力增加伤害
          if (attackCtx.AttackType == AttackType.Normal && attackCtx.Skill != null && attackCtx.Skill.job == MaoJob.Feng_ShuShi)
          {
            damageAdd += status.val;
          }
        }
      }
      ActiveJobInfo activeJobInfo = self.GetComponent<ActiveJobInfo>();
      Skill talentSkill = activeJobInfo.ActiveTalentSkill;
      if (talentSkill != null)
      {
        if (talentSkill.skillId == SkillIdEnum.Liu_Huo)
        {
          // 流火增加伤害
          long randVal = RandomGenerator.RandomNumber(0, 1000);
          if (randVal < talentSkill.vals[0])
          {
            damageAdd += talentSkill.vals[1];
          }
          else if (talentSkill.skillId == SkillIdEnum.XiangRuiZhi_Li)
          {
            // 祥瑞之力增加技能伤害
            if (attackCtx.AttackType == AttackType.Skill)
            {
              damageAdd += talentSkill.vals[0];
            }
          }
        }
      }
      return damageAdd;
    }

    // 获得最终伤害数值
    public static long GetFinalDmgNum(this InFightComponent self, InFightComponent target, long dmgNum, DmgType dmgType, long addPercent)
    {
      AttackComponent srcAttack = self.GetParent<AttackComponent>();
      AttackComponent targetAttack = target.GetParent<AttackComponent>();
      long defense = self.GetDefense(dmgType == DmgType.Magic);
      // 使用 tanh 函数计算减伤
      double defenseRatio = (defense <= 0 || srcAttack.level <= 0) ? 0 : (double)defense / (srcAttack.level * 20.0); // 防止除零并处理 defense <= 0
      long subNum = (long)(950 * Math.Tanh(0.6 * defenseRatio)); // 调整因子 0.6 和 20.0 可按需修改
                                                                 // 防御力获得的伤害减免最大为95% (tanh 版本已通过乘以950限制，这里可以省略或保留作为双重保险)
      subNum = Math.Min(subNum, 950); // 保留以防万一
      long finalDmgNum = dmgNum * (1000 - subNum) / 1000;
      // 伤害系数
      long damageRate = addPercent - target.GetTargetReduce(dmgType) + self.GetSrcAdd(dmgType);
      Skill talentSkill = self.GetComponent<ActiveJobInfo>().ActiveTalentSkill;
      if (talentSkill != null)
      {
        if (talentSkill.skillId == SkillIdEnum.HaiYang_ShaShou)
        {
          if (target.GetComponent<ActiveJobInfo>().ActiveJob == MaoJob.Bin_ShuShi)
          {
            damageRate += talentSkill.vals[0];
          }
        }
      }
      // 增伤系数最大为300%，最小为-90%
      if (damageRate > 3000)
      {
        damageRate = 3000;
      }
      if (damageRate < -900)
      {
        damageRate = -900;
      }
      // 获取最终伤害
      long finalNum = finalDmgNum * (1000 + damageRate) / 1000;
      return finalNum;
    }
  }
}