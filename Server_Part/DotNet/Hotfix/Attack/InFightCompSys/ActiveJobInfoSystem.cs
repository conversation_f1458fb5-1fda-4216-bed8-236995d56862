namespace MaoYouJi
{
  [EntitySystemOf(typeof(ActiveJobInfo))]
  [FriendOf(typeof(ActiveJobInfo))]
  public static partial class ActiveJobInfoSystem
  {
    [EntitySystem]
    private static void Awake(this ActiveJobInfo self)
    {
      AttackComponent attackComponent = self.Parent.GetParent<AttackComponent>();
      User user = attackComponent.GetParent<User>();
      if (user == null)
      {
        return;
      }
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      foreach (Skill skill in skillComponent.skillMap.Values)
      {
        if (skill.skillType == SkillTypeEnum.TALENT_SKILL && skill.isActive)
        {
          self.ActiveTalentSkill = skill;
        }
        if (skill.skillType == SkillTypeEnum.JOB_Base_SKILL && skill.isActive)
        {
          self.ActiveJobBaseSkill = skill;
          self.ActiveJob = SkillConstant.GetSkillMaoJob(skill.skillId);
        }
      }
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      self.ActiveWeapon = equipComponent.GetEquipByPart(EquipPart.Wu_Qi);
    }

    [EntitySystem]
    private static void Destroy(this ActiveJobInfo self)
    {
    }
  }
}