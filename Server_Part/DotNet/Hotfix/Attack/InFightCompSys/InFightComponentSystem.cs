using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [FriendOf(typeof(InFightComponent))]
  [FriendOf(typeof(AttackComponent))]
  public static partial class InFightComponentSystem
  {
    [EntitySystem]
    private static void Awake(this InFightComponent self, AttackComponent target, AttackInCache attackInCache)
    {
      AttackComponent selfAttack = self.GetParent<AttackComponent>();
      selfAttack.LiveState = LiveStateEnum.FIGHTING;
      self.attackInCache = attackInCache;
      self.attackId = attackInCache.Id;
      self.attackTarget = target.fightInfo;
      self.fightList = new ConcurrentDictionary<long, FightInfo>();
      bool addRlt = self.fightList.TryAdd(target.Id, target.fightInfo);
      self.AddComponent<AttackStatusComponent>();
      self.AddComponent<NormalAttackComp, long>(0);
      self.AddComponent<ActiveJobInfo>();
      // 如果是用户，需要记录战斗的ActorId，方便发送消息
      if (selfAttack.fightInfo.liveType == LiveType.ROLE)
      {
        User user = selfAttack.GetParent<User>();
        user.GetOrAddComponent<UserActorComponent>().UserAttackActorId = attackInCache.GetActorId();
      }
      if (!addRlt)
      {
        ETLog.Error($"add fight list fail: {selfAttack.fightInfo.fightId}, {target.Id}, {attackInCache.Id}");
      }
      selfAttack.UpdateMapInfo();
      target.UpdateMapInfo();
      ETLog.Info($"add fight list: {selfAttack.fightInfo.fightId}, {target.Id}, {attackInCache.Id}");
    }

    [EntitySystem]
    private static void Destroy(this InFightComponent self)
    {
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      if (attackComponent.LiveState != LiveStateEnum.DEAD)
      {
        attackComponent.LiveState = LiveStateEnum.ALIVE;
      }
      AttackInCache attackInCache = self.attackInCache;
      if (attackInCache != null)
      {
        attackInCache.SendQuitInfo(attackComponent);
        attackInCache.FightList.TryRemove(attackComponent.Id, out _);
        attackInCache.UpdateTime = TimeInfo.Instance.ServerNow();
      }
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      long now = TimeInfo.Instance.ServerNow();
      if (skillComponent != null)
      {
        // 删除无用的技能cd
        HashSet<SkillIdEnum> needRemoveCool = new();
        foreach (var entry in skillComponent.skillCool)
        {
          if (entry.Value <= now)
          {
            needRemoveCool.Add(entry.Key);
          }
        }
        foreach (SkillIdEnum skillId in needRemoveCool)
        {
          skillComponent.skillCool.TryRemove(skillId, out _);
        }
      }
      ETLog.Info($"quit attack: {self.Id}, {self.fightList.Count}");
      FightInfo selfFightInfo = self.GetFightInfo();
      // 遍历旧的战斗列表，将该对象从其他对象的战斗列表中移除
      foreach (FightInfo fighter in self.fightList.Values)
      {
        AttackComponent fightTarget = attackInCache.GetFightTarget(fighter);
        InFightComponent fightTargetInFight = fightTarget?.GetComponent<InFightComponent>();
        if (fightTargetInFight == null)
        {
          continue;
        }
        // 移除过程中可能涉及到的其他对象也退出战斗的处理
        fightTargetInFight.RemoveFight(selfFightInfo);

        if (fightTargetInFight.fightList.IsEmpty)
        {
          // 如果对象的战斗数也为0，让该对象也退出战斗
          fightTargetInFight.Dispose();
        }
        else if (fightTargetInFight.attackTarget.fightId == self.Id)
        {
          fightTargetInFight.ChangeTarget(null);
        }
      }
      if (attackInCache.FightList.IsEmpty)
      {
        attackInCache.EndAttack();
      }
      if (attackComponent.fightInfo.liveType == LiveType.ROLE)
      {
        EventSystem.Instance.Publish(attackComponent.Scene(), new UserQuitAttackEvent()
        {
          user = attackComponent.GetParent<User>(),
          attackInCache = attackInCache
        });
      }
      else if (attackComponent.fightInfo.liveType == LiveType.MONSTER)
      {
        EventSystem.Instance.Publish(attackComponent.Scene(), new MonsterQuitAttackEvent()
        {
          monsterInfo = attackComponent.GetParent<MonsterInfo>(),
          attackInCache = attackInCache
        });
      }
    }

    public static FightInfo GetFightInfo(this InFightComponent self)
    {
      return self.GetParent<AttackComponent>().fightInfo;
    }

    public static void AddFightTarget(this InFightComponent self, AttackComponent target)
    {
      FightInfo fightInfo = self.GetFightInfo();
      bool addRlt = self.fightList.TryAdd(target.Id, target.fightInfo);
      if (!addRlt)
      {
        ETLog.Error($"add fight list fail: {fightInfo.fightId}, {target.Id}, {self.fightList.Count}");
      }
      ETLog.Info($"add fight list: {fightInfo.fightId}, {target.Id}, {self.fightList.Count}");
    }

    public static void RemoveFight(this InFightComponent self, FightInfo fightInfo)
    {
      self.fightList.TryRemove(fightInfo.fightId, out _);
    }

    public static bool HasState(this InFightComponent self, AttackState state)
    {
      return self.attackState.ContainsKey(state);
    }

    public static AttachStatus GetState(this InFightComponent self, AttackState state)
    {
      self.attackState.TryGetValue(state, out AttachStatus attachStatus);
      if (attachStatus == null)
      {
        return null;
      }
      return attachStatus;
    }

    public static void RemoveState(this InFightComponent self, AttackState state)
    {
      self.GetComponent<AttackStatusComponent>().RemoveChild((long)state);
    }

    public static bool HasStateNum(this InFightComponent self, AttackState state, int num)
    {
      if (!self.attackState.TryGetValue(state, out AttachStatus attachStatus))
      {
        return false;
      }
      return attachStatus.num >= num;
    }

    public static void ChangeNormalAttack(this InFightComponent self)
    {
      long lastNormalAttackTime = self.GetComponent<NormalAttackComp>().LastNormalAttackTime;
      self.RemoveComponent<NormalAttackComp>();
      self.AddComponent<NormalAttackComp, long>(lastNormalAttackTime);
    }

    public static LogicRet UseSkill(this InFightComponent self, Skill skill)
    {
      AttackInCache attackInCache = self.attackInCache;
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      bool isUser = attackComponent.fightInfo.liveType == LiveType.ROLE;
      if (isUser)
      {
        ActiveJobInfo activeJobInfo = self.GetComponent<ActiveJobInfo>();
        if (skill.needBaseJob != BaseJob.None && attackComponent.job != skill.needBaseJob)
        {
          return LogicRet.Failed("基础职业不匹配");
        }
        if (skill.job != MaoJob.None)
        {
          if (activeJobInfo.ActiveJob != skill.job)
            return LogicRet.Failed("职业基础技能未激活");
          else if (activeJobInfo.ActiveWeapon == null || activeJobInfo.ActiveWeapon.weaponType != SkillConstant.GetJobWeaponType(skill.job))
          {
            return LogicRet.Failed("必须装备对应的职业武器");
          }
          else if (activeJobInfo.ActiveWeapon.remainUseCnt <= 0)
          {
            return LogicRet.Failed("您的武器已损坏，请修理");
          }
        }
      }
      AttackCtxComp attackCtxComp = attackInCache.AddComponent<AttackCtxComp, AttackComponent>(attackComponent);
      ETLog.Info($"use skill: {self.Id}, {skill.skillId}");
      try
      {
        AttackFlowNode rootNode = skill.cureType == CureType.None ? attackCtxComp.BuildDmgSkillFlow(skill) : attackCtxComp.BuildCureSkillFlow(skill);
        return rootNode.ExecuteTree();
      }
      catch (Exception e)
      {
        ETLog.Error(e.ToString());
        return LogicRet.Failed("服务器内部错误!");
      }
      finally
      {
        attackInCache.RemoveComponent<AttackCtxComp>();
      }
    }

    public static LogicRet StartEscape(this InFightComponent self)
    {
      AttackInCache attackInCache = self.attackInCache;
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      AttackCtxComp attackCtxComp = attackInCache.AddComponent<AttackCtxComp, AttackComponent>(attackComponent);
      ETLog.Info($"start escape: {self.Id}");
      try
      {
        AttackFlowNode rootNode = attackCtxComp.BuildEscapeFlow();
        return rootNode.ExecuteTree();
      }
      catch (Exception e)
      {
        ETLog.Error(e.ToString());
        return LogicRet.Failed("服务器内部错误!");
      }
      finally
      {
        attackInCache.RemoveComponent<AttackCtxComp>();
      }
    }

    public static void ChangeTarget(this InFightComponent self, FightInfo destTarget)
    {
      // 将源对象的攻击信息列表复制到新列表中
      List<FightInfo> fightList = new(self.fightList.Values);
      // 如果攻击信息列表为空，则直接返回源对象
      if (fightList.Count == 0)
      {
        ETLog.Error($"changeTarget: {self.Id}, {destTarget.fightId}, {destTarget.fightName}, fightList is empty");
        self.Dispose();
        return;
      }
      // 如果没有指定新的攻击目标，则随机选择一个目标
      destTarget ??= RandomGenerator.RandomArray(fightList);
      // 记录变更攻击目标的日志信息
      ETLog.Info($"changeTarget: {self.Id}, {destTarget.fightId}, {destTarget.fightName}");
      AttackInCache attackInCache = self.attackInCache;
      AttackComponent srcAttack = self.GetParent<AttackComponent>();
      AttackComponent destCanAttack = attackInCache.GetFightTarget(destTarget);
      if (destCanAttack == null)
      {
        ETLog.Error($"changeTarget: {self.Id}, {destTarget.fightId}, {destTarget.fightName}, destCanAttack is null");
        return;
      }
      // 向所有战斗中的人发送变更攻击目标的消息
      attackInCache.SendChangeTarget(srcAttack, destTarget);
      if (srcAttack.fightInfo.liveType == LiveType.ROLE)
      {
        User user = srcAttack.GetParent<User>();
        user.SendMessage(new ServerChangeTargetMsg()
        {
          src = srcAttack.fightInfo,
          targetInfo = destCanAttack.GetAttackDaoInfo()
        });
      }
      // 更新攻击发起者的攻击目标
      self.attackTarget = destTarget;
      attackInCache.UpdateTime = TimeInfo.Instance.ServerNow(); // 更新目标的最后操作时间
    }

    public static string GetAttackJobGroup(this InFightComponent self)
    {
      return JobIdHelper.GetAttackJobGroup(self.attackId);
    }
  }
}