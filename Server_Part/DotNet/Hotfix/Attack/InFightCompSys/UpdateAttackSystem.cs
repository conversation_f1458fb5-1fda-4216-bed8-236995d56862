using System.Collections.Generic;

namespace MaoYouJi
{
  public static partial class UpdateAttackSystem
  {
    public static void UpdateAttackBloodAndBlue(this InFightComponent self, long bloodSub = 0, long blueSub = 0)
    {
      UpdateAttackInfo updateAttackInfo = new();
      AttackComponent src = self.GetParent<AttackComponent>();
      if (bloodSub != 0)
      {
        updateAttackInfo.flag |= 1 << (int)UpdateAttackType.Update_Blood;
        updateAttackInfo.bloodSub = bloodSub;
      }
      if (blueSub != 0)
      {
        updateAttackInfo.flag |= 1 << (int)UpdateAttackType.Update_Blue;
        updateAttackInfo.blueSub = blueSub;
      }
      updateAttackInfo.srcId = src.fightInfo.fightId;
      self.attackInCache.Get().SendMessageToAllFightUser(new ServerUpdateAttackInfoMsg { updateAttackInfos = new() { updateAttackInfo } });
    }

    public static void UpdateAttackState(this InFightComponent self, List<AttachStatus> stateList,
      List<AttackState> removeStateList)
    {
      UpdateAttackInfo updateAttackInfo = new();
      AttackComponent src = self.GetParent<AttackComponent>();
      updateAttackInfo.flag |= 1 << (int)UpdateAttackType.Update_State;
      if (stateList != null)
      {
        updateAttackInfo.stateList = stateList;
      }
      if (removeStateList != null)
      {
        updateAttackInfo.removeStateList = removeStateList;
      }
      updateAttackInfo.srcId = src.fightInfo.fightId;
      self.attackInCache.Get().SendMessageToAllFightUser(new ServerUpdateAttackInfoMsg { updateAttackInfos = new() { updateAttackInfo } });
    }

    public static void UpdateAttackState(this InFightComponent self, AttachStatus status, AttackState removeState)
    {
      UpdateAttackInfo updateAttackInfo = new();
      AttackComponent src = self.GetParent<AttackComponent>();
      updateAttackInfo.flag |= 1 << (int)UpdateAttackType.Update_State;
      if (status != null)
      {
        updateAttackInfo.stateList = new() { status };
      }
      if (removeState != AttackState.None)
      {
        updateAttackInfo.removeStateList = new() { removeState };
      }
      updateAttackInfo.srcId = src.fightInfo.fightId;
      self.attackInCache.Get().SendMessageToAllFightUser(new ServerUpdateAttackInfoMsg { updateAttackInfos = new() { updateAttackInfo } });
    }

    public static void UpdateAttackLiveState(this InFightComponent self, LiveStateEnum liveState)
    {
      UpdateAttackInfo updateAttackInfo = new();
      AttackComponent src = self.GetParent<AttackComponent>();
      updateAttackInfo.flag |= 1 << (int)UpdateAttackType.Update_Live_State;
      updateAttackInfo.liveState = liveState;
      updateAttackInfo.srcId = src.fightInfo.fightId;
      self.attackInCache.Get().SendMessageToAllFightUser(new ServerUpdateAttackInfoMsg { updateAttackInfos = new() { updateAttackInfo } });
    }

    public static void UpdateAttackSkillCool(this InFightComponent self, Dictionary<SkillIdEnum, long> skillCool)
    {
      UpdateAttackInfo updateAttackInfo = new();
      AttackComponent src = self.GetParent<AttackComponent>();
      updateAttackInfo.flag |= 1 << (int)UpdateAttackType.Update_Skill_Cool;
      updateAttackInfo.skillCool = skillCool;
      updateAttackInfo.srcId = src.fightInfo.fightId;
      self.attackInCache.Get().SendMessageToAllFightUser(new ServerUpdateAttackInfoMsg { updateAttackInfos = new() { updateAttackInfo } });
    }

    public static void UpdateAttackKilled(this InFightComponent self, FightInfo killed, long killTime)
    {
      UpdateAttackInfo updateAttackInfo = new();
      AttackComponent src = self.GetParent<AttackComponent>();
      updateAttackInfo.flag |= 1 << (int)UpdateAttackType.Update_Kill_Info;
      updateAttackInfo.killed = killed;
      updateAttackInfo.killTime = killTime;
      updateAttackInfo.srcId = src.fightInfo.fightId;
      self.attackInCache.Get().SendMessageToAllFightUser(new ServerUpdateAttackInfoMsg { updateAttackInfos = new() { updateAttackInfo } });
    }
  }
}