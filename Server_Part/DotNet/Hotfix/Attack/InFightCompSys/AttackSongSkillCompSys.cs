namespace MaoYouJi
{
  [EntitySystemOf(typeof(AttackSongSkillComp))]
  [FriendOf(typeof(AttackSongSkillComp))]
  public static partial class AttackSongSkillCompSys
  {
    [EntitySystem]
    private static void Awake(this AttackSongSkillComp self)
    {
      // 待补充，添加定时任务
      InFightComponent inFight = self.GetParent<InFightComponent>();

    }

    [EntitySystem]
    private static void Destroy(this AttackSongSkillComp self)
    {
      InFightComponent inFight = self.GetParent<InFightComponent>();
      QuartzScheduler.Instance.DeleteJob(inFight.GetAttackJobGroup(), self.SongJobId);
    }
  }
}