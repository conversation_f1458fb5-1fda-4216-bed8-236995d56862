namespace MaoYouJi
{
  [EntitySystemOf(typeof(NormalAttackComp))]
  [FriendOf(typeof(NormalAttackComp))]
  public static partial class NormalAttackCompSys
  {
    // 这里传入的是距离上次攻击的时间，单位是毫秒
    [EntitySystem]
    private static void Awake(this NormalAttackComp self, long lastNormalAttackTime)
    {
      // 将初始攻击时间置为现在
      if (lastNormalAttackTime == 0)
      {
        self.LastNormalAttackTime = TimeInfo.Instance.ServerNow();
      }
      else
      {
        self.LastNormalAttackTime = lastNormalAttackTime;
      }
      self.NowAttackRate = self.GetParent<InFightComponent>().GetNowAttackRate() * 100;
      InFightComponent inFight = self.GetParent<InFightComponent>();
      self.NormalAttackJobId = self.Id + "_" + "normal_attack";
      long timeGam = TimeInfo.Instance.ServerNow() - self.LastNormalAttackTime;
      long delayTime = self.NowAttackRate - timeGam;
      AttackInCache attackInCache = inFight.attackInCache;
      MaoScheduleJobInfo jobInfo = new(attackInCache.GetActorId(),
       new InnerExecNormalAttack
       {
         fightInfo = self.Parent.GetParent<AttackComponent>().fightInfo,
       }
      );
      QuartzScheduler.Instance.AddScheduleJob(jobInfo, inFight.GetAttackJobGroup(), self.NormalAttackJobId, delayTime, self.NowAttackRate, int.MaxValue);
    }

    [EntitySystem]
    private static void Destroy(this NormalAttackComp self)
    {
      InFightComponent inFight = self.GetParent<InFightComponent>();
      QuartzScheduler.Instance.DeleteJob(inFight.GetAttackJobGroup(), self.NormalAttackJobId);
    }

    public static void ExecNormalAttack(this NormalAttackComp self)
    {
      AttackInCache attackInCache = self.GetParent<InFightComponent>().attackInCache;
      AttackComponent attackComponent = self.Parent.GetParent<AttackComponent>();
      AttackCtxComp attackCtxComp = attackInCache.AddComponent<AttackCtxComp, AttackComponent>(attackComponent);
      try
      {
        AttackFlowNode rootNode = attackCtxComp.BuildAutoAttackFlow();
        rootNode.ExecuteTree();
      }
      finally
      {
        attackInCache.RemoveComponent<AttackCtxComp>();
      }
    }
  }
}