using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(AttackStatusComponent))]
  [FriendOf(typeof(InFightComponent))]
  [FriendOf(typeof(AttackStatusComponent))]
  public static partial class AttackStatusComponentSystem
  {
    [EntitySystem]
    private static void Awake(this AttackStatusComponent self)
    {

    }

    [EntitySystem]
    private static void Destroy(this AttackStatusComponent self)
    {

    }
  }

  [EntitySystemOf(typeof(OneAttackStatus))]
  [FriendOf(typeof(OneAttackStatus))]
  public static partial class OneAttackStatusSystem
  {
    [EntitySystem]
    private static void Awake(this OneAttackStatus self, AttachStatus attachStatus, AttackComponent addSrc)
    {
      self.attackState = attachStatus.state;
      InFightComponent targetInFight = self.GetParent<AttackStatusComponent>().GetParent<InFightComponent>();
      AttackComponent targetAttack = targetInFight.GetParent<AttackComponent>();
      AttackInCache attackInCache = targetInFight.attackInCache;
      attachStatus.addInfo = addSrc.fightInfo;
      if (attachStatus.existTime > 0)
      {
        attachStatus.endTime = TimeInfo.Instance.ServerNow() + attachStatus.existTime;
      }
      targetInFight.attackState.TryAdd(attachStatus.state, attachStatus);
      self.jobId = self.GetStateScheduleId(attachStatus.state, addSrc.fightInfo);
      // 移除咏唱状态
      if (AttackStateHelper.IsBreakSongState(attachStatus.state))
      {
        // breakSong(target.getAttackInfo());
        // 如果状态不是逃跑，并且目标有逃跑状态，则移除逃跑状态
        if (attachStatus.state != AttackState.Tao_Pao && targetInFight.HasState(AttackState.Tao_Pao))
        {
          targetInFight.RemoveState(AttackState.Tao_Pao);
        }
      }
      if (AttackStateHelper.IsChangeAutoAttackState(attachStatus.state))
      {

      }
      if (attachStatus.state == AttackState.Tao_Pao)
      {
        MaoScheduleJobInfo jobInfo = new(attackInCache.GetActorId(), new InnerExecEscapeMsg { fightInfo = targetAttack.fightInfo });
        QuartzScheduler.Instance.AddScheduleJob(jobInfo, targetInFight.GetAttackJobGroup(), self.jobId, attachStatus.existTime);
      }
      else if (AttackStateHelper.IsPosionState(attachStatus.state))
      {
        // PosionExecIn posionExecIn = new PosionExecIn();
        // posionExecIn.setAllBizStr(AllBizEnum.ATTACK_BIZ.toString());
        // posionExecIn.setSubBizStr(AttackBizEnum.Poison_Exec.toString());
        // posionExecIn.src = src;
        // posionExecIn.target = target;
        // posionExecIn.status = status;
        // int execNum = (int)(status.existTime / 1000);
        // status.num = execNum;
        // schedulerUtil.addScheduleJob(posionExecIn, 1000, 1000, execNum, jobId,
        //     JobIdUtil.getAttackJobGroup(ctx.attackId));
      }
      else if (AttackStateHelper.IsCureState(attachStatus.state))
      {
        // CureExecIn cureExecIn = new CureExecIn();
        // cureExecIn.setAllBizStr(AllBizEnum.ATTACK_BIZ.toString());
        // cureExecIn.setSubBizStr(AttackBizEnum.Cure_Exec.toString());
        // int execNum = (int)(status.existTime / 1000);
        // status.num = execNum;
        // if (status.isPercent)
        // {
        //   status.val = status.val * target.getAttackInfo().maxBlood / 1000 / execNum;
        //   if (status.val < 1)
        //   {
        //     status.val = 1;
        //   }
        // }
        // cureExecIn.src = src;
        // cureExecIn.target = target;
        // cureExecIn.status = status;
        // cureExecIn.cureType = 0;
        // LogUtil.INFO("addStatusTimer", target.getFightInfo().fightName, status.state, status.endTime);
        // schedulerUtil.addScheduleJob(cureExecIn, 1000, 1000, execNum, jobId,
        //     JobIdUtil.getAttackJobGroup(ctx.attackId));
      }
      else
      {
        // RemoveStateIn removeStateIn = new RemoveStateIn();
        // removeStateIn.setAllBizStr(AllBizEnum.ATTACK_BIZ.toString());
        // removeStateIn.setSubBizStr(AttackBizEnum.REMOVE_STATE.toString());
        // removeStateIn.endTime = status.endTime;
        // removeStateIn.target = target;
        // removeStateIn.state = status.state;
        // LogUtil.INFO("addStatusTimer", target.getFightInfo().fightName, status.state, status.endTime);
        // schedulerUtil.addScheduleJob(removeStateIn, new Date(status.endTime), jobId,
        //     JobIdUtil.getAttackJobGroup(ctx.attackId));
      }
    }

    [EntitySystem]
    private static void Destroy(this OneAttackStatus self)
    {
      ETLog.Info($"RemoveState: {self.attackState}");
      InFightComponent inFight = self.GetParent<AttackStatusComponent>().GetParent<InFightComponent>();
      inFight.attackState.TryRemove(self.attackState, out _);
      QuartzScheduler.Instance.DeleteJob(inFight.GetAttackJobGroup(), self.jobId);
      // 如果状态是改变攻击速度，则改变普通攻击
      if (AttackStateHelper.IsChangeAutoAttackState(self.attackState))
      {
        inFight.ChangeNormalAttack();
      }
    }

    private static string GetStateScheduleId(this OneAttackStatus self, AttackState attackState, FightInfo fightInfo)
    {
      return fightInfo.fightId + "_State_" + attackState;
    }
  }
}