namespace MaoYouJi
{
  [FriendOf(typeof(KilledComponent))]
  public static partial class KilledComponentSystem
  {
    [EntitySystem]
    private static void Awake(this KilledComponent self, AttackComponent killedBy)
    {
      self.killed = killedBy.fightInfo;
      self.killTime = TimeInfo.Instance.ServerNow();
      AttackComponent selfAttack = self.GetParent<AttackComponent>();
      selfAttack.LiveState = LiveStateEnum.DEAD;
      selfAttack.blood = 0;
      selfAttack.blue = 0;
      if (selfAttack.fightInfo.liveType == LiveType.ROLE)
      {
        EventSystem.Instance.Publish(self.Root(), new UserDeadEvent()
        {
          deader = selfAttack.GetParent<User>(),
          killedBy = killedBy,
        });
        if (killedBy.fightInfo.liveType == LiveType.ROLE)
        {
          EventSystem.Instance.Publish(self.Root(), new UserKillUserEvent()
          {
            deader = selfAttack.GetParent<User>(),
            killedBy = killedBy.GetParent<User>(),
          });
        }
        else
        {
          EventSystem.Instance.Publish(self.Root(), new MonsterKillUserEvent()
          {
            deader = selfAttack.GetParent<User>(),
            killedBy = killedBy.GetParent<MonsterInfo>(),
          });
        }
      }
      else
      {
        EventSystem.Instance.Publish(self.Root(), new MonsterDeadEvent()
        {
          deader = selfAttack.GetParent<MonsterInfo>(),
          killedBy = killedBy,
        });
        if (killedBy.fightInfo.liveType == LiveType.ROLE)
        {
          EventSystem.Instance.Publish(self.Root(), new UserKillMonsterEvent()
          {
            deader = selfAttack.GetParent<MonsterInfo>(),
            killedBy = killedBy.GetParent<User>(),
          });
        }
        else
        {
          EventSystem.Instance.Publish(self.Root(), new MonsterKillMonsterEvent()
          {
            deader = selfAttack.GetParent<MonsterInfo>(),
            killedBy = killedBy.GetParent<MonsterInfo>(),
          });
        }
      }
    }
  }
}