using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  public static class AttackFlowBuilder
  {
    public static void InjectFlow(this AttackCtxComp ctx, AttackFlowNode root)
    {
      AttackComponent src = ctx.Src;
      InFightComponent srcInFight = src.GetComponent<InFightComponent>();
      List<AttackFlowNode> allNodes = root.GetAllNode();
      Dictionary<AttackFlowEnum, List<AttackFlowNode>> nodeMap = new();
      foreach (AttackFlowNode node in allNodes)
      {
        if (nodeMap.TryGetValue(node.FlowType, out List<AttackFlowNode> value))
        {
          value.Add(node);
        }
        else
        {
          nodeMap[node.FlowType] = new List<AttackFlowNode>() { node };
        }
      }
      // 技能拦截，仅在使用技能时生效
      if (ctx.AttackType == AttackType.Skill)
      {
        Skill skill = ctx.Skill;
        if (AttackFlowInjectInfo.SkillInjects.TryGetValue(skill.skillId, out AttackFlowEnum flowType))
        {
          List<AttackFlowNode> nodes = nodeMap[flowType];
          if (nodes != null)
          {
            ctx.InjectInfos.Add(skill.skillId.ToString());
            foreach (AttackFlowNode node in nodes)
            {
              EventSystem.Instance.Invoke((long)skill.skillId, new SkillInjectInfo(skill, ctx, node));
            }
          }
        }
      }
      // 自己状态拦截
      foreach (AttackState state in AttackFlowInjectInfo.SelfStateInjects.Keys)
      {
        if (srcInFight.HasState(state))
        {
          List<AttackFlowNode> nodes = nodeMap[AttackFlowInjectInfo.SelfStateInjects[state]];
          if (nodes != null)
          {
            ctx.InjectInfos.Add(state.ToString());
            foreach (AttackFlowNode node in nodes)
            {
              EventSystem.Instance.Invoke((long)state, new SelfStateInjectInfo(state, ctx, node));
            }
          }
        }
      }
      nodeMap.TryGetValue(AttackFlowEnum.Calc_Dmg, out List<AttackFlowNode> calcDmgNodes);
      // 目标状态拦截
      foreach (AttackFlowNode node in calcDmgNodes)
      {
        AttackComponent target = node.NodeCtx.ReplaceTarget;
        InFightComponent targetInFight = target.GetComponent<InFightComponent>();
        foreach (AttackState state in AttackFlowInjectInfo.TargetStateInjects)
        {
          if (targetInFight.HasState(state))
          {
            ctx.InjectInfos.Add(target.fightInfo.fightName + "-" + state.ToString());
            EventSystem.Instance.Invoke((long)state, new TargetStateInjectInfo(state, ctx, node));
          }
        }
      }
    }

    public static bool CanJustSkill(this AttackComponent src, Skill skill)
    {
      InFightComponent srcInFight = src.GetComponent<InFightComponent>();
      if (srcInFight.HasState(AttackState.Heng_Sao) && skill.skillId == SkillIdEnum.SheMin_ChuanCi)
      {
        return true;
      }
      if (srcInFight.HasState(AttackState.MoFa_AoYi))
      {
        return true;
      }
      return false;
    }

    private static CheckCondCtx GetSkillCheckCondCtx(this AttackCtxComp ctx, Skill skill, InFightComponent srcInFight)
    {
      CheckCondCtx condCtx = new()
      {
        BlueExpend = skill.expend,
        NotSelfState = new List<AttackState>()
        {
          AttackState.ChanRao,
          AttackState.DIZZY,
          AttackState.FROZEN,
          AttackState.Chui_Fei
        }
      };
      if (!srcInFight.HasState(AttackState.MoFa_AoYi))
        condCtx.NotSelfState.Add(AttackState.SILENT);
      return condCtx;
    }

    // 自动攻击工作流
    public static AttackFlowNode BuildAutoAttackFlow(this AttackCtxComp ctx)
    {
      AttackComponent src = ctx.Src.Get();
      InFightComponent srcInFight = src.GetComponent<InFightComponent>();
      AttackComponent target = ctx.NowTarget.Get();
      CheckCondCtx condCtx = new()
      {
        NotSelfState = new List<AttackState>()
        {
          AttackState.ChanRao,
          AttackState.DIZZY,
          AttackState.FROZEN,
          AttackState.Chui_Fei,
          AttackState.KongShouRu_BaiRen,
          AttackState.KongShouRu_BaiRen2,
          AttackState.Tao_Pao
        }
      };
      if (!srcInFight.HasState(AttackState.ZhiMin_YiJi))
        condCtx.NotSelfState.Add(AttackState.TAKE_WEAPON);
      AttackFlowNode root = AttackFlowNodeFunc.CreateNode(AttackFlowEnum.Check_Pre_Cond, condCtx);
      AttackFlowNode procNode = root.AddChild(AttackFlowEnum.Pre_Proc, new PreProcCtx());
      IsHitCtx isHitCtx = new()
      {
        ReplaceTarget = ctx.NowTarget
      };
      AttackFlowNode hitNode = procNode.AddChild(AttackFlowEnum.Is_Hit, isHitCtx);
      CalcDmgCtx calcDmgCtx = new()
      {
        ReplaceTarget = ctx.NowTarget,
        DmgType = src.job == BaseJob.SOLDIER ? DmgType.Physics : DmgType.Magic
      };
      hitNode.AddRight(AttackFlowEnum.Calc_Dmg, calcDmgCtx).AddRight(AttackFlowEnum.Dmg_Target, calcDmgCtx);
      hitNode.AddChild(AttackFlowEnum.Proc_Rlt, new AttackFlowContext());
      ctx.RootFlowNode = root;
      // 注入flow
      ctx.InjectFlow(root);
      return root;
    }

    // 构建对单个目标造成伤害的工作流
    private static AttackFlowNode BuildOneTargetSkillFlow(this AttackCtxComp self, AttackFlowNode nowNode, AttackComponent target, Skill skill, List<AttachStatus> targetStatus)
    {
      IsHitCtx isHitCtx = new IsHitCtx();
      AttackComponent src = self.Src;
      isHitCtx.ReplaceTarget = target;
      AttackFlowNode hitNode = nowNode.AddChild(AttackFlowEnum.Is_Hit, isHitCtx);
      AttackFlowNode lastRight = hitNode;
      // 目标状态不为空，则添加目标状态
      if (targetStatus != null)
      {
        SetStateCtx setStateCtx = new SetStateCtx(target, true, targetStatus, null);
        lastRight = hitNode.AddRight(AttackFlowEnum.Set_State, setStateCtx);
      }
      // 伤害不为0，则添加伤害
      if (skill.damage != 0)
      {
        CalcDmgCtx calcDmgCtx = new()
        {
          IsPercentDmg = skill.isPercent(),
          DmgNum = skill.damage,
          ExtraDmg = skill.extraDmg,
          ReplaceTarget = target,
          DmgType = src.job == BaseJob.SOLDIER ? DmgType.Physics : DmgType.Magic
        };
        lastRight.AddRight(AttackFlowEnum.Calc_Dmg, calcDmgCtx).AddRight(AttackFlowEnum.Dmg_Target, calcDmgCtx);
      }
      return hitNode;
    }

    // 构建技能工作流
    public static AttackFlowNode BuildDmgSkillFlow(this AttackCtxComp ctx, Skill skill)
    {
      AttackFlowNode root = null;
      ctx.AttackType = AttackType.Skill;
      AttackComponent src = ctx.Src;
      InFightComponent srcInFight = src.GetComponent<InFightComponent>();
      AttackInCache attackInCache = ctx.GetParent<AttackInCache>();
      CheckCondCtx condCtx = ctx.GetSkillCheckCondCtx(skill, srcInFight);
      root = AttackFlowNodeFunc.CreateNode(AttackFlowEnum.Check_Pre_Cond, condCtx);
      // 二次释放移除技能状态
      if (skill.secondUseNeedState != AttackState.None && srcInFight.HasState(skill.secondUseNeedState))
      {
        root.ChildNode = null;
        root.RightNode = null;
        AttachStatus status = srcInFight.GetState(skill.secondUseNeedState);
        condCtx.UniqCheck.Add((atkCtx) =>
        {
          if (status.endTime - TimeInfo.Instance.ServerNow() > status.existTime - 1000)
          {
            return LogicRet.Failed("施加时间小于1s，无法移除！");
          }
          return LogicRet.Success;
        });
        condCtx.BlueExpend = 0;
        if (skill.secondUseNeedState == AttackState.FROZEN)
        {
          condCtx.NotSelfState.Remove(AttackState.FROZEN);
        }
        SetStateCtx setStateCtx = new(src, false, null, skill.secondUseNeedState);
        root.AddChild(AttackFlowEnum.Set_State, setStateCtx).AddChild(AttackFlowEnum.Proc_Rlt, null);
        return root;
      }
      else if (skill.delayTime > 0 && !src.CanJustSkill(skill))
      {
        // 开始咏唱
        ctx.SetStartSong();
        AttackFlowNode nowNode = root.AddChild(AttackFlowEnum.Start_Song, null);
        if (srcInFight.HasState(AttackState.Tao_Pao))
        {
          SetStateCtx setStateCtx = new(src, false, null, AttackState.Tao_Pao);
          setStateCtx.srcDesc = skill.name;
          nowNode = nowNode.AddChild(AttackFlowEnum.Set_State, setStateCtx);
        }
        nowNode.AddChild(AttackFlowEnum.Proc_Rlt, null);
      }
      else
      {
        AttackFlowNode procNode = root.AddChild(AttackFlowEnum.Pre_Proc, null);
        if (skill.selfStatus != null)
        {
          SetStateCtx setStateCtx = new(src, true, skill.selfStatus, null);
          procNode.AddChild(AttackFlowEnum.Set_State, setStateCtx);
        }
        AttackFlowNode lastNode = procNode;
        List<AttachStatus> targetStatus = skill.targetStatus;
        if (skill.damage != 0 || targetStatus != null)
        {
          if (skill.targetNum == 0 || skill.targetNum == 1)
          {
            lastNode = ctx.BuildOneTargetSkillFlow(lastNode, ctx.NowTarget, skill, targetStatus);
          }
          else if (skill.targetNum == -1)
          {
            // 全体攻击
            List<AttackComponent> targetList = attackInCache.GetTargetList(src);
            foreach (AttackComponent target in targetList)
            {
              List<AttachStatus> targetStatusClone = targetStatus.Select(item => item.Clone() as AttachStatus).ToList();
              lastNode = ctx.BuildOneTargetSkillFlow(lastNode, target, skill, targetStatusClone);
            }
          }
          else
          {
            // 多目标攻击
            List<AttackComponent> targetList = attackInCache.GetTargetList(src);
            List<AttackComponent> randomList = RandomGenerator.RandomArray(targetList, skill.targetNum);
            foreach (AttackComponent target in randomList)
            {
              List<AttachStatus> targetStatusClone = targetStatus.Select(item => item.Clone() as AttachStatus).ToList();
              lastNode = ctx.BuildOneTargetSkillFlow(lastNode, target, skill, targetStatusClone);
            }
          }
        }
        lastNode.AddChild(AttackFlowEnum.Proc_Rlt, null);
      }
      ctx.InjectFlow(root);
      return root;
    }

    public static AttackFlowNode BuildCureSkillFlow(this AttackCtxComp ctx, Skill skill)
    {
      ctx.AttackType = AttackType.Skill;
      AttackComponent src = ctx.Src;
      InFightComponent srcInFight = src.GetComponent<InFightComponent>();
      CheckCondCtx condCtx = ctx.GetSkillCheckCondCtx(skill, srcInFight);
      AttackFlowNode root = AttackFlowNodeFunc.CreateNode(AttackFlowEnum.Check_Pre_Cond, condCtx);
      AttackFlowNode procNode = root.AddChild(AttackFlowEnum.Pre_Proc, null);
      CureTargetCtx cureTargetCtx = new CureTargetCtx();
      if (skill.cureType == CureType.HP)
      {
        cureTargetCtx.cureHpNum = skill.damage;
      }
      else if (skill.cureType == CureType.MP)
      {
        cureTargetCtx.cureMpNum = skill.damage;
      }
      else if (skill.cureType == CureType.HP_MP)
      {
        cureTargetCtx.cureHpNum = skill.damage;
        cureTargetCtx.cureMpNum = skill.damage;
      }
      cureTargetCtx.cureType = skill.cureType;
      cureTargetCtx.ReplaceTarget = src;
      procNode.AddChild(AttackFlowEnum.Cure_Target, cureTargetCtx).AddChild(AttackFlowEnum.Proc_Rlt, null);
      return root;
    }

    // 构建使用物品工作流
    public static AttackFlowNode BuildUseThingFlow(this AttackCtxComp ctx, Thing thing)
    {
      CheckCondCtx condCtx = new()
      {
        NotSelfState = new List<AttackState>()
        {
          AttackState.ChanRao,
          AttackState.DIZZY,
          AttackState.FROZEN,
          AttackState.Chui_Fei,
          AttackState.SILENT
        }
      };
      AttackFlowNode root = AttackFlowNodeFunc.CreateNode(AttackFlowEnum.Check_Pre_Cond, condCtx);
      ctx.Thing = thing;
      ctx.AttackType = AttackType.Item;
      AttackComponent src = ctx.Src;
      if (thing.thingType == ThingType.FOOD)
      {
        Food food = thing.Clone() as Food;
        CureTargetCtx cureTargetCtx = new();
        cureTargetCtx.cureHpNum = food.isPercent ? src.maxBlood * food.val / 1000 : food.val;
        cureTargetCtx.cureType = CureType.HP;
        if (food.addType == FoodAddType.ADD_SP)
        {
          cureTargetCtx.cureMpNum = food.isPercent ? src.maxBlue * food.val / 1000 : food.val;
          cureTargetCtx.cureType = CureType.MP;
        }
        else if (food.addType == FoodAddType.ADD_ALL)
        {
          cureTargetCtx.cureMpNum = food.isPercent ? src.maxBlue * food.val / 1000 : food.val;
          cureTargetCtx.cureType = CureType.HP_MP;
        }
        cureTargetCtx.ReplaceTarget = ctx.NowTarget;
        root.AddChild(AttackFlowEnum.Pre_Proc, null).AddChild(AttackFlowEnum.Cure_Target, cureTargetCtx)
            .AddChild(AttackFlowEnum.Proc_Rlt, null);
      }
      else if (thing.thingType == ThingType.TREASURE)
      {
        AttackFlowNode lastNode = root;
        lastNode = lastNode.AddChild(AttackFlowEnum.Pre_Proc, null);
        Treasure treasure = thing.Clone() as Treasure;
        if (treasure.selfStatus != null && treasure.selfStatus.Count > 0)
        {
          SetStateCtx setStateCtx = new(src, true, treasure.selfStatus, null);
          lastNode = lastNode.AddChild(AttackFlowEnum.Set_State, setStateCtx);
        }
        // 宝物不需要考虑命中
        if (treasure.val > 0)
        {
          CalcDmgCtx calcDmgCtx = new()
          {
            IsPercentDmg = false,
            // 使用宝物不附加基础攻击
            IsAddBaseDmg = false,
            DmgNum = treasure.val,
            ReplaceTarget = ctx.NowTarget,
            DmgType = src.job == BaseJob.SOLDIER ? DmgType.Physics : DmgType.Magic
          };
          lastNode = lastNode.AddChild(AttackFlowEnum.Calc_Dmg, calcDmgCtx);
          lastNode.AddRight(AttackFlowEnum.Dmg_Target, calcDmgCtx);
        }
        if (treasure.targetStatus != null && treasure.targetStatus.Count > 0)
        {
          SetStateCtx setStateCtx = new(ctx.NowTarget, true, treasure.targetStatus, null);
          lastNode = lastNode.AddChild(AttackFlowEnum.Set_State, setStateCtx);
        }
        lastNode.AddChild(AttackFlowEnum.Proc_Rlt, null);
      }
      ctx.InjectFlow(root);
      return root;
    }

    // 逃跑工作流
    public static AttackFlowNode BuildEscapeFlow(this AttackCtxComp ctx)
    {
      AttackComponent src = ctx.Src;
      ctx.AttackType = AttackType.Escape;
      CheckCondCtx condCtx = new()
      {
        NotSelfState = new List<AttackState>()
        {
          AttackState.ChanRao,
          AttackState.DIZZY,
          AttackState.FROZEN,
          AttackState.Chui_Fei,
          AttackState.KuangFeng_LongZhao,
          AttackState.Bao_Nu
        }
      };
      AttackFlowNode root = AttackFlowNodeFunc.CreateNode(AttackFlowEnum.Check_Pre_Cond, condCtx);
      // 施加逃跑状态
      AttachStatus attachStatus = new()
      {
        state = AttackState.Tao_Pao,
        endTime = TimeInfo.Instance.ServerNow() + 2000,
        existTime = 2000,
        addInfo = src.fightInfo
      };
      SetStateCtx setStateCtx = new(ctx.Src, true, attachStatus, AttackState.None);
      root.AddChild(AttackFlowEnum.Set_State, setStateCtx).AddChild(AttackFlowEnum.Proc_Rlt, null);
      return root;
    }

    // 退出战斗工作流
    public static AttackFlowNode BuildQuitAttackFlow(this AttackCtxComp ctx)
    {
      AttackFlowContext nodeCtx = new();
      nodeCtx.ReplaceTarget = ctx.Src;
      AttackFlowNode root = AttackFlowNodeFunc.CreateNode(AttackFlowEnum.Quit_Attack, nodeCtx);
      root.AddChild(AttackFlowEnum.Proc_Rlt, null);
      return root;
    }

    // 结束战斗工作流
    public static AttackFlowNode BuildEndAttackFlow(this AttackCtxComp ctx)
    {
      AttackFlowNode root = AttackFlowNodeFunc.CreateNode(AttackFlowEnum.End_Attack, new AttackFlowContext());
      root.AddChild(AttackFlowEnum.Proc_Rlt, null);
      return root;
    }
  }
}