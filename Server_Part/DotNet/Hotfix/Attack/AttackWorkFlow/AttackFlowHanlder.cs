using System;
using System.Collections.Generic;
using System.Text;

namespace MaoYouJi
{
  [Invoke((long)AttackFlowEnum.Check_Pre_Cond)]
  public class CheckPreCondHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      CheckCondCtx checkCondCtx = (CheckCondCtx)args.Node.NodeCtx;
      AttackCtxComp flowComp = ThreadHelper.GetAttackCtx();
      AttackComponent realSrc = checkCondCtx.ReplaceSrc == null ? flowComp.Src : checkCondCtx.ReplaceSrc;
      AttackComponent realTarget = checkCondCtx.ReplaceTarget == null ? flowComp.NowTarget : checkCondCtx.ReplaceTarget;
      InFightComponent srcInFight = realSrc.GetComponent<InFightComponent>();
      InFightComponent targetInFight = realTarget.GetComponent<InFightComponent>();
      // 需要自己满足条件才能进行的操作
      if (checkCondCtx.NeedSelfState != null && checkCondCtx.NeedSelfState.Count > 0)
      {
        foreach (AttackState state in checkCondCtx.NeedSelfState)
        {
          if (!srcInFight.HasState(state))
          {
            return LogicRet.Failed(state + "状态下才能进行该操作");
          }
        }
      }
      // 不满足条件才能进行的操作
      if (checkCondCtx.NotSelfState != null && checkCondCtx.NotSelfState.Count > 0)
      {
        foreach (AttackState state in checkCondCtx.NotSelfState)
        {
          if (srcInFight.HasState(state))
          {
            return LogicRet.Failed(state + "状态下无法进行该操作");
          }
        }
      }
      // 需要目标满足条件才能进行的操作
      if (checkCondCtx.NeedTargetState != null && checkCondCtx.NeedTargetState.Count > 0)
      {
        foreach (AttackState state in checkCondCtx.NeedTargetState)
        {
          if (!targetInFight.HasState(state))
          {
            return LogicRet.Failed("目标无" + state + "状态");
          }
        }
      }
      // 层数达到多少层才能进行的操作
      if (checkCondCtx.MinSelfStateNum != null && checkCondCtx.MinSelfStateNum.Count > 0)
      {
        foreach (var entry in checkCondCtx.MinSelfStateNum)
        {
          if (!srcInFight.HasStateNum(entry.Key, entry.Value))
          {
            return LogicRet.Failed("需要" + entry.Key + "状态" + entry.Value + "层");
          }
        }
      }
      // 蓝量满足
      if (checkCondCtx.BlueExpend > 0)
      {
        if (realSrc.blue < checkCondCtx.BlueExpend)
        {
          return LogicRet.Failed("蓝量不够");
        }
      }
      foreach (CheckCondFunc func in checkCondCtx.UniqCheck)
      {
        LogicRet rlt = func(checkCondCtx);
        if (!rlt.IsSuccess)
        {
          return rlt;
        }
      }
      // 只要是技能类型，释放前一定要打算咏唱
      // if (flowComp.AttackType == AttackType.Skill && flowComp.NowStatus != null)
      // {
      // flowComp.NowStatus.endTime = TimeHelper.ServerNow();
      // }
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Pre_Proc)]
  public class PreProcHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      PreProcCtx preProcCtx = (PreProcCtx)args.Node.NodeCtx;
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      AttackFlowNode node = args.Node;
      AttackComponent realSrc = preProcCtx.ReplaceSrc == null ? attackCtx.Src : preProcCtx.ReplaceSrc;
      AttackComponent realTarget = preProcCtx.ReplaceTarget == null ? attackCtx.NowTarget : preProcCtx.ReplaceTarget;
      InFightComponent srcInFight = realSrc.GetComponent<InFightComponent>();
      InFightComponent targetInFight = realTarget.GetComponent<InFightComponent>();
      List<AttackState> removeList = new();
      if (attackCtx.AttackType == AttackType.Normal)
      {
        // 普通攻击需要移除无畏状态
        if (srcInFight.HasState(AttackState.ZhiMin_YiJi))
        {
          List<AttackFlowContext> calDmgCtxs = node.GetNodeCtxs(AttackFlowEnum.Calc_Dmg);
          if (calDmgCtxs.Count > 0)
          {
            AttachStatus attachStatus = srcInFight.GetState(AttackState.ZhiMin_YiJi);
            foreach (AttackFlowContext calDmgCtx in calDmgCtxs)
            {
              ((CalcDmgCtx)calDmgCtx).AddPercent += attachStatus.val;
            }
          }
          removeList.Add(AttackState.ZhiMin_YiJi);
          // 无畏状态不需要判断命中
          node.RemoveOneChild(AttackFlowEnum.Is_Hit);
        }
        if (srcInFight.HasState(AttackState.ADD_NORMAL))
        {
          List<AttackFlowContext> calDmgCtxs = node.GetNodeCtxs(AttackFlowEnum.Calc_Dmg);
          if (calDmgCtxs.Count > 0)
          {
            AttachStatus attachStatus = srcInFight.GetState(AttackState.ADD_NORMAL);
            foreach (AttackFlowContext calDmgCtx in calDmgCtxs)
            {
              ((CalcDmgCtx)calDmgCtx).AddPercent += attachStatus.val;
            }
          }
          removeList.Add(AttackState.ADD_NORMAL);
        }
      }
      else if (attackCtx.AttackType == AttackType.Skill)
      {
        // 如果释放舍命刺穿要移除横扫状态
        if (srcInFight.HasState(AttackState.Heng_Sao) && attackCtx.Skill != null
            && attackCtx.Skill.skillId == SkillIdEnum.SheMin_ChuanCi)
        {
          removeList.Add(AttackState.Heng_Sao);
        }
        // 技能释放需要移除法术无畏状态
        if (srcInFight.HasState(AttackState.MoFa_AoYi))
        {
          removeList.Add(AttackState.MoFa_AoYi);
          // 觉醒状态不需要判断命中
          node.RemoveOneChild(AttackFlowEnum.Is_Hit);
        }
        // 有伤害的技能造成伤害时计算加成
        if (srcInFight.HasState(AttackState.ADD_MAGIC) && attackCtx.Skill.damage > 0)
        {
          List<AttackFlowContext> calDmgCtxs = node.GetNodeCtxs(AttackFlowEnum.Calc_Dmg);
          if (calDmgCtxs.Count > 0)
          {
            AttachStatus attachStatus = srcInFight.GetState(AttackState.ADD_MAGIC);
            foreach (AttackFlowContext calDmgCtx in calDmgCtxs)
            {
              ((CalcDmgCtx)calDmgCtx).AddPercent += attachStatus.val;
            }
          }
          removeList.Add(AttackState.ADD_MAGIC);
        }
      }
      // 释放技能时，如果目标有逃脱状态，则移除逃脱状态
      if (attackCtx.Skill != null && targetInFight.HasState(AttackState.Tao_Pao))
      {
        removeList.Add(AttackState.Tao_Pao);
      }
      if (removeList.Count > 0)
      {
        SetStateCtx setStateCtx = new(realSrc, false, null, removeList);
        node.AddRight(AttackFlowEnum.Set_State, setStateCtx);
      }
      // 成功释放，处理技能cd
      if (attackCtx.Skill != null)
      {
        SkillComponent skillComponent = realSrc.GetComponent<SkillComponent>();
        long now = TimeInfo.Instance.ServerNow();
        realSrc.blue -= attackCtx.Skill.expend;
        srcInFight.UpdateAttackBloodAndBlue(blueSub: -attackCtx.Skill.expend);
        long coolTime = now + attackCtx.Skill.cd;
        if (skillComponent != null)
        {
          skillComponent.skillCool[attackCtx.Skill.skillId] = coolTime;
          srcInFight.UpdateAttackSkillCool(new() { { attackCtx.Skill.skillId, coolTime } });
          // UseSkillOut useSkillOut = new UseSkillOut();
          // useSkillOut.src = realSrc.getFightInfo();
          // useSkillOut.skillName = ctx.skill.skillId;
          // attackProc.addSendChats(attackCacheProc.getInFightUsers(realSrc, null), useSkillOut);
          // ctx.needSendList.add(realSrc);
        }
      }
      if (attackCtx.Thing != null && realSrc.fightInfo.liveType == LiveType.ROLE)
      {
        BagComponent bagComponent = realSrc.GetParent<User>().GetComponent<BagComponent>();
        // 如果使用物品，需要处理一下
        bagComponent.AddThingNumWithSend(attackCtx.Thing, -1);
      }
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Set_State)]
  public class SetStateHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      SetStateCtx setStateCtx = (SetStateCtx)args.Node.NodeCtx;
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      AttackComponent realSrc = setStateCtx.ReplaceSrc == null ? attackCtx.Src : setStateCtx.ReplaceSrc;
      AttackComponent target = setStateCtx.ReplaceTarget == null ? attackCtx.NowTarget : setStateCtx.ReplaceTarget;
      InFightComponent srcInFight = realSrc.GetComponent<InFightComponent>();
      InFightComponent targetInFight = target.GetComponent<InFightComponent>();
      if (target == null)
      {
        return LogicRet.Success;
      }
      if (target.LiveState != LiveStateEnum.FIGHTING)
      {
        return LogicRet.Success;
      }
      ServerFightChatMsg sendFightChatOut = new();
      sendFightChatOut.type = FightChatType.Set_State;
      sendFightChatOut.src = realSrc != null ? realSrc.fightInfo : null;
      sendFightChatOut.dmgSrc = attackCtx.Skill != null ? attackCtx.Skill.skillId.ToString() : null;
      if (setStateCtx.stateSrc != null)
      {
        sendFightChatOut.src = setStateCtx.stateSrc;
      }
      if (setStateCtx.srcDesc != null)
      {
        sendFightChatOut.dmgSrc = setStateCtx.srcDesc;
      }
      sendFightChatOut.isAuto = setStateCtx.isAutoRemove;
      // 遍历设置状态的列表
      // Set<String> userId = new HashSet<>();
      // if (realSrc != null && realSrc.getLiveType() == LiveType.ROLE)
      // {
      //   userId.add(realSrc.getId());
      // }
      // for (FightInfo fightInfo : target.getFightList().values())
      // {
      //   if (fightInfo.liveType == LiveType.ROLE)
      //     userId.add(fightInfo.fightId);
      // }
      // if (target != null && target.getLiveType() == LiveType.ROLE)
      // {
      //   userId.add(target.getId());
      // }
      // target.getAttackInfo().updateTime = System.currentTimeMillis();
      // ctx.needSendList.add(target);
      // UpdateAttackInfo updateAttackInfo = UpdateAttackHelper.getUpdateAttackInfo(target.getAttackInfo());
      if (!setStateCtx.isAdd)
      {
        // 如果是删除状态
        List<AttackState> realCanRemove = new();
        if (setStateCtx.removeList != null && setStateCtx.removeList.Count > 0)
        {
          // 如果指定了删除列表，从删除列表中挑选状态删除
          foreach (AttackState state in setStateCtx.removeList)
          {
            if (targetInFight.HasState(state))
            {
              realCanRemove.Add(state);
            }
          }
        }
        StringBuilder sb = new StringBuilder();
        foreach (AttackState state in realCanRemove)
        {
          sb.Append(EnumDescriptionCache.GetDescription(state) + ",");
          targetInFight.RemoveState(state);
        }
        targetInFight.UpdateAttackState(null, realCanRemove);
        if (sb.Length > 0)
        {
          sendFightChatOut.delStates[target.fightInfo.fightName] = sb.ToString().TrimEnd(',');
        }
      }
      else
      {
        // 正面状态一定要指定列表
        List<AttachStatus> needSendList = new();
        StringBuilder addSb = new(), replaceSb = new();
        AttackStatusComponent targetStatusComp = targetInFight.GetComponent<AttackStatusComponent>();
        foreach (AttachStatus originState in setStateCtx.addList)
        {
          // 深度拷贝一下
          AttachStatus status = (AttachStatus)originState.Clone();
          if (AttackStateHelper.IsNegativeState(status.state))
          {
            // 高级免疫免疫所有负面状态
            if (targetInFight.HasState(AttackState.GaoJi_MianYi))
            {
              continue;
            }
            if (targetInFight.HasState(AttackState.MianYi) && AttackStateHelper.IsMianYiState(status.state))
            {
              continue;
            }
          }
          // 概率状态不施加
          if (status.probability != 0 && status.probability < 1000)
          {
            long radVal = RandomGenerator.RandomNumber(0, 1000);
            if (radVal >= status.probability)
            {
              continue;
            }
          }
          AttachStatus preStatus = targetInFight.GetState(status.state);
          if (preStatus != null)
          {
            OneAttackStatus oneAttackStatus = targetStatusComp.GetChild<OneAttackStatus>((long)status.state);
            replaceSb.Append(EnumDescriptionCache.GetDescription(status.state) + ",");
            if (AttackStateHelper.IsCanStackState(status.state))
            {
              preStatus.num += status.num;
              if (preStatus.maxNum != 0 && preStatus.num > preStatus.maxNum)
                preStatus.num = preStatus.maxNum;
            }
            else
            {
              preStatus.val = Math.Max(status.val, preStatus.val);
            }
            status = preStatus;
            needSendList.Add(status);
            ETLog.Info($"replaceState: {target.fightInfo.fightName} {EnumDescriptionCache.GetDescription(status.state)}");
          }
          else
          {
            targetStatusComp.AddChildWithId<OneAttackStatus, AttachStatus, AttackComponent>((long)status.state, status, realSrc);
            addSb.Append(EnumDescriptionCache.GetDescription(status.state) + ",");
            needSendList.Add(status);
            ETLog.Info($"addState: {target.fightInfo.fightName} {EnumDescriptionCache.GetDescription(status.state)}");
          }
          // if ((preStatus == null && status.existTime > 0)
          //     || (preStatus != null && preStatus.state.canFresh() && preStatus.existTime > 0))
          // {
          //   status.endTime = System.currentTimeMillis() + status.existTime;
          //   attackProc.addStatusTimer(realSrc, target, status);
          // }
          targetInFight.UpdateAttackState(needSendList, null);
        }
        if (addSb.Length > 0)
        {
          sendFightChatOut.addStates[target.fightInfo.fightName] = addSb.ToString().TrimEnd(',');
        }
        if (replaceSb.Length > 0)
        {
          sendFightChatOut.replaceStates[target.fightInfo.fightName] = replaceSb.ToString().TrimEnd(',');
        }
      }
      if (sendFightChatOut.delStates.Count > 0 || sendFightChatOut.addStates.Count > 0
          || sendFightChatOut.replaceStates.Count > 0)
      {
        attackCtx.GetParent<AttackInCache>().SendMessageToAllFightUser(sendFightChatOut);
      }
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Is_Hit)]
  public class IsHitHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      IsHitCtx isHitCtx = (IsHitCtx)args.Node.NodeCtx;
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      AttackComponent realSrc = isHitCtx.ReplaceSrc == null ? attackCtx.Src : isHitCtx.ReplaceSrc;
      AttackComponent realTarget = isHitCtx.ReplaceTarget == null ? attackCtx.NowTarget : isHitCtx.ReplaceTarget;
      InFightComponent srcInFight = realSrc.GetComponent<InFightComponent>();
      InFightComponent targetInFight = realTarget.GetComponent<InFightComponent>();
      isHitCtx.hitRate = srcInFight.GetRealHitRate() + (realSrc.level - realTarget.level) * 5;
      // 舍命刺穿增加命中率
      if (attackCtx.Skill != null && attackCtx.Skill.skillId == SkillIdEnum.SheMin_ChuanCi)
      {
        isHitCtx.hitRate += attackCtx.Skill.vals[0];
      }
      isHitCtx.miss = targetInFight.GetRealMiss();
      // 如果有拦截函数，在这里处理一下
      if (isHitCtx.changeHit != null)
      {
        isHitCtx.changeHit(isHitCtx);
      }
      bool isHit = isHitCtx.hitRate > isHitCtx.miss;
      if (!isHit)
      {
        // 记录伤害信息
        DmgInfo dmgInfo = new DmgInfo();
        dmgInfo.isHit = false;
        dmgInfo.src = realSrc.fightInfo;
        dmgInfo.target = realTarget.fightInfo;
        attackCtx.GetParent<AttackInCache>().SendDmgInfo(dmgInfo, attackCtx.GetSrcName(), attackCtx.AttackType);
        // 未命中，不执行后序的所有操作
        args.Node.RightNode = null;
      }
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Calc_Dmg)]
  public class CalcDmgHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)args.Node.NodeCtx;
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      AttackComponent realSrc = calcDmgCtx.ReplaceSrc == null ? attackCtx.Src : calcDmgCtx.ReplaceSrc;
      AttackComponent realTarget = calcDmgCtx.ReplaceTarget == null ? attackCtx.NowTarget : calcDmgCtx.ReplaceTarget;
      InFightComponent srcInFight = realSrc.GetComponent<InFightComponent>();
      InFightComponent targetInFight = realTarget.GetComponent<InFightComponent>();
      // 先根据攻击力获取基础伤害
      long baseDmg = srcInFight.GetBaseDmg();
      long damgeRange = calcDmgCtx.DmgNum;
      calcDmgCtx.RealDmgNum = srcInFight.GetRealAttack();
      // 如果是百分比技能，百分比放大
      if (calcDmgCtx.IsPercentDmg)
      {
        if (damgeRange == 0)
        {
          calcDmgCtx.InitDmgNum = baseDmg;
        }
        else
        {
          calcDmgCtx.InitDmgNum = baseDmg * damgeRange / 1000;
          calcDmgCtx.RealDmgNum = calcDmgCtx.RealDmgNum * damgeRange / 1000;
        }
      }
      else
      {
        // 否则直接加上额外伤害
        if (calcDmgCtx.IsAddBaseDmg)
        {
          calcDmgCtx.InitDmgNum = baseDmg + damgeRange;
        }
        else
        {
          calcDmgCtx.InitDmgNum = damgeRange;
        }
      }
      calcDmgCtx.InitDmgNum += calcDmgCtx.ExtraDmg;
      // 如果有真实伤害转换，转换一下
      if (calcDmgCtx.TranRealPercent > 0)
      {
        calcDmgCtx.RealDmgNum += calcDmgCtx.InitDmgNum * calcDmgCtx.TranRealPercent / 1000;
        calcDmgCtx.InitDmgNum = calcDmgCtx.InitDmgNum * (1000 - calcDmgCtx.TranRealPercent) / 1000;
      }
      foreach (BeforeCalcDmgInject inject in calcDmgCtx.BeforeCalcDmg)
      {
        inject.procBeforeCalcDmg(calcDmgCtx);
      }
      // 获取最终伤害
      if (calcDmgCtx.InitDmgNum > 0)
        calcDmgCtx.FinalDmgNum = srcInFight.GetFinalDmgNum(targetInFight, calcDmgCtx.InitDmgNum,
            calcDmgCtx.DmgType, calcDmgCtx.AddPercent);
      // 如果有特殊，按比例缩放计算
      if (calcDmgCtx.FinalDmgPercent != 1000)
      {
        calcDmgCtx.FinalDmgNum = calcDmgCtx.FinalDmgNum * calcDmgCtx.FinalDmgPercent / 1000;
        calcDmgCtx.RealDmgNum = calcDmgCtx.RealDmgNum * calcDmgCtx.FinalDmgPercent / 1000;
      }
      if (calcDmgCtx.CanHeavyHit && srcInFight.CanCrit(targetInFight))
      {
        calcDmgCtx.FinalDmgNum = srcInFight.GetCritDmg(targetInFight, calcDmgCtx.FinalDmgNum);
        calcDmgCtx.HasCrit = true;
      }
      // 加上真实伤害
      calcDmgCtx.FinalDmgNum += calcDmgCtx.RealDmgNum;
      // 伤害至少为1
      if (calcDmgCtx.FinalDmgNum < 1)
      {
        calcDmgCtx.FinalDmgNum = 1;
      }
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Cure_Target)]
  public class CureTargetHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      CureTargetCtx cureTargetCtx = (CureTargetCtx)args.Node.NodeCtx;
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      AttackComponent realSrc = cureTargetCtx.ReplaceSrc == null ? attackCtx.Src : cureTargetCtx.ReplaceSrc;
      AttackComponent realTarget = cureTargetCtx.ReplaceTarget == null ? attackCtx.NowTarget : cureTargetCtx.ReplaceTarget;
      InFightComponent srcInFight = realSrc.GetComponent<InFightComponent>();
      InFightComponent targetInFight = realTarget.GetComponent<InFightComponent>();
      string dmgSrc = attackCtx.GetSrcName();
      if (cureTargetCtx.cureHpNum <= 0 && cureTargetCtx.cureMpNum <= 0)
      {
        return LogicRet.Success;
      }
      long cureHpNum = cureTargetCtx.cureHpNum, cureMpNum = cureTargetCtx.cureMpNum;
      if (cureTargetCtx.cureType == CureType.HP)
      {
        cureHpNum = Math.Min(cureHpNum, realTarget.maxBlood - realTarget.blood);
        realTarget.blood += cureHpNum;
        targetInFight.UpdateAttackBloodAndBlue(bloodSub: cureHpNum);
      }
      else if (cureTargetCtx.cureType == CureType.MP)
      {
        cureMpNum = Math.Min(cureMpNum, realTarget.maxBlue - realTarget.blue);
        realTarget.blue += cureMpNum;
        targetInFight.UpdateAttackBloodAndBlue(blueSub: cureMpNum);
      }
      else
      {
        cureHpNum = Math.Min(cureHpNum, realTarget.maxBlood - realTarget.blood);
        realTarget.blood += cureHpNum;
        cureMpNum = Math.Min(cureMpNum, realTarget.maxBlue - realTarget.blue);
        realTarget.blue += cureMpNum;
        targetInFight.UpdateAttackBloodAndBlue(cureHpNum, cureMpNum);
      }
      attackCtx.GetParent<AttackInCache>().SendCureInfo(cureHpNum, cureMpNum, dmgSrc, cureTargetCtx.cureType, realSrc, realTarget);
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Dmg_Target)]
  public class DmgTargetHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)args.Node.NodeCtx;
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      AttackComponent realSrc = calcDmgCtx.ReplaceSrc == null ? attackCtx.Src : calcDmgCtx.ReplaceSrc;
      AttackComponent realTarget = calcDmgCtx.ReplaceTarget == null ? attackCtx.NowTarget : calcDmgCtx.ReplaceTarget;
      InFightComponent srcInFight = realSrc.GetComponent<InFightComponent>();
      InFightComponent targetInFight = realTarget.GetComponent<InFightComponent>();
      string dmgSrc = attackCtx.GetSrcName();
      // 拦截函数处理一下
      if (calcDmgCtx.BeforExecDmg.Count > 0)
      {
        foreach (BeforeExecDmgInject inject in calcDmgCtx.BeforExecDmg)
        {
          if (!inject.procBeforeExecDmg(calcDmgCtx).IsSuccess)
          {
            return LogicRet.Success;
          }
        }
      }
      long preBlood = realTarget.blood; // 记录伤害前的目标血量
                                        // 检查目标是否在战斗中，若不在，则不执行伤害计算，记录重要日志，因为这种逻辑不应该存在
      if (realTarget.LiveState != LiveStateEnum.FIGHTING)
      {
        ETLog.Error($"not in fight target, src: {realSrc.fightInfo.fightId}, target: {realTarget.fightInfo.fightId}, liveState: {realTarget.LiveState}");
        return LogicRet.Success;
      }
      if (calcDmgCtx.ExecDmg != null)
        calcDmgCtx.ExecDmg.execDmg(calcDmgCtx);
      else
        attackCtx.DmgTarget(realSrc, calcDmgCtx);
      // 伤害过高，记录日志
      if (calcDmgCtx.FinalDmgNum > realSrc.level * 10)
      {
        // StringBuilder sb = new StringBuilder();
        // sb.append(ComUtil.simpleId(realSrc.fightInfo.fightId)).append("-");
        // sb.append(ComUtil.simpleId(targetInfo.fightInfo.fightId)).append("-");
        // sb.append(calcDmgCtx.finalDmgNum).append("-");
        // sb.append(srcInfo.level).append("|");
        // sb.append(srcInfo.attackState.keySet()).append("|");
        // sb.append(targetInfo.attackState.keySet()).append("|");
        // LogUtil.LevelInfo(10, "dmgTarget", sb.toString());
      }

      // 检查目标血量是否小于等于0，若是，则标记为被击杀，并将血量设为0
      if (realTarget.blood <= 0)
      {
        realTarget.AddComponent<KilledComponent, AttackComponent>(realSrc);
        calcDmgCtx.Killed = true;
      }
      if (calcDmgCtx.AfterDmg.Count > 0)
      {
        foreach (AfterDmgInject inject in calcDmgCtx.AfterDmg)
        {
          inject.procAfterDmg(calcDmgCtx);
        }
      }
      // ctx.needSendList.add(calcDmgCtx.target);
      // // 记录伤害信息
      DmgInfo dmgInfo = new DmgInfo
      {
        dmgNum = calcDmgCtx.FinalDmgNum,
        defendNum = calcDmgCtx.DefendNum,
        isHit = true,
        realNum = calcDmgCtx.RealDmgNum,
        killed = calcDmgCtx.Killed,
        src = realSrc.fightInfo,
        target = realTarget.fightInfo,
        dmgType = calcDmgCtx.DmgType,
        hasCrit = calcDmgCtx.HasCrit
      };
      AttackType attckType = attackCtx.AttackType;
      if (attackCtx.Skill != null && attackCtx.AttackType == AttackType.Normal)
      {
        attckType = AttackType.Skill;
      }
      attackCtx.GetParent<AttackInCache>().SendDmgInfo(dmgInfo, dmgSrc, attckType);
      // ctx.dmgList.add(dmgInfo);
      // 如果真的击杀了，增加击杀和退出战斗的工作流节点
      if (calcDmgCtx.Killed)
      {
        args.Node.AddRight(AttackFlowEnum.Kill_Target, calcDmgCtx);
      }
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Kill_Target)]
  public class KillTargetHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      AttackFlowContext killTargetCtx = args.Node.NodeCtx;
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      AttackComponent realSrc = killTargetCtx.ReplaceSrc == null ? attackCtx.Src : killTargetCtx.ReplaceSrc;
      AttackComponent realTarget = killTargetCtx.ReplaceTarget == null ? attackCtx.NowTarget : killTargetCtx.ReplaceTarget;
      InFightComponent srcInFight = realSrc.GetComponent<InFightComponent>();
      InFightComponent targetInFight = realTarget.GetComponent<InFightComponent>();
      AttackFlowNode node = args.Node;
      SkillComponent skillComp = realSrc.GetComponent<SkillComponent>();
      // 伏击特殊处理
      if (attackCtx.Skill != null && attackCtx.Skill.skillId == SkillIdEnum.Fu_Ji)
      {
        Skill equipSkill = skillComp.GetSkill(SkillIdEnum.Fu_Ji);
        if (equipSkill.damage == attackCtx.Skill.damage)
        {
          long readyTime = TimeInfo.Instance.ServerNow() + equipSkill.cd / 2;
          skillComp.skillCool[SkillIdEnum.Fu_Ji] = readyTime;
          srcInFight.UpdateAttackSkillCool(new() { { SkillIdEnum.Fu_Ji, readyTime } });
        }
      }
      targetInFight.UpdateAttackKilled(realSrc.fightInfo, TimeInfo.Instance.ServerNow());
      attackCtx.GetParent<AttackInCache>().SendKillInfo(realSrc.fightInfo, realTarget.fightInfo, realTarget.Parent.GetParent<MapNode>());
      // 退出战斗
      node.AddChild(AttackFlowEnum.Quit_Attack, killTargetCtx);
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Quit_Attack)]
  public class QuitAttackHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      AttackFlowContext quitAttackCtx = args.Node.NodeCtx;
      AttackComponent realTarget = quitAttackCtx.ReplaceTarget;
      // 退出战斗，移除战斗组件
      realTarget.RemoveComponent<InFightComponent>();
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.End_Attack)]
  public class EndAttackHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      attackCtx.GetParent<AttackInCache>().EndAttack();
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Self_Define)]
  public class SelfDefineHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      AttackFlowNode node = args.Node;
      SelfDefineCtx selfDefineCtx = (SelfDefineCtx)node.NodeCtx;
      if (selfDefineCtx.selfDefine == null)
      {
        return LogicRet.Success;
      }
      selfDefineCtx.selfDefine(ThreadHelper.GetAttackCtx());
      return LogicRet.Success;
    }
  }

  [Invoke((long)AttackFlowEnum.Proc_Rlt)]
  public class ProcAttackCtxHandler : AInvokeHandler<AttackFlowExec, LogicRet>
  {
    public override LogicRet Handle(AttackFlowExec args)
    {
      AttackCtxComp attackCtx = ThreadHelper.GetAttackCtx();
      attackCtx.ProcAttackCtx();
      return LogicRet.Success;
    }
  }
}