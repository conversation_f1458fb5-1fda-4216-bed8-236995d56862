using System.Collections.Generic;
using Microsoft.CodeAnalysis.CSharp.Syntax;

namespace MaoYouJi
{
  public static class AttackFlowNodeFunc
  {
    public static AttackFlowNode CreateNode(AttackFlowEnum type, AttackFlowContext ctx)
    {
      ctx ??= new AttackFlowContext();
      switch (type)
      {
        case AttackFlowEnum.Check_Pre_Cond:
          return new CheckPreCondNode((CheckCondCtx)ctx);
        case AttackFlowEnum.Start_Song:
          return new StartSongNode((StartSongCtx)ctx);
        case AttackFlowEnum.Pre_Proc:
          return new PreProcNode((PreProcCtx)ctx);
        case AttackFlowEnum.Change_Target:
          return new ChangeTargetNode((ChangeTargetCtx)ctx);
        case AttackFlowEnum.Set_State:
          return new SetStateNode((SetStateCtx)ctx);
        case AttackFlowEnum.Is_Hit:
          return new IsHitNode((IsHitCtx)ctx);
        case AttackFlowEnum.Calc_Dmg:
          return new CalcDmgNode((CalcDmgCtx)ctx);
        case AttackFlowEnum.Dmg_Target:
          return new DmgTargetNode((CalcDmgCtx)ctx);
        case AttackFlowEnum.Cure_Target:
          return new CureTargetNode((CureTargetCtx)ctx);
        case AttackFlowEnum.Kill_Target:
          return new KillTargetNode((CalcDmgCtx)ctx);
        case AttackFlowEnum.Quit_Attack:
          return new QuitAttackNode(ctx);
        case AttackFlowEnum.End_Attack:
          return new EndAttackNode(ctx);
        case AttackFlowEnum.Self_Define:
          return new SelfDefineNode((SelfDefineCtx)ctx);
        case AttackFlowEnum.Proc_Rlt:
          return new ProcRltNode(ctx);
        default:
          return null;
      }
    }

    private static void CollectNodes(this AttackFlowNode node, List<AttackFlowNode> allNodes)
    {
      if (node == null || allNodes.Contains(node))
        return;

      // 添加当前节点
      allNodes.Add(node);

      // 递归访问子节点
      node.ChildNode.CollectNodes(allNodes);

      // 递归访问右侧节点
      node.RightNode.CollectNodes(allNodes);
    }

    public static List<AttackFlowNode> GetAllNode(this AttackFlowNode node)
    {
      List<AttackFlowNode> allNodes = new();
      if (node == null)
        return allNodes;

      // 使用递归确保访问每个节点
      node.CollectNodes(allNodes);
      return allNodes;
    }

    public static List<AttackFlowContext> GetNodeCtxs(this AttackFlowNode node, AttackFlowEnum type)
    {
      List<AttackFlowContext> baseWorkCtxs = new();
      List<AttackFlowNode> allNodes = node.GetAllNode();
      foreach (AttackFlowNode n in allNodes)
      {
        if (n.FlowType == type)
        {
          baseWorkCtxs.Add(n.NodeCtx);
        }
      }
      return baseWorkCtxs;
    }

    public static void RemoveOneChild(this AttackFlowNode node, params AttackFlowEnum[] types)
    {
      HashSet<AttackFlowEnum> set = new(types);
      AttackFlowNode next = node.ChildNode;
      while (next != null)
      {
        if (!set.Contains(next.FlowType))
        {
          next = next.ChildNode;
          continue;
        }
        if (next.RightNode != null)
        {
          next.ParentNode.ChildNode = next.RightNode;
          next.RightNode.ChildNode = next.ChildNode;
          if (next.ChildNode != null)
          {
            next.ChildNode.ParentNode = next.RightNode;
          }
        }
        else
        {
          next.ParentNode.ChildNode = next.ChildNode;
          if (next.ChildNode != null)
          {
            next.ChildNode.ParentNode = next.ParentNode;
          }
        }
        next = next.ChildNode;
      }
    }

    public static AttackFlowNode AddRight(this AttackFlowNode node, AttackFlowEnum subType, AttackFlowContext ctx)
    {
      AttackFlowNode newNode = CreateNode(subType, ctx);
      newNode.ParentNode = node.ParentNode;
      newNode.LeftNode = node;
      if (node.RightNode != null)
      {
        node.RightNode.LeftNode = newNode;
        newNode.RightNode = node.RightNode;
      }
      node.RightNode = newNode;
      return newNode;
    }

    public static AttackFlowNode AddChild(this AttackFlowNode node, AttackFlowEnum subType, AttackFlowContext ctx)
    {
      AttackFlowNode newNode = CreateNode(subType, ctx);
      newNode.ParentNode = node;
      if (node.ChildNode != null)
      {
        node.ChildNode.ParentNode = newNode;
        newNode.ChildNode = node.ChildNode;
      }
      node.ChildNode = newNode;
      return newNode;
    }

    public static AttackFlowNode GetFirstChild(this AttackFlowNode node, AttackFlowEnum type)
    {
      if (node.FlowType == type)
        return node;
      AttackFlowNode child = node.ChildNode;
      while (child != null)
      {
        if (child.FlowType == type)
        {
          return child;
        }
        AttackFlowNode right = child.RightNode;
        while (right != null)
        {
          if (right.FlowType == type)
          {
            return right;
          }
          right = right.RightNode;
        }
        child = child.ChildNode;
      }
      return null;
    }

    public static AttackFlowNode GetLastChild(this AttackFlowNode node, AttackFlowEnum type)
    {
      AttackFlowNode lastNode = null;
      if (node.FlowType == type)
        lastNode = node;
      AttackFlowNode child = node.ChildNode;
      while (child != null)
      {
        if (child.FlowType == type)
        {
          lastNode = child;
        }
        AttackFlowNode right = child.RightNode;
        while (right != null)
        {
          if (right.FlowType == type)
          {
            lastNode = right;
          }
          right = right.RightNode;
        }
        child = child.ChildNode;
      }
      return lastNode;
    }

    public static List<AttackFlowNode> GetAllChild(this AttackFlowNode node, AttackFlowEnum type)
    {
      List<AttackFlowNode> matchNodes = new();
      AttackFlowNode child = node.ChildNode;
      while (child != null)
      {
        if (child.FlowType == type)
        {
          matchNodes.Add(child);
        }
        AttackFlowNode right = child.RightNode;
        while (right != null)
        {
          if (right.FlowType == type)
          {
            matchNodes.Add(right);
          }
          right = right.RightNode;
        }
        child = child.ChildNode;
      }
      return matchNodes;
    }
  }
}