using System;

namespace MaoYouJi
{
  [Invoke((long)AttackState.ShenDong_ZhiHan)] // 注入Pre_Proc节点中
  public class ShenDongZhiHanSelfStateInjectInvoker : AInvokeHandler<SelfStateInjectInfo>
  {
    public override void Handle(SelfStateInjectInfo info)
    {
      AttackCtxComp ctx = info.AttackCtx;
      // 深冬之寒，每释放一次冰术士的技能，叠加一层
      if (ctx.Skill != null && ctx.Skill.job == MaoJob.Bin_ShuShi)
      {
        InFightComponent srcInFight = ctx.Src.Get().GetComponent<InFightComponent>();
        AttachStatus preStatus = srcInFight.GetState(AttackState.ShenDong_ZhiHan);
        AttachStatus newStatus = preStatus.Clone() as AttachStatus;
        newStatus.num = 1; // 这里为1是因为这个状态可叠加，每次叠加一层就行了，倒时会自动移除的
        newStatus.endTime = 0;
        SetStateCtx setStateCtx = new(ctx.Src.Get(), true, newStatus, AttackState.None);
        ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, setStateCtx);
      }
    }
  }
}