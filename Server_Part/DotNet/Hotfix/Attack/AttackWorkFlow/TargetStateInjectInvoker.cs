using System;

namespace MaoYouJi
{
  [Invoke((long)AttackState.Chui_Fei)]
  public class ChuiFeiTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      AttackCtxComp ctx = info.AttackCtx;
      if (ctx.AttackType == AttackType.Normal)
      {
        BeforeExecDmgInject inject = new()
        {
          priority = 10,
          procBeforeExecDmg = (dmgCtx) =>
          {
            // 普通攻击不造成伤害
            dmgCtx.FinalDmgNum = 0;
            dmgCtx.RealDmgNum = 0;
            return LogicRet.Success;
          }
        };
        calcDmgCtx.AddBeforeExecDmgInject(inject);
      }
      else if (ctx.AttackType == AttackType.Skill)
      {
        AfterDmgInject inject = new()
        {
          priority = 1,
          procAfterDmg = (dmgCtx) =>
          {
            // 造成伤害后移除吹飞状态
            if (dmgCtx.FinalDmgNum > 0)
            {
              SetStateCtx removeStateCtx = new(calcDmgCtx.ReplaceTarget, false, null, AttackState.Chui_Fei);
              ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, removeStateCtx);
            }
            return LogicRet.Success;
          }
        };
        calcDmgCtx.AddAfterDmgInject(inject);
      }
    }
  }

  [Invoke((long)AttackState.FROZEN)]
  public class FrozenTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      BeforeExecDmgInject inject = new()
      {
        priority = 1000, // 冻结状态优先级最高
        procBeforeExecDmg = (dmgCtx) =>
        {
          dmgCtx.FinalDmgNum = 0;
          dmgCtx.RealDmgNum = 0;
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddBeforeExecDmgInject(inject);
    }
  }

  [Invoke((long)AttackState.KongShouRu_BaiRen)]
  public class KongShouRuBaiRenTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      AttackCtxComp ctx = info.AttackCtx;
      AttackComponent src = ctx.Src.Get();
      AttackComponent target = calcDmgCtx.ReplaceTarget;
      InFightComponent srcInFight = src.GetComponent<InFightComponent>();
      InFightComponent targetInFight = target.GetComponent<InFightComponent>();
      BeforeExecDmgInject inject = new()
      {
        priority = 2,
        procBeforeExecDmg = (dmgCtx) =>
        {
          AttachStatus kongShou = targetInFight.GetState(AttackState.KongShouRu_BaiRen);
          if (kongShou != null && ctx.Skill == null && ctx.AttackType == AttackType.Normal && dmgCtx.FinalDmgNum > 0)
          {
            AttachStatus attachStatus = new AttachStatus();
            string dmgSrc = "普通攻击";
            attachStatus.existTime = kongShou.val;
            attachStatus.state = AttackState.TAKE_WEAPON;
            SetStateCtx setStateCtx = new(ctx.Src, true, attachStatus, AttackState.None);
            SetStateCtx removeStateCtx = new(calcDmgCtx.ReplaceTarget, false, null, kongShou.state);
            setStateCtx.srcDesc = EnumDescriptionCache.GetDescription(kongShou.state);
            setStateCtx.stateSrc = target.fightInfo;
            ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, setStateCtx).AddRight(AttackFlowEnum.Set_State, removeStateCtx);
            ctx.GetParent<AttackInCache>().SendDefendInfo(src, target, dmgSrc, EnumDescriptionCache.GetDescription(kongShou.state), ctx.AttackType);
            return LogicRet.Failed("普通攻击被格挡");
          }
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddBeforeExecDmgInject(inject);
    }
  }

  [Invoke((long)AttackState.KongShouRu_BaiRen2)]
  public class KongShouRuBaiRen2TargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      AttackCtxComp ctx = info.AttackCtx;
      AttackComponent src = ctx.Src.Get();
      AttackComponent target = calcDmgCtx.ReplaceTarget;
      InFightComponent srcInFight = src.GetComponent<InFightComponent>();
      InFightComponent targetInFight = target.GetComponent<InFightComponent>();
      BeforeExecDmgInject inject = new()
      {
        priority = 2,
        procBeforeExecDmg = (dmgCtx) =>
        {
          AttachStatus kongShou2 = targetInFight.GetState(AttackState.KongShouRu_BaiRen2);
          if (kongShou2 != null && ctx.Skill != null && ctx.AttackType == AttackType.Skill && dmgCtx.FinalDmgNum > 0)
          {
            AttachStatus attachStatus = new AttachStatus();
            string dmgSrc = ctx.Skill != null ? ctx.Skill.name : "普通攻击";
            attachStatus.existTime = kongShou2.val;
            attachStatus.state = AttackState.SILENT;
            SetStateCtx setStateCtx = new(src, true, attachStatus, AttackState.None);
            SetStateCtx removeStateCtx = new(target, false, null, kongShou2.state);
            setStateCtx.srcDesc = EnumDescriptionCache.GetDescription(kongShou2.state);
            setStateCtx.stateSrc = target.fightInfo;
            ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, setStateCtx).AddRight(AttackFlowEnum.Set_State, removeStateCtx);
            ctx.GetParent<AttackInCache>().SendDefendInfo(src, target, dmgSrc, EnumDescriptionCache.GetDescription(kongShou2.state), ctx.AttackType);
            return LogicRet.Failed("技能攻击被格挡");
          }
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddBeforeExecDmgInject(inject);
    }
  }

  [Invoke((long)AttackState.MoFa_Dun)]
  public class MoFaDunTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      AttackCtxComp ctx = info.AttackCtx;
      ExecDmgInject inject = new()
      {
        priority = 1,
        execDmg = (dmgCtx) =>
        {
          AttackComponent target = dmgCtx.ReplaceTarget;
          InFightComponent targetInFight = target.GetComponent<InFightComponent>();
          AttachStatus mofaDun = targetInFight.GetState(AttackState.MoFa_Dun);
          bool needCancelDun = false;
          long preNum = dmgCtx.FinalDmgNum - dmgCtx.RealDmgNum; // 抵抗前的伤害
          long remainNum = 0; // 剩余伤害
          long blueDefendNum = 0; // 抵抗的伤害
          long maxNum = target.blue * mofaDun.val / 100; // 最大抵抗伤害
          // 魔法盾能抗的伤害受蓝量和剩余层数限制
          if (mofaDun.num > preNum && maxNum > preNum)
          {
            blueDefendNum = preNum; // 抵抗的伤害等于抵抗前的伤害
          }
          else
          {
            blueDefendNum = Math.Min(mofaDun.num, maxNum);
          }
          calcDmgCtx.DefendNum = blueDefendNum;
          remainNum = preNum - blueDefendNum;
          mofaDun.num -= blueDefendNum;
          if (mofaDun.num == 0)
          {
            needCancelDun = true;
          }
          long realBlueDefendNum = blueDefendNum * 100 / mofaDun.val;
          target.blue -= realBlueDefendNum;
          if (target.blue <= 0)
          {
            needCancelDun = true;
            target.blue = 0;
          }
          remainNum += dmgCtx.RealDmgNum;
          target.blood -= remainNum; // 执行伤害扣除
          targetInFight.UpdateAttackBloodAndBlue(-remainNum, -realBlueDefendNum);
          if (target.blood > 0 && needCancelDun)
          {
            SetStateCtx setStateCtx = new(target, false, null, AttackState.MoFa_Dun);
            ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, setStateCtx);
          }
          else
          {
            targetInFight.UpdateAttackState(mofaDun, AttackState.None);
          }
        }
      };
      calcDmgCtx.SetExecDmgInject(inject);
    }
  }

  [Invoke((long)AttackState.LieYan_ZhuoShao)]
  public class LieYanZhuoShaoTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      AttackCtxComp ctx = info.AttackCtx;
      BeforeCalcDmgInject inject = new()
      {
        priority = 1,
        procBeforeCalcDmg = (dmgCtx) =>
        {
          if (ctx.Skill != null && ctx.Skill.job == MaoJob.Yan_ShuShi)
          {
            AttackComponent target = dmgCtx.ReplaceTarget;
            InFightComponent targetInFight = target.GetComponent<InFightComponent>();
            AttachStatus lieYanZhuoShao = targetInFight.GetState(AttackState.LieYan_ZhuoShao);
            dmgCtx.AddPercent += lieYanZhuoShao.val * lieYanZhuoShao.num;
          }
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddBeforeCalcDmgInject(inject);
    }
  }

  [Invoke((long)AttackState.HuoYan_PinZhang)]
  public class HuoYanPinZhangTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      ExecDmgInject inject = new()
      {
        priority = 2, // 火焰屏障优先级大于魔法盾
        execDmg = (dmgCtx) =>
        {
          // 护盾抵抗伤害
          AttackComponent target = dmgCtx.ReplaceTarget;
          InFightComponent targetInFight = target.GetComponent<InFightComponent>();
          AttachStatus HuoYanPinZhang = targetInFight.GetState(AttackState.HuoYan_PinZhang);
          long preNum = dmgCtx.FinalDmgNum - dmgCtx.RealDmgNum;
          calcDmgCtx.DefendNum = preNum;
          HuoYanPinZhang.num += preNum;
          target.blood -= dmgCtx.RealDmgNum;
          targetInFight.UpdateAttackBloodAndBlue(-dmgCtx.RealDmgNum, 0);
          targetInFight.UpdateAttackState(HuoYanPinZhang, AttackState.None);
        }
      };
      calcDmgCtx.SetExecDmgInject(inject);
    }
  }

  [Invoke((long)AttackState.TongQiang_TieBi)]
  public class TongQiangTieBiTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      ExecDmgInject inject = new()
      {
        priority = 3, // 铜墙铁壁优先级大于火焰屏障
        execDmg = (dmgCtx) =>
        {
          // 护盾抵抗伤害
          AttackComponent target = dmgCtx.ReplaceTarget;
          InFightComponent targetInFight = target.GetComponent<InFightComponent>();
          target.blood -= 1;
          dmgCtx.FinalDmgNum = 1;
          dmgCtx.RealDmgNum = 0;
          targetInFight.UpdateAttackBloodAndBlue(-1, 0);
        }
      };
      calcDmgCtx.SetExecDmgInject(inject);
    }
  }

  [Invoke((long)AttackState.Jie_Jing)]
  public class JieJingTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      AttackCtxComp ctx = info.AttackCtx;
      BeforeExecDmgInject inject = new()
      {
        priority = 1000, // 结晶状态优先级最高
        procBeforeExecDmg = (dmgCtx) =>
        {
          if (ctx.AttackType == AttackType.Skill)
          {
            // 免疫技能攻击
            dmgCtx.FinalDmgNum = 0;
            dmgCtx.RealDmgNum = 0;
            SetStateCtx setStateCtx = new(dmgCtx.ReplaceTarget, false, null, AttackState.Jie_Jing);
            ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, setStateCtx);
            return LogicRet.Failed("免疫技能攻击");
          }
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddBeforeExecDmgInject(inject);
    }
  }

  [Invoke((long)AttackState.DaPeng_ZhanChi)]
  public class DaPengZhanChiTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      ExecDmgInject inject = new()
      {
        priority = 4, // 大鹏展翅高于铜墙铁壁
        execDmg = (dmgCtx) =>
        {
          // 吸收所有伤害
          dmgCtx.FinalDmgNum = 0;
          dmgCtx.RealDmgNum = 0;
          dmgCtx.HasCrit = false;
        }
      };
      calcDmgCtx.SetExecDmgInject(inject);
    }
  }

  [Invoke((long)AttackState.HaiYao_ZhiGe)]
  public class HaiYaoZhiGeTargetStateInjectInvoker : AInvokeHandler<TargetStateInjectInfo>
  {
    public override void Handle(TargetStateInjectInfo info)
    {
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)info.Node.NodeCtx;
      AttackCtxComp ctx = info.AttackCtx;
      ExecDmgInject inject = new()
      {
        priority = 2, // 海妖之歌优先级大于魔法盾
        execDmg = (dmgCtx) =>
        {
          // 吸收伤害
          AttackComponent target = dmgCtx.ReplaceTarget;
          InFightComponent targetInFight = target.GetComponent<InFightComponent>();
          AttachStatus HaiYaoZhiGe = targetInFight.GetState(AttackState.HaiYao_ZhiGe);
          long preNum = dmgCtx.FinalDmgNum - dmgCtx.RealDmgNum;
          calcDmgCtx.DefendNum = preNum;
          HaiYaoZhiGe.num += preNum;
          target.blood -= dmgCtx.RealDmgNum;
          targetInFight.UpdateAttackBloodAndBlue(-dmgCtx.RealDmgNum, 0);
          // 吸收伤害达到上限时，移除状态
          if (HaiYaoZhiGe.num >= HaiYaoZhiGe.val * ctx.Src.Get().level)
          {
            SetStateCtx setStateCtx = new(dmgCtx.ReplaceTarget, false, null, AttackState.HaiYao_ZhiGe);
            ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, setStateCtx);
          }
          else
          {
            targetInFight.UpdateAttackState(HaiYaoZhiGe, AttackState.None);
          }
        }
      };
      calcDmgCtx.SetExecDmgInject(inject);
    }
  }
}