using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  [Invoke((long)SkillIdEnum.WuYing_Jiao)] // 注入 PRE_PROC
  public class WuYingJiaoSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode procNode = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      AttackComponent src = ctx.Src;
      procNode.ChildNode = null;
      AttackComponent nowTarget = info.AttackCtx.NowTarget;
      AttackFlowContext baseWorkCtx = new();
      baseWorkCtx.ReplaceTarget = nowTarget;
      MapNode nowMap = nowTarget.Parent.GetParent<MapNode>();
      SelfDefineCtx selfDefineCtx = new SelfDefineCtx();
      selfDefineCtx.selfDefine = (inCtx) =>
      {
        string content = nowTarget.fightInfo.fightName + "被幽魂蚁后一脚踢飞了！";
        if (nowTarget.fightInfo.liveType == LiveType.ROLE)
        {
          User user = nowTarget.GetParent<User>();
          List<MapNode> mapNodes = MapProcSystem.GetMapNodesFromCache(nowMap.mapName);
          MapNode newNode = RandomGenerator.RandomArray(mapNodes);
          newNode.PutUserInMapNode(user);
          nowMap.RemoveUserInMapNode(user);
          newNode.SendMessageToMapUser(new ServerSendChatMsg
          {
            content = "幽魂蚁后一脚将" + user.nickname + "踢了过来",
            chatType = ChatType.Local_Chat
          });
        }
        nowMap.SendMessageToMapUser(new ServerSendChatMsg
        {
          content = content,
          chatType = ChatType.Local_Chat
        });
        ctx.GetParent<AttackInCache>().SendMessageToAllFightUser(new ServerSendChatMsg
        {
          content = content,
          chatType = ChatType.Fight_Chat
        });
      };
      procNode.AddChild(AttackFlowEnum.Quit_Attack, baseWorkCtx).AddChild(AttackFlowEnum.Proc_Rlt, null)
          .AddChild(AttackFlowEnum.Self_Define, selfDefineCtx);
    }
  }

  [Invoke((long)SkillIdEnum.Boss_AnYin_BaoZha)] // 注入 CHECK_PRE_COND
  public class BossAnYinBaoZhaSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode procNode = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      AttackComponent src = ctx.Src;
      Skill skill = ctx.Skill;
      CheckCondCtx condCtx = (CheckCondCtx)procNode.NodeCtx;
      condCtx.UniqCheck.Add((atkCtx) =>
      {
        if (src.blood <= skill.vals[0])
        {
          return LogicRet.Failed("您的血量不够释放此技能");
        }
        return LogicRet.Success;
      });
      AttackFlowNode node = procNode.GetFirstChild(AttackFlowEnum.Calc_Dmg);
      if (node == null)
        return;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      AfterDmgInject inject = new()
      {
        priority = 1,
        procAfterDmg = (dmgCtx) =>
        {
          // 造成伤害后扣除血量
          long bloodSub = Math.Min(src.blood - 1, skill.vals[0]);
          src.blood -= bloodSub;
          src.GetComponent<InFightComponent>().UpdateAttackBloodAndBlue(-bloodSub);
          return LogicRet.Success;
        },
      };
      calcDmgCtx.AddAfterDmgInject(inject);
    }
  }

  [Invoke((long)SkillIdEnum.Boss_SiWang_ChanRao)] // 注入 CALC_DMG
  public class BossSiWangChanRaoSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode node = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      AttackComponent src = ctx.Src;
      // 造成伤害后恢复血量
      AfterDmgInject inject = new()
      {
        priority = 1,
        procAfterDmg = (dmgCtx) =>
        {
          CureTargetCtx cureTargetCtx = new()
          {
            cureHpNum = Math.Min(src.maxBlood - src.blood, ctx.Skill.vals[0]),
            cureType = CureType.HP,
            ReplaceTarget = src
          };
          node.AddChild(AttackFlowEnum.Cure_Target, cureTargetCtx);
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddAfterDmgInject(inject);
    }
  }
}
