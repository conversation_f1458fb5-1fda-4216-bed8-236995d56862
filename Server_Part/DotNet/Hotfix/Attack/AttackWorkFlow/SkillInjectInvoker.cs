using System;

namespace MaoYouJi
{
  public static class SkillInjectInvokerComFunc
  {
    public static void ComDmgByLevel(SkillInjectInfo info)
    {
      AttackFlowNode node = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      // 造成等级*50的伤害
      BeforeExecDmgInject inject = new()
      {
        priority = 1,
        procBeforeExecDmg = (dmgCtx) =>
        {
          AttackComponent src = ctx.Src;
          AttackComponent target = calcDmgCtx.ReplaceTarget;
          InFightComponent srcInFight = src.GetComponent<InFightComponent>();
          InFightComponent targetInFight = target.GetComponent<InFightComponent>();
          dmgCtx.FinalDmgNum = srcInFight.GetFinalDmgNum(targetInFight, src.level * skill.damage, calcDmgCtx.DmgType, calcDmgCtx.AddPercent);
          dmgCtx.RealDmgNum = 0;
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddBeforeExecDmgInject(inject);
    }
  }
  [Invoke((long)SkillIdEnum.QiangLi_DaJi)] // 注入Check_Pre_Cond
  public class QiangLiDaJiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackCtxComp ctx = info.AttackCtx;
      ctx.AttackType = AttackType.Normal; // 强力打击视为普通攻击
    }
  }

  [Invoke((long)SkillIdEnum.Heng_Sao)] // 注入Check_Pre_Cond
  public class HengSaoSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      AttackFlowNode root = info.Node;
      AttackFlowNode procNode = root.GetFirstChild(AttackFlowEnum.Pre_Proc),
          setTargetState = root.GetLastChild(AttackFlowEnum.Set_State);
      SetStateCtx setStateCtx = (SetStateCtx)setTargetState.NodeCtx;
      long randVal = RandomGenerator.RandomNumber(0, 1000);
      // 触发横扫和昏迷，移除划伤效果
      if (randVal < skill.vals[0])
      {
        setStateCtx.RemoveStateList(true, AttackState.Hua_Shang);
      }
      else
      {
        setStateCtx.RemoveStateList(true, AttackState.DIZZY);
        procNode.RightNode = null; // 不给自己施加横扫状态
        AttachStatus huaShang = setStateCtx.GetAddStatus(AttackState.Hua_Shang);
        AttackComponent src = ctx.Src;
        AttackComponent target = ctx.NowTarget;
        InFightComponent targetInFight = target.GetComponent<InFightComponent>();
        SkillComponent srcSkill = src.GetComponent<SkillComponent>();
        Skill baWangQiang = srcSkill?.GetSkill(SkillIdEnum.BaWan_Qiang);
        if (baWangQiang != null)
        {
          huaShang.val += baWangQiang.level * skill.vals[2];
        }
        if (targetInFight.HasState(AttackState.ARMOR_BREAK))
        {
          huaShang.val += huaShang.val * skill.vals[3] / 1000;
        }
      }
    }
  }

  [Invoke((long)SkillIdEnum.Ya_Zhi)] // 注入Check_Pre_Cond
  public class YaZhiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      AttackFlowNode root = info.Node;
      CheckCondCtx condCtx = (CheckCondCtx)root.NodeCtx;
      condCtx.NeedTargetState.Add(AttackState.Si_Lie); // 需要撕裂状态
      AttachStatus siLieStatus = ctx.NowTarget.Get().GetComponent<InFightComponent>().GetState(AttackState.Si_Lie);
      if (siLieStatus != null)
      {
        // 撕裂状态存在，则计算伤害
        int existTime = siLieStatus.GetStatusAddTime();
        long dmgRate = Math.Min(skill.vals[0] * existTime, skill.vals[1]);
        AttackFlowNode node = root.GetFirstChild(AttackFlowEnum.Calc_Dmg);
        if (node == null)
          return;
        CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
        calcDmgCtx.DmgNum += dmgRate;
        calcDmgCtx.TranRealPercent = 1000;
      }
    }
  }

  [Invoke((long)SkillIdEnum.NuFeng_ZhiJi)] // 注入Calc_Dmg
  public class NuFengZhiJiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode node = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      calcDmgCtx.DmgType = DmgType.Magic;
      AfterDmgInject inject = new()
      {
        priority = 1,
        procAfterDmg = (dmgCtx) =>
        {
          // 造成伤害后恢复蓝量
          AttackComponent src = ctx.Src;
          CureTargetCtx cureTargetCtx = new CureTargetCtx();
          cureTargetCtx.cureMpNum = Math.Min(src.maxBlood - src.blood, calcDmgCtx.FinalDmgNum);
          cureTargetCtx.cureType = CureType.MP;
          cureTargetCtx.ReplaceTarget = ctx.Src;
          ctx.NowFlowNode.AddRight(AttackFlowEnum.Cure_Target, cureTargetCtx);
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddAfterDmgInject(inject);
    }
  }

  [Invoke((long)SkillIdEnum.NuFeng_ZhanSha)] // 注入Check_Pre_Cond
  public class NuFengZhanShaSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      AttackFlowNode root = info.Node;
      CheckCondCtx condCtx = (CheckCondCtx)root.NodeCtx;
      condCtx.UniqCheck.Add((inCtx) =>
      {
        AttackComponent targetInfo = ctx.NowTarget;
        // 怒风斩杀要检查血量是否满足要求
        if (targetInfo.blood > (targetInfo.maxBlood * skill.vals[0] / 1000))
        {
          return LogicRet.Failed("敌方的血量不满足斩杀要求");
        }
        return LogicRet.Success;
      });
      // 怒风斩杀需要重新计算伤害，并且将蓝量归0
      AttackFlowNode node = root.GetFirstChild(AttackFlowEnum.Calc_Dmg);
      if (node == null)
        return;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      AttackInCache cache = ctx.GetParent<AttackInCache>();
      BeforeExecDmgInject inject = new()
      {
        priority = 1,
        procBeforeExecDmg = (dmgCtx) =>
        {
          AttackComponent src = ctx.Src;
          AttackComponent target = ctx.NowTarget;
          InFightComponent srcInFight = src.GetComponent<InFightComponent>();
          InFightComponent targetInFight = target.GetComponent<InFightComponent>();
          dmgCtx.FinalDmgNum = srcInFight.GetFinalDmgNum(targetInFight,
              src.blue * skill.vals[0] / 100, DmgType.Physics, calcDmgCtx.AddPercent);
          dmgCtx.RealDmgNum = 0;
          src.blue = 0;
          srcInFight.UpdateAttackBloodAndBlue(0, -src.blue);
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddBeforeExecDmgInject(inject);
    }
  }

  [Invoke((long)SkillIdEnum.MengQin_YiJi)] // 注入Calc_Dmg
  public class MengQinYiJiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode node = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      long rand = RandomGenerator.RandomNumber(0, 1000);
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      calcDmgCtx.IsPercentDmg = true;
      if (rand < skill.vals[0])
      {
        // 触发猛禽一击，计算伤害，部分伤害转化为真实伤害
        calcDmgCtx.DmgNum = skill.vals[1];
        calcDmgCtx.TranRealPercent = skill.vals[2];
      }
      else
      {
        // 未触发猛禽一击，计算伤害，直接三倍伤害
        calcDmgCtx.CanHeavyHit = false;
        calcDmgCtx.DmgNum = 3000;
      }
    }
  }

  [Invoke((long)SkillIdEnum.ZhengYi_ShenPan)] // 注入 Check_Pre_Cond
  public class ZhengYiShenPanSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode root = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      CheckCondCtx condCtx = (CheckCondCtx)root.NodeCtx;
      condCtx.NeedTargetState.Add(AttackState.DIZZY);
      AttackFlowNode node = root.GetFirstChild(AttackFlowEnum.Calc_Dmg);
      if (node == null)
        return;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      AfterDmgInject inject = new()
      {
        priority = 1,
        procAfterDmg = (dmgCtx) =>
        {
          // 造成伤害后移除眩晕状态
          SetStateCtx removeStateCtx = new(ctx.NowTarget, false, null, AttackState.DIZZY);
          ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, removeStateCtx);
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddAfterDmgInject(inject);
    }
  }

  [Invoke((long)SkillIdEnum.GuangMing_ShenPan)] // 注入 Calc_Dmg
  public class GuangMingShenPanSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode node = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      AttackComponent target = calcDmgCtx.ReplaceTarget;
      InFightComponent targetInFight = target.GetComponent<InFightComponent>();
      AttachStatus shenPan = targetInFight.GetState(AttackState.Shen_Pan);
      if (shenPan != null)
      {
        // 根据审判状态计算真实伤害比例
        calcDmgCtx.TranRealPercent = Math.Min(shenPan.num * skill.vals[0], skill.vals[1]);
      }
      AfterDmgInject inject = new()
      {
        priority = 1,
        procAfterDmg = (dmgCtx) =>
        {
          // 造成伤害后移除审判状态
          if (targetInFight.HasState(AttackState.Shen_Pan))
          {
            SetStateCtx removeStateCtx = new(target, false, null, AttackState.Shen_Pan);
            ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, removeStateCtx);
          }
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddAfterDmgInject(inject);
    }

    [Invoke((long)SkillIdEnum.TianFa_ZhiLei)] // 注入Calc_Dmg
    public class TianFaZhiLeiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
    {
      public override void Handle(SkillInjectInfo info)
      {
        AttackFlowNode node = info.Node;
        AttackCtxComp ctx = info.AttackCtx;
        Skill skill = ctx.Skill;
        CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
        InFightComponent targetInFight = calcDmgCtx.ReplaceTarget.GetComponent<InFightComponent>();
        AttachStatus chuiFei = targetInFight.GetState(AttackState.Chui_Fei);
        if (chuiFei != null)
        {
          BeforeCalcDmgInject inject = new()
          {
            priority = 1,
            procBeforeCalcDmg = (dmgCtx) =>
            {
              int addTime = chuiFei.GetStatusAddTime();
              dmgCtx.AddPercent += Math.Min(addTime * skill.vals[0], skill.vals[1]);
              return LogicRet.Success;
            }
          };
          calcDmgCtx.AddBeforeCalcDmgInject(inject);
        }
      }
    }
  }

  [Invoke((long)SkillIdEnum.AnYin_BaoZha)] // 注入Check_Pre_Cond
  public class AnYinBaoZhaSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode root = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      CheckCondCtx condCtx = (CheckCondCtx)root.NodeCtx;
      AttackComponent src = ctx.Src;
      condCtx.UniqCheck.Add((atkCtx) =>
      {
        if (src.blood <= skill.vals[0])
        {
          return LogicRet.Failed("您的血量不够释放此技能");
        }
        return LogicRet.Success;
      });
      AttackFlowNode node = root.GetFirstChild(AttackFlowEnum.Calc_Dmg);
      if (node == null)
        return;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      AfterDmgInject inject = new()
      {
        priority = 1,
        procAfterDmg = (dmgCtx) =>
        {
          // 造成伤害后扣除血量
          long bloodSub = Math.Min(src.blood - 1, skill.vals[0]);
          src.blood -= bloodSub;
          src.GetComponent<InFightComponent>().UpdateAttackBloodAndBlue(-bloodSub, 0);
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddAfterDmgInject(inject);
    }
  }

  [Invoke((long)SkillIdEnum.AnYin_JiaoSha)] // 注入Check_Pre_Cond
  public class AnYinJiaoShaSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode root = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      // 必须有暗影箭才能使用技能
      CheckCondCtx condCtx = (CheckCondCtx)root.NodeCtx;
      condCtx.NeedTargetState.Add(AttackState.AnYin_Jian);
      AttackFlowNode node = root.GetFirstChild(AttackFlowEnum.Calc_Dmg);
      if (node == null)
        return;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      // 造成伤害前，根据暗影箭存在的时间计算增伤
      BeforeCalcDmgInject inject = new()
      {
        priority = 1,
        procBeforeCalcDmg = (dmgCtx) =>
        {
          AttachStatus status = dmgCtx.ReplaceTarget.GetComponent<InFightComponent>().GetState(AttackState.AnYin_Jian);
          if (status == null)
            return LogicRet.Success;
          dmgCtx.AddPercent += Math.Min(skill.vals[1], skill.vals[0] * status.GetStatusAddTime());
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddBeforeCalcDmgInject(inject);
      // 造成伤害后移除暗影箭状态
      AfterDmgInject inject2 = new()
      {
        priority = 1,
        procAfterDmg = (dmgCtx) =>
        {
          SetStateCtx setStateCtx = new(dmgCtx.ReplaceTarget, false, null, AttackState.AnYin_Jian);
          ctx.NowFlowNode.AddRight(AttackFlowEnum.Set_State, setStateCtx);
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddAfterDmgInject(inject2);
    }
  }

  [Invoke((long)SkillIdEnum.SiWang_ChanRao)] // 注入Calc_Dmg
  public class SiWangChanRaoSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode node = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      // 造成伤害后恢复血量
      AfterDmgInject inject = new()
      {
        priority = 1,
        procAfterDmg = (dmgCtx) =>
        {
          AttackComponent src = ctx.Src;
          CureTargetCtx cureTargetCtx = new()
          {
            cureHpNum = Math.Min(src.maxBlood - src.blood, skill.vals[0]),
            cureType = CureType.HP,
            ReplaceTarget = ctx.Src
          };
          ctx.NowFlowNode.AddRight(AttackFlowEnum.Cure_Target, cureTargetCtx);
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddAfterDmgInject(inject);
    }
  }

  [Invoke((long)SkillIdEnum.ShengLong_LieYan)] // 注入Calc_Dmg
  public class ShengLongLieYanSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      SkillInjectInvokerComFunc.ComDmgByLevel(info);
    }
  }

  [Invoke((long)SkillIdEnum.RongYan_ChongJi)] // 注入Calc_Dmg
  public class RongYanChongJiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      SkillInjectInvokerComFunc.ComDmgByLevel(info);
    }
  }

  [Invoke((long)SkillIdEnum.JuLangZhi_Shi)] // 注入Calc_Dmg
  public class JuLangZhiShiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode node = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      CalcDmgCtx calcDmgCtx = (CalcDmgCtx)node.NodeCtx;
      // 造成对方现有生命的百分比伤害
      BeforeExecDmgInject inject = new()
      {
        priority = 1,
        procBeforeExecDmg = (dmgCtx) =>
        {
          AttackComponent src = ctx.Src;
          AttackComponent target = calcDmgCtx.ReplaceTarget;
          InFightComponent srcInFight = src.GetComponent<InFightComponent>();
          InFightComponent targetInFight = target.GetComponent<InFightComponent>();
          dmgCtx.FinalDmgNum = srcInFight.GetFinalDmgNum(targetInFight, target.blood * skill.damage / 1000, calcDmgCtx.DmgType, calcDmgCtx.AddPercent);
          dmgCtx.RealDmgNum = 0;
          return LogicRet.Success;
        }
      };
      calcDmgCtx.AddBeforeExecDmgInject(inject);
    }
  }

  [Invoke((long)SkillIdEnum.DaPeng_ZhanChi)] // 注入Pre_Proc
  public class DaPengZhanChiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode procNode = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      AttackComponent src = ctx.Src;
      procNode.RightNode = null;
      CureTargetCtx cureTargetCtx = new()
      {
        cureHpNum = skill.damage * src.level,
        cureType = CureType.HP,
        ReplaceTarget = ctx.Src
      };
      procNode.AddChild(AttackFlowEnum.Cure_Target, cureTargetCtx).AddChild(AttackFlowEnum.Proc_Rlt, new AttackFlowContext());
    }
  }

  [Invoke((long)SkillIdEnum.Re_Lang)] // 注入Calc_Dmg
  public class ReLangSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      SkillInjectInvokerComFunc.ComDmgByLevel(info);
    }
  }

  [Invoke((long)SkillIdEnum.HuanYingZhi_Ji)] // 注入Calc_Dmg
  public class HuanYingZhiJiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      SkillInjectInvokerComFunc.ComDmgByLevel(info);
    }
  }

  [Invoke((long)SkillIdEnum.YanShiZhi_Quan)] // 注入Calc_Dmg
  public class YanShiZhiQuanSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      SkillInjectInvokerComFunc.ComDmgByLevel(info);
    }
  }

  [Invoke((long)SkillIdEnum.ZhiYanZhi_Xi)] // 注入Check_Pre_Cond
  public class ZhiYanZhiXiSkillInjectInvoker : AInvokeHandler<SkillInjectInfo>
  {
    public override void Handle(SkillInjectInfo info)
    {
      AttackFlowNode root = info.Node;
      AttackCtxComp ctx = info.AttackCtx;
      Skill skill = ctx.Skill;
      AttackFlowNode checkNode = root.GetFirstChild(AttackFlowEnum.Check_Pre_Cond);
      if (checkNode == null)
        return;
      AttackComponent src = ctx.Src;
      CheckCondCtx checkCondCtx = (CheckCondCtx)checkNode.NodeCtx;
      checkCondCtx.UniqCheck.Add((inCtx) =>
      {
        // 炙炎之息要检查血量是否满足要求
        if (src.blood <= skill.damage)
        {
          return LogicRet.Failed("您的血量不够释放此技能");
        }
        // 释放前扣除血量
        src.blood -= skill.damage;
        src.GetComponent<InFightComponent>().UpdateAttackBloodAndBlue(-skill.damage, 0);
        return LogicRet.Success;
      });
      AttackFlowNode procNode = root.GetFirstChild(AttackFlowEnum.Pre_Proc);
      procNode.ChildNode = null;
      CureTargetCtx cureTargetCtx = new()
      {
        cureHpNum = skill.damage,
        cureType = CureType.HP,
        ReplaceTarget = ctx.Src
      };
      procNode.AddChild(AttackFlowEnum.Cure_Target, cureTargetCtx).AddChild(AttackFlowEnum.Proc_Rlt, new AttackFlowContext());
    }
  }
}