namespace MaoYouJi
{
  [EntitySystemOf(typeof(AttackCtxComp))]
  [FriendOf(typeof(AttackCtxComp))]
  public static partial class AttackCtxCompSys
  {
    [EntitySystem]
    private static void Awake(this AttackCtxComp self, AttackComponent attackComponent)
    {
      AttackInCache attackInCache = self.GetParent<AttackInCache>();
      self.Src = attackComponent;
      InFightComponent srcInFight = attackComponent.GetComponent<InFightComponent>();
      if (srcInFight != null && srcInFight.attackTarget != null)
      {
        self.NowTarget = attackInCache.GetFightTarget(srcInFight.attackTarget);
      }
      ThreadHelper.SetAttackCtx(self);
    }

    [EntitySystem]
    private static void Destroy(this AttackCtxComp self)
    {
      ThreadHelper.SetAttackCtx(null);
    }

    public static bool IsStartSong(this AttackCtxComp self)
    {
      return (self.CtxFlag & 1) == 1;
    }

    public static bool IsExecSong(this AttackCtxComp self)
    {
      return (self.CtxFlag & 2) == 2;
    }

    public static void SetStartSong(this AttackCtxComp self)
    {
      self.CtxFlag |= 1;
    }

    public static void SetExecSong(this AttackCtxComp self)
    {
      self.CtxFlag |= 2;
    }

    public static string GetSrcName(this AttackCtxComp self)
    {
      if (self.Skill != null)
      {
        return self.Skill.name;
      }
      if (self.Thing != null)
      {
        return self.Thing.name;
      }
      if (self.NowStatus != null)
      {
        return EnumDescriptionCache.GetDescription(self.NowStatus.state);
      }
      return "普通攻击";
    }

    public static void DmgTarget(this AttackCtxComp self, AttackComponent realSrc, CalcDmgCtx calcDmgCtx)
    {
      AttackComponent realTarget = calcDmgCtx.ReplaceTarget == null ? self.NowTarget : calcDmgCtx.ReplaceTarget;
      long dmgNum = calcDmgCtx.FinalDmgNum;
      ActiveJobInfo activeJobInfo = realTarget.GetComponent<InFightComponent>().GetComponent<ActiveJobInfo>();
      Skill talentSkill = activeJobInfo.ActiveTalentSkill;
      if (talentSkill != null)
      {
        // 恶魔皮肤一定概率吸收伤害
        if (talentSkill.skillId == SkillIdEnum.EMo_PiFu)
        {
          long randVal = RandomGenerator.RandomNumber(0, 1000);
          if (randVal < talentSkill.vals[2])
          {
            calcDmgCtx.FinalDmgNum = 0;
            calcDmgCtx.RealDmgNum = 0;
            return;
          }
        }
      }
      if (dmgNum > realTarget.blood)
      {
        dmgNum = realTarget.blood;
      }
      realTarget.blood -= dmgNum; // 执行伤害扣除
      realTarget.GetComponent<InFightComponent>().UpdateAttackBloodAndBlue(bloodSub: -dmgNum);
    }

    // 处理最终结果
    public static void ProcAttackCtx(this AttackCtxComp self)
    {
    }
  }
}