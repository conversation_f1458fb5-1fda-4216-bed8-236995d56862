using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(AttackInCache))]
  [FriendOf(typeof(AttackInCache))]
  public static partial class AttackInCacheSys
  {
    [EntitySystem]
    private static void Awake(this AttackInCache self, AttackComponent src, AttackComponent target)
    {
      AttackCtxComp attackCtxComp = self.AddComponent<AttackCtxComp, AttackComponent>(src);
      ETLog.Info($"开始战斗: {self.Id}, {src.fightInfo.fightName}-{src.fightInfo.fightId}, {target.fightInfo.fightName}-{target.fightInfo.fightId}");
      MapNode mapNode = src.Parent.GetParent<MapNode>();
      self.MapName = mapNode.mapName;
      self.PointName = mapNode.pointName;
      self.StartTime = TimeInfo.Instance.ServerNow();
      self.UpdateTime = self.StartTime;
      self.FightList.TryAdd(src.Id, src);
      self.FightList.TryAdd(target.Id, target);
      src.AddComponent<InFightComponent, AttackComponent, AttackInCache>(target, self);
      target.AddComponent<InFightComponent, AttackComponent, AttackInCache>(src, self);
      // 添加一个MailBoxComponent，用于接收消息
      self.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.UnOrderedMessage);
      GlobalInfoCache.Instance.AddAttackInCache(self.Id, self);
      attackCtxComp.Dispose();
    }

    [EntitySystem]
    private static void Destroy(this AttackInCache self)
    {
      GlobalInfoCache.Instance.RemoveAttackInCache(self.Id);
    }

    public static void AddFight(this AttackInCache self, AttackComponent src, AttackComponent target)
    {
      AttackCtxComp attackCtxComp = self.AddComponent<AttackCtxComp, AttackComponent>(src);
      self.FightList.TryAdd(src.Id, src);
      src.AddComponent<InFightComponent, AttackComponent, AttackInCache>(target, self);
      self.UpdateTime = TimeInfo.Instance.ServerNow();
      ETLog.Info($"加入战斗: {self.Id}, {src.fightInfo.fightName}-{src.fightInfo.fightId}, {target.fightInfo.fightName}-{target.fightInfo.fightId}");
      attackCtxComp.Dispose();
    }

    // 调用这个函数延迟更新是为了避免Actor消息没有被处理
    public static void EndAttack(this AttackInCache self)
    {
      if (self.IsEnd)
      {
        return;
      }
      ETLog.Info($"战斗结束");
      self.IsEnd = true;
      foreach (var fight in self.FightList)
      {
        AttackComponent target = fight.Value;
        target?.RemoveComponent<InFightComponent>();
      }
      self.FightList.Clear();
      self.DelayDestroy().Coroutine();
    }

    private static async ETTask DelayDestroy(this AttackInCache self)
    {
      TimerComponent timerComponent = self.Root().GetComponent<TimerComponent>();
      await timerComponent.WaitAsync(3000);
      ETLog.Info($"销毁战斗: {self.Id}");
      self.Dispose();
    }

    public static AttackComponent GetFightTarget(this AttackInCache self, FightInfo fightInfo)
    {
      if (self.FightList.TryGetValue(fightInfo.fightId, out EntityRef<AttackComponent> target))
      {
        return target;
      }
      return null;
    }

    public static AttackComponent GetFightTarget(this AttackInCache self, long fightId)
    {
      if (self.FightList.TryGetValue(fightId, out EntityRef<AttackComponent> target))
      {
        return target;
      }
      return null;
    }

    public static List<AttackComponent> GetTargetList(this AttackInCache self, AttackComponent src)
    {
      List<AttackComponent> targetList = new();
      foreach (FightInfo fightInfo in src.GetComponent<InFightComponent>().fightList.Values)
      {
        if (self.FightList.TryGetValue(fightInfo.fightId, out EntityRef<AttackComponent> targetRef))
        {
          targetList.Add(targetRef.Get());
        }
      }
      return targetList;
    }
  }
}