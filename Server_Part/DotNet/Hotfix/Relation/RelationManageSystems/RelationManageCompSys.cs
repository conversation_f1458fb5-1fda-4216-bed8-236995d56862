using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(RelationManageComponent))]
  [FriendOf(typeof(RelationManageComponent))]
  public static partial class RelationManageCompSys
  {
    [EntitySystem]
    public static void Awake(this RelationManageComponent self)
    {

    }

    public static async ETTask InitUserFriendInfo(this RelationManageComponent self, long userId)
    {
      User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
      if (user == null)
      {
        return;
      }
      DBComponent dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      List<UserFriendInfo> userFriendInfos = await dbComponent.Query<UserFriendInfo>(userId, d => d.SrcUserId == userId || d.TargetUserId == userId);
      if (userFriendInfos == null)
      {
        return;
      }
      foreach (UserFriendInfo dbInfo in userFriendInfos)
      {
        if (!self.Children.ContainsKey(dbInfo.Id))
        {
          self.AddChildWithAwake(dbInfo);
        }
        UserFriendInfo userFriendInfo = self.GetChild<UserFriendInfo>(dbInfo.Id);
        if (userFriendInfo.SrcUserId == userId)
        {
          userFriendInfo.SrcUser = user;
        }
        else
        {
          userFriendInfo.TargetUser = user;
        }
        RelationComponent relationComponent = user.GetComponent<RelationComponent>();
        if (userFriendInfo.FriendType == FriendTypeEnum.Friend)
        {
          relationComponent.UserFriendInfoMap.TryAdd(userFriendInfo.TargetUserId, userFriendInfo);
        }
        else if (userFriendInfo.FriendType == FriendTypeEnum.Lover)
        {
          relationComponent.UserLoveInfo = userFriendInfo;
        }
        else if (userFriendInfo.FriendType == FriendTypeEnum.Teacher)
        {
          // 师傅
          if (userFriendInfo.SrcUserId == userId)
          {
            relationComponent.UserTeacherInfo = userFriendInfo;
          }
          else
          {
            // 徒弟
            relationComponent.UserStudentInfo.TryAdd(userFriendInfo.SrcUserId, userFriendInfo);
          }
        }
        await ETTask.CompletedTask;
      }
    }

    public static async ETTask UnInitUserFriendInfo(this RelationManageComponent self, long userId, List<long> userFriendInfoIds)
    {
      DBComponent dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      List<UserFriendInfo> userFriendInfos = new List<UserFriendInfo>();
      for (int i = 0; i < userFriendInfoIds.Count; i++)
      {
        if (GlobalInfoCache.Instance.allUserFriendInfoCache.TryGetValue(userFriendInfoIds[i], out UserFriendInfo userFriendInfo))
        {
          userFriendInfos.Add(userFriendInfo);
        }
      }
      ETLog.Info($"Start UnInitUserFriendInfo: {userId} {userFriendInfos.Count}");
      int unLoadCount = 0;
      foreach (UserFriendInfo userFriendInfo in userFriendInfos)
      {
        if (userFriendInfo == null)
        {
          continue;
        }
        if (userFriendInfo.SrcUserId == userId)
        {
          User targetUser = userFriendInfo.TargetUser;
          if (targetUser == null)
          {
            unLoadCount++;
            await dbComponent.Save(userFriendInfo.Id, userFriendInfo);
            self.RemoveChild(userFriendInfo.Id);
          }
        }
        else
        {
          User srcUser = userFriendInfo.SrcUser;
          if (srcUser == null)
          {
            unLoadCount++;
            await dbComponent.Save(userFriendInfo.Id, userFriendInfo);
            self.RemoveChild(userFriendInfo.Id);
          }
        }
      }
      ETLog.Info($"End UnInitUserFriendInfo: {userId} {unLoadCount}");
    }

    // 发送好友请求
    public static async ETTask AddFriend(this RelationManageComponent self, AddFriendInfo addFriendInfo)
    {
      UserFriendInfo userFriendInfo = self.AddChild<UserFriendInfo, AddFriendInfo>(addFriendInfo);
      DBComponent dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      await dbComponent.Insert(userFriendInfo.Id, userFriendInfo);
    }

    // 删除好友
    public static async ETTask DelFriend(this RelationManageComponent self, FriendInfoRecord friendInfoRecord)
    {
      DBComponent dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      UserFriendInfo userFriendInfo = self.GetChild<UserFriendInfo>(friendInfoRecord.id);
      await dbComponent.Remove<UserFriendInfo>(friendInfoRecord.id);
      if (userFriendInfo == null)
      {
        ETLog.Error($"DelFriend: {friendInfoRecord.id} not found");
        return;
      }
      User nowUser = self.GetParent<User>();
      ETLog.Info($"DelFriend: {userFriendInfo.Id} {userFriendInfo.FriendType} {userFriendInfo.SrcUserId} {userFriendInfo.TargetUserId}");
      User srcUser = userFriendInfo.SrcUser;
      User targetUser = userFriendInfo.TargetUser;
      if (srcUser != null)
      {
        if (srcUser.Id != nowUser.Id)
        {
          srcUser.SendToast($"你和{nowUser.nickname}的{EnumDescriptionCache.GetDescription(userFriendInfo.FriendType)}关系已解除");
        }
        srcUser.GetComponent<RelationComponent>().RemoveUserFriendInfo(userFriendInfo);
      }
      if (targetUser != null)
      {
        if (targetUser.Id != nowUser.Id)
        {
          targetUser.SendToast($"你和{nowUser.nickname}的{EnumDescriptionCache.GetDescription(userFriendInfo.FriendType)}关系已解除");
        }
        targetUser.GetComponent<RelationComponent>().RemoveUserFriendInfo(userFriendInfo);
      }
      self.RemoveChild(userFriendInfo.Id);
    }
  }
}