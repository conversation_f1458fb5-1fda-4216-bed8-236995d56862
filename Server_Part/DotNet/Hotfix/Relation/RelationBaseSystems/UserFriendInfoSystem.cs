namespace MaoYouJi
{
  [EntitySystemOf(typeof(UserFriendInfo))]
  [FriendOf(typeof(UserFriendInfo))]
  public static partial class UserFriendInfoSystem
  {
    [EntitySystem]
    public static void Awake(this UserFriendInfo self)
    {
      GlobalInfoCache.Instance.allUserFriendInfoCache.TryAdd(self.Id, self);
    }

    [EntitySystem]
    public static void Awake(this UserFriendInfo self, AddFriendInfo addFriendInfo)
    {
      self.SrcUserId = addFriendInfo.srcUserId;
      self.TargetUserId = addFriendInfo.targetUserId;
      // 师徒关系，srcUserId是徒弟，targetUserId是师傅
      if (addFriendInfo.addFriendType == AddFriendTypeEnum.AddStudent)
      {
        self.SrcUserId = addFriendInfo.targetUserId;
        self.TargetUserId = addFriendInfo.srcUserId;
      }
      self.FriendType = AddFriendInfo.addFriendTypeToFriendTypeMap[addFriendInfo.addFriendType];
      self.CreateTime = addFriendInfo.createTime;
      User srcUser = GlobalInfoCache.Instance.GetOnlineUser(addFriendInfo.srcUserId);
      if (srcUser != null)
      {
        self.SrcUser = srcUser;
        srcUser.GetComponent<RelationComponent>().UserFriendInfoMap.TryAdd(self.Id, self);
      }
      User targetUser = GlobalInfoCache.Instance.GetOnlineUser(addFriendInfo.targetUserId);
      if (targetUser != null)
      {
        self.TargetUser = targetUser;
        targetUser.GetComponent<RelationComponent>().UserFriendInfoMap.TryAdd(self.Id, self);
      }
      GlobalInfoCache.Instance.allUserFriendInfoCache.TryAdd(self.Id, self);
    }

    [EntitySystem]
    public static void Destroy(this UserFriendInfo self)
    {
      GlobalInfoCache.Instance.allUserFriendInfoCache.TryRemove(self.Id, out _);
    }

    public static FriendInfoRecord GetFriendInfoRecord(this UserFriendInfo self)
    {
      return new FriendInfoRecord
      {
        id = self.Id,
        srcUserId = self.SrcUserId,
        targetUserId = self.TargetUserId,
        friendType = self.FriendType,
        createTime = self.CreateTime,
      };
    }
  }
}