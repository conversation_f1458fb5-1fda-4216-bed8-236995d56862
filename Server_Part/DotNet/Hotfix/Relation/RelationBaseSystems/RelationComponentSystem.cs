using System;
using System.Collections.Generic;
using System.Linq;
using MongoDB.Bson;
using MongoDB.Driver;

namespace MaoYouJi
{
  [FriendOf(typeof(RelationComponent))]
  public static partial class RelationComponentSystem
  {
    [EntitySystem]
    public static void Load(this RelationComponent self)
    {

    }

    public static void AddRelationExp(this RelationComponent self, RelationTypeEnum relationType, int addExp)
    {
      User user = self.GetParent<User>();
      RelationInfo relationInfo = self.GetRelationInfo(relationType);
      string addType = addExp > 0 ? "增加" : "减少";
      // 发送关系值增加消息
      user.SendChat("您的" + EnumDescriptionCache.GetDescription(relationType) + addType + Math.Abs(addExp) + "点");
      relationInfo.relationExp += addExp;
      // 关系值达到上限后，关系等级提升
      if (relationInfo.relationExp >= relationInfo.relationMaxExp)
      {
        if (relationInfo.level < 5)
        {
          relationInfo.level++;
          relationInfo.relationExp = 0;
          relationInfo.relationMaxExp = RelationInfo.GetRelationExp(relationInfo.level);
          // 发送关系等级提升消息
          user.SendChat("您的" + EnumDescriptionCache.GetDescription(relationType) + "等级提升至" + RelationInfo.GetRelationLevelTxt(relationInfo.level));
        }
        else
        {
          relationInfo.relationExp = relationInfo.relationMaxExp;
        }
      }
      else if (relationInfo.relationExp < 0)
      {
        if (relationInfo.level > -3)
        {
          relationInfo.level--;
          relationInfo.relationExp = 0;
          relationInfo.relationMaxExp = RelationInfo.GetRelationExp(relationInfo.level);
          // 发送关系等级降低消息
          user.SendChat("您的" + EnumDescriptionCache.GetDescription(relationType) + "等级降低至" + RelationInfo.GetRelationLevelTxt(relationInfo.level));
        }
        else
        {
          relationInfo.relationExp = 0;
        }
      }
    }

    public static List<FriendInfoRecord> GetFriendRecord(this RelationComponent self, FriendTypeEnum friendType)
    {
      List<EntityRef<UserFriendInfo>> userFriendInfos = new();
      if (friendType == FriendTypeEnum.Friend || friendType == FriendTypeEnum.None)
      {
        userFriendInfos.AddRange(self.UserFriendInfoMap.Values);
      }
      if (friendType == FriendTypeEnum.Teacher || friendType == FriendTypeEnum.None)
      {
        userFriendInfos.Add(self.UserTeacherInfo);
        userFriendInfos.AddRange(self.UserStudentInfo.Values);
      }
      if (friendType == FriendTypeEnum.Lover || friendType == FriendTypeEnum.None)
      {
        userFriendInfos.Add(self.UserLoveInfo);
      }
      List<FriendInfoRecord> friendInfoRecords = new();
      foreach (UserFriendInfo userFriendInfo in userFriendInfos)
      {
        if (userFriendInfo != null)
        {
          friendInfoRecords.Add(userFriendInfo.GetFriendInfoRecord());
        }
      }
      return friendInfoRecords;
    }

    public static FriendInfoRecord GetFriendRecord(this RelationComponent self, long friendRecordId, FriendTypeEnum friendType)
    {
      List<FriendInfoRecord> friendInfoRecords = self.GetFriendRecord(friendType);
      return friendInfoRecords.Find(record => record.id == friendRecordId);
    }

    public static async ETTask<List<FriendInfoRecord>> GetFriendRecordFromDb(this RelationComponent self, FriendTypeEnum friendType, params long[] needQueryUserIds)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<UserFriendInfo>();
      var filterBuilder = Builders<UserFriendInfo>.Filter;
      var filter = filterBuilder.In(f => f.SrcUserId, needQueryUserIds);
      if (friendType != FriendTypeEnum.None)
      {
        filter &= filterBuilder.Eq(f => f.FriendType, friendType);
      }
      IAsyncCursor<UserFriendInfo> cursor = await collection.FindAsync(filter);
      List<FriendInfoRecord> friendInfoRecords = new();
      await cursor.ForEachAsync(userFriendInfo =>
      {
        friendInfoRecords.Add(userFriendInfo.GetFriendInfoRecord());
      });
      return friendInfoRecords;
    }

    // 统计好友数量
    public static long CountFriend(this RelationComponent self)
    {
      return self.UserFriendInfoMap.Count;
    }

    // 统计徒弟数量
    public static long CountStudent(this RelationComponent self)
    {
      return self.UserStudentInfo.Count;
    }

    // 统计师傅数量
    public static bool HasTeacher(this RelationComponent self)
    {
      UserFriendInfo userTeacherInfo = self.UserTeacherInfo;
      return userTeacherInfo != null;
    }

    // 统计爱人数量
    public static bool HasLover(this RelationComponent self)
    {
      UserFriendInfo userLoverInfo = self.UserLoveInfo;
      return userLoverInfo != null;
    }

    public static async ETTask<List<AddFriendInfo>> GetAddFriendReqList(this RelationComponent self, long userId, string reqId)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<AddFriendInfo>();
      var filterBuilder = Builders<AddFriendInfo>.Filter;
      var filter = filterBuilder.Eq(f => f.targetUserId, userId);
      if (reqId != null)
      {
        filter &= filterBuilder.Eq(f => f.id, new ObjectId(reqId));
      }
      return await collection.Find(filter).ToListAsync();
    }

    public static async ETTask RemoveAddFriendReq(this RelationComponent self, List<string> reqIds)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<AddFriendInfo>();
      var filterBuilder = Builders<AddFriendInfo>.Filter;
      var filter = filterBuilder.In(f => f.id, reqIds.Select(id => new ObjectId(id)));
      await collection.DeleteManyAsync(filter);
    }

    // 统计添加好友请求数量
    public static async ETTask<long> CountAddFriendReqAsync(this RelationComponent self, long targetUserId, AddFriendTypeEnum addFriendType)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<AddFriendInfo>();
      var filterBuilder = Builders<AddFriendInfo>.Filter;
      var filter = filterBuilder.Eq(f => f.targetUserId, targetUserId);
      if (addFriendType != AddFriendTypeEnum.None)
      {
        filter &= filterBuilder.Eq(f => f.addFriendType, addFriendType);
      }
      return await collection.CountDocumentsAsync(filter);
    }

    // 判断是否存在添加好友请求
    public static async ETTask<bool> HasAddFriendReqAsync(this RelationComponent self, long targetUserId, AddFriendTypeEnum addFriendType)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<AddFriendInfo>();
      var filterBuilder = Builders<AddFriendInfo>.Filter;
      var filter = filterBuilder.Eq(f => f.targetUserId, targetUserId);
      if (addFriendType != AddFriendTypeEnum.None)
      {
        filter &= filterBuilder.Eq(f => f.addFriendType, addFriendType);
      }
      return await collection.Find(filter).AnyAsync();
    }

    // 判断是否存在好友关系
    public static bool HasFriendInfo(this RelationComponent self, long targetUserId, FriendTypeEnum friendType)
    {
      if (friendType == FriendTypeEnum.Friend)
      {
        foreach (UserFriendInfo userFriendInfo in self.UserFriendInfoMap.Values)
        {
          if (userFriendInfo.TargetUserId == targetUserId)
          {
            return true;
          }
        }
        return false;
      }
      else if (friendType == FriendTypeEnum.Teacher)
      {
        List<EntityRef<UserFriendInfo>> userTeacherInfos = new(self.UserStudentInfo.Values);
        userTeacherInfos.Add(self.UserTeacherInfo);
        foreach (EntityRef<UserFriendInfo> userFriendInfoRef in userTeacherInfos)
        {
          UserFriendInfo userFriendInfo = userFriendInfoRef;
          if (userFriendInfo != null && (userFriendInfo.TargetUserId == targetUserId || userFriendInfo.SrcUserId == targetUserId))
          {
            return true;
          }
        }
      }
      else if (friendType == FriendTypeEnum.Lover)
      {
        UserFriendInfo userLoverInfo = self.UserLoveInfo;
        return userLoverInfo != null && (userLoverInfo.TargetUserId == targetUserId || userLoverInfo.SrcUserId == targetUserId);
      }
      return false;
    }

    // 判断是否存在预添加好友请求
    public static async ETTask<bool> HasPreAddFriendReqAsync(this RelationComponent self, long srcUserId, long targetUserId, AddFriendTypeEnum addFriendType)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<AddFriendInfo>();
      var filterBuilder = Builders<AddFriendInfo>.Filter;
      var filter = filterBuilder.And(
          filterBuilder.Eq(f => f.srcUserId, srcUserId),
          filterBuilder.Eq(f => f.targetUserId, targetUserId),
          filterBuilder.Eq(f => f.addFriendType, addFriendType)
      );
      return await collection.Find(filter).AnyAsync();
    }

    // 发送好友请求
    public static async ETTask AddFriendReq(this RelationComponent self, long srcUserId, long targetUserId, AddFriendTypeEnum addFriendType, string addMsg)
    {
      AddFriendInfo addFriendInfo = new AddFriendInfo
      {
        addFriendType = addFriendType,
        srcUserId = srcUserId,
        targetUserId = targetUserId,
        addMsg = addMsg,
        createTime = TimeInfo.Instance.ServerNow()
      };
      await self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<AddFriendInfo>().InsertOneAsync(addFriendInfo);
    }

    public static void AddUserFriendInfo(this RelationComponent self, UserFriendInfo userFriendInfo)
    {
      User user = self.GetParent<User>();
      if (userFriendInfo.FriendType == FriendTypeEnum.Friend)
      {
        self.UserFriendInfoMap.TryAdd(userFriendInfo.Id, userFriendInfo);
      }
      else if (userFriendInfo.FriendType == FriendTypeEnum.Lover)
      {
        self.UserLoveInfo = userFriendInfo;
      }
      else if (userFriendInfo.FriendType == FriendTypeEnum.Teacher)
      {
        if (userFriendInfo.SrcUserId == user.Id)
        {
          self.UserTeacherInfo = userFriendInfo;
        }
        else
        {
          self.UserStudentInfo.TryAdd(userFriendInfo.Id, userFriendInfo);
        }
      }
    }

    public static void RemoveUserFriendInfo(this RelationComponent self, UserFriendInfo userFriendInfo)
    {
      if (userFriendInfo.FriendType == FriendTypeEnum.Friend)
      {
        self.UserFriendInfoMap.TryRemove(userFriendInfo.Id, out _);
      }
      else if (userFriendInfo.FriendType == FriendTypeEnum.Lover)
      {
        self.UserLoveInfo = null;
      }
      else if (userFriendInfo.FriendType == FriendTypeEnum.Teacher)
      {
        if (userFriendInfo.SrcUserId == self.GetParent<User>().Id)
        {
          self.UserTeacherInfo = null;
        }
        else
        {
          self.UserStudentInfo.TryRemove(userFriendInfo.Id, out _);
        }
      }
    }
  }
}