
namespace MaoYouJi
{
  [MessageHand<PERSON>(SceneType.Social)]
  [FriendOf(typeof(RelationManageComponent))]
  public class InitUserFriendInfoMsgHandler : MessageHandler<Scene, InnerInitUserFriendInfoMsg>
  {
    protected override async ETTask Run(Scene scene, InnerInitUserFriendInfoMsg msg)
    {
      RelationManageComponent relationManageComponent = scene.GetComponent<RelationManageComponent>();
      await relationManageComponent.InitUserFriendInfo(msg.UserId);
    }
  }

  [MessageHandler(SceneType.Social)]
  [FriendOf(typeof(RelationManageComponent))]
  public class UnInitUserFriendInfoMsgHandler : MessageHandler<Scene, InnerUnInitUserFriendInfoMsg>
  {
    protected override async ETTask Run(Scene scene, InnerUnInitUserFriendInfoMsg msg)
    {
      RelationManageComponent relationManageComponent = scene.GetComponent<RelationManageComponent>();
      if (msg.UserId == 0)
      {
        ETLog.Error("卸载用户关系：UserId is 0");
        return;
      }
      await relationManageComponent.UnInitUserFriendInfo(msg.UserId, msg.UserFriendInfoIds);
    }
  }
}