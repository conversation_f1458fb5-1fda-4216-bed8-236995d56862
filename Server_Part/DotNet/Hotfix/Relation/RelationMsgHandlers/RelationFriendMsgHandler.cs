using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Social)]
  public class GetFriendListHandler : MessageHandler<Scene, GetFriendListReq, GetFriendListResp>
  {
    protected override async ETTask Run(Scene scene, GetFriendListReq req, GetFriendListResp resp)
    {
      LogicRet getUserRet = UserProcSystem.GetUserWithCheck(req.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        resp.SetError(getUserRet.Message);
        return;
      }
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      resp.FriendRecords = relationComponent.GetFriendRecord(FriendTypeEnum.None);
      List<long> friendIds = resp.FriendRecords.Select(friendRecord =>
      {
        if (friendRecord.srcUserId == user.Id)
        {
          return friendRecord.targetUserId;
        }
        return friendRecord.srcUserId;
      }).ToList();
      FiberUsersComponent fiberUsersComponent = scene.Root().GetComponent<FiberUsersComponent>();
      resp.UserSimpleInfos = await fiberUsersComponent.GetSimpleUserByIds(friendIds.ToArray());
    }
  }

  [MessageLocationHandler(SceneType.Social)]
  public class AddFriendHandler : MessageHandler<Scene, ClientAddFriendMsg>
  {
    protected override async ETTask Run(Scene nowMap, ClientAddFriendMsg msg)
    {
      LogicRet getUserRet = UserProcSystem.GetUserWithCheck(msg.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      if (msg.addFriendType == AddFriendTypeEnum.None)
      {
        user.SendToast("好友类型为空");
        return;
      }
      if (msg.TargetUserId == null)
      {
        user.SendToast("目标不能为空");
        return;
      }
      FiberUsersComponent fiberUsersComponent = nowMap.Root().GetComponent<FiberUsersComponent>();
      User targetUser = await fiberUsersComponent.GetOnlineUserBuyIdOrName(msg.TargetUserId);
      if (targetUser == null)
      {
        user.SendToast("目标用户不存在或不在线");
        return;
      }
      if (user.Id == targetUser.Id)
      {
        user.SendToast("不能添加自己");
        return;
      }
      if (user.netAccountId == targetUser.netAccountId)
      {
        user.SendToast("同账户角色无法添加");
        return;
      }
      FriendTypeEnum targetFriendType = FriendTypeEnum.None;
      RelationComponent targetRelationComponent = targetUser.GetComponent<RelationComponent>();
      if (msg.addFriendType == AddFriendTypeEnum.AddFriend)
      {
        targetFriendType = FriendTypeEnum.Friend;
        if (relationComponent.CountFriend() >= 50)
        {
          user.SendToast("您的好友数量已满");
          return;
        }
        if (targetRelationComponent.CountFriend() >= 50)
        {
          user.SendToast("对方的好友数量已满");
          return;
        }
      }
      else if (msg.addFriendType == AddFriendTypeEnum.AddLover)
      {
        targetFriendType = FriendTypeEnum.Lover;
        if (relationComponent.HasLover())
        {
          user.SendToast("您已经有爱人了");
          return;
        }
        if (targetRelationComponent.HasLover())
        {
          user.SendToast("对方已经有爱人了");
          return;
        }
      }
      else if (msg.addFriendType == AddFriendTypeEnum.AddTeacher || msg.addFriendType == AddFriendTypeEnum.AddStudent)
      {
        targetFriendType = FriendTypeEnum.Teacher;
        AttackComponent attackComponent = user.GetComponent<AttackComponent>();
        AttackComponent targetAttackComponent = targetUser.GetComponent<AttackComponent>();
        if (msg.addFriendType == AddFriendTypeEnum.AddTeacher)
        {
          if (attackComponent.level > 20)
          {
            user.SendToast("您的等级超过20级，无法拜师");
            return;
          }
          if (targetAttackComponent.level <= attackComponent.level)
          {
            user.SendToast("对方的等级小于您，无法拜师");
            return;
          }
          if (relationComponent.HasTeacher())
          {
            user.SendToast("您已经在师徒关系中");
            return;
          }
          if (targetRelationComponent.CountStudent() >= 5)
          {
            user.SendToast("对方已经有5个徒弟了");
            return;
          }
        }
        else if (msg.addFriendType == AddFriendTypeEnum.AddStudent)
        {
          if (targetAttackComponent.level <= 20)
          {
            user.SendToast("对方的等级小于20级，无法收徒");
            return;
          }
          if (targetAttackComponent.level >= attackComponent.level)
          {
            user.SendToast("对方的等级大于您，无法收徒");
            return;
          }
          if (targetRelationComponent.HasTeacher())
          {
            user.SendToast("对方已经有师傅了，无法收徒");
            return;
          }
          if (relationComponent.CountStudent() >= 5)
          {
            user.SendToast("您已经有5个徒弟了");
            return;
          }
        }
      }
      if (relationComponent.HasFriendInfo(targetUser.Id, targetFriendType))
      {
        user.SendToast("关系已存在");
        return;
      }
      if (await relationComponent.HasPreAddFriendReqAsync(user.Id, targetUser.Id, msg.addFriendType))
      {
        user.SendToast("无法重复发送请求");
        return;
      }
      if (msg.AddMsg == null || msg.AddMsg.Length == 0)
      {
        msg.AddMsg = "我很赏识你哦！";
      }
      long reqNum = await relationComponent.CountAddFriendReqAsync(targetUser.Id, AddFriendTypeEnum.None);
      if (reqNum >= 50)
      {
        user.SendToast("对方的社交请求已满");
        return;
      }
      await relationComponent.AddFriendReq(user.Id, targetUser.Id, msg.addFriendType, msg.AddMsg);
      targetUser.SendToast("您有新的" + EnumDescriptionCache.GetDescription(msg.addFriendType) + "请求");
      user.SendToast(EnumDescriptionCache.GetDescription(msg.addFriendType) + "请求发送成功");
      // user.GetComponent<MenuReddotComponent>().SetReddot(MenuReddotEnum.Menu_Friends_Reddot, true);
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Social)]
  public class ProcAddFriendHandler : MessageHandler<Scene, ProcAddFriendReq, ProcAddFriendResp>
  {
    protected override async ETTask Run(Scene scene, ProcAddFriendReq msg, ProcAddFriendResp resp)
    {
      LogicRet getUserRet = UserProcSystem.GetUserWithCheck(msg.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        resp.SetError(getUserRet.Message);
        return;
      }
      if (msg.isProcAll == 0 && msg.reqId == null)
      {
        resp.SetError("目标请求不能为空");
        return;
      }
      if (msg.isProcAll == 1)
      {
        msg.reqId = null;
      }
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      List<AddFriendInfo> addFriendInfos;
      // 这里加锁，防止多个请求同时处理
      using (await scene.GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.Social, user.Id))
      {
        addFriendInfos = await relationComponent.GetAddFriendReqList(user.Id, msg.reqId);
        if (addFriendInfos == null || addFriendInfos.Count == 0)
        {
          resp.SetError("请求不存在");
          return;
        }
        await relationComponent.RemoveAddFriendReq(
            addFriendInfos.Select(addFriendInfo => addFriendInfo.id.ToString()).ToList());
      }
      List<long> srcUserIds = addFriendInfos.Select(addFriendInfo => addFriendInfo.srcUserId).ToList();
      FiberUsersComponent fiberUsersComponent = scene.Root().GetComponent<FiberUsersComponent>();
      Dictionary<long, ShowSimpleUser> srcUserInfos = await fiberUsersComponent.GetSimpleUserByIds(srcUserIds.ToArray());
      if (msg.procType != 0)
      {
        ChatProSystem.SendChatToUsers(srcUserIds, user.nickname + "拒绝了您的社交请求");
      }
      else
      {
        HashSet<long> needQueryIds = [.. srcUserIds, user.Id];
        // 统计好友数量
        List<FriendInfoRecord> friendRecords = await relationComponent.GetFriendRecordFromDb(FriendTypeEnum.None, needQueryIds.ToArray());
        Dictionary<long, FriendCnt> friendCntMap = new Dictionary<long, FriendCnt>();
        HashSet<string> friendKeySet = new HashSet<string>();
        foreach (FriendInfoRecord friendRecord in friendRecords)
        {
          friendKeySet.Add(friendRecord.srcUserId + "_" + friendRecord.targetUserId + "_" + friendRecord.friendType);
          FriendCnt srcCnt = friendCntMap.GetValueOrDefault(friendRecord.srcUserId, new FriendCnt());
          FriendCnt targetCnt = friendCntMap.GetValueOrDefault(friendRecord.targetUserId, new FriendCnt());
          if (friendRecord.friendType == FriendTypeEnum.Friend)
          {
            srcCnt.friendNum++;
            targetCnt.friendNum++;
          }
          else if (friendRecord.friendType == FriendTypeEnum.Teacher)
          {
            srcCnt.teacherNum++;
            targetCnt.studentNum++;
          }
          else if (friendRecord.friendType == FriendTypeEnum.Lover)
          {
            srcCnt.loverNum++;
            targetCnt.loverNum++;
          }
        }
        FriendCnt myCnt = friendCntMap.GetValueOrDefault(user.Id, new FriendCnt());
        foreach (AddFriendInfo addFriendInfo in addFriendInfos)
        {
          if (friendKeySet.Contains(addFriendInfo.srcUserId + "_" + addFriendInfo.targetUserId + "_"
              + addFriendInfo.addFriendType) || friendKeySet.Contains(
                  addFriendInfo.targetUserId + "_"
                      + addFriendInfo.srcUserId + "_" + addFriendInfo.addFriendType))
          {
            continue;
          }
          FriendCnt targetCnt = friendCntMap.GetValueOrDefault(addFriendInfo.srcUserId, new FriendCnt());
          if (addFriendInfo.addFriendType == AddFriendTypeEnum.AddFriend)
          {
            if (myCnt.friendNum >= 50 || targetCnt.friendNum >= 50)
            {
              user.SendToast("您或对方的好友数量已超过50，无法添加");
              continue;
            }
          }
          else if (addFriendInfo.addFriendType == AddFriendTypeEnum.AddTeacher)
          {
            if (targetCnt.teacherNum >= 1)
            {
              user.SendToast("对方已经有师傅了，无法收徒");
              continue;
            }
            if (myCnt.studentNum >= 5)
            {
              user.SendToast("您已经有5个徒弟了，无法继续收徒");
              continue;
            }
          }
          else if (addFriendInfo.addFriendType == AddFriendTypeEnum.AddStudent)
          {
            if (myCnt.teacherNum >= 1)
            {
              user.SendToast("您已经有师傅了，无法拜师");
              continue;
            }
            if (targetCnt.studentNum >= 5)
            {
              user.SendToast("对方已经有5个徒弟了，无法收徒");
              continue;
            }
          }
          else if (addFriendInfo.addFriendType == AddFriendTypeEnum.AddLover)
          {
            if (myCnt.loverNum >= 1 || targetCnt.loverNum >= 1)
            {
              user.SendToast("双方并非单身，无法成为婚姻关系");
              continue;
            }
          }
          FriendTypeEnum friendType = AddFriendInfo.addFriendTypeToFriendTypeMap[addFriendInfo.addFriendType];
          RelationManageComponent relationManageComponent = scene.Root().GetComponent<RelationManageComponent>();
          await relationManageComponent.AddFriend(addFriendInfo);
          if (srcUserInfos.ContainsKey(addFriendInfo.srcUserId))
          {
            user.SendChat("您和" + srcUserInfos.GetValueOrDefault(addFriendInfo.srcUserId).name
                + "成为" + EnumDescriptionCache.GetDescription(friendType) + "关系");
          }
          ChatProSystem.SendChatToUser(addFriendInfo.srcUserId, "您和" + user.nickname + "成为"
              + EnumDescriptionCache.GetDescription(friendType) + "关系");
        }
      }
      resp.showMessage = "处理成功";
    }
  }

  [MessageLocationHandler(SceneType.Social)]
  public class GetAddFriendListHandler : MessageHandler<Scene, GetAddFriendListReq, GetAddFriendListResp>
  {
    protected override async ETTask Run(Scene scene, GetAddFriendListReq req, GetAddFriendListResp resp)
    {
      LogicRet getUserRet = UserProcSystem.GetUserWithCheck(req.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        resp.SetError(getUserRet.Message);
        return;
      }
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      List<AddFriendInfo> addFriendInfos = await relationComponent.GetAddFriendReqList(user.Id, null);
      List<long> friendIds = addFriendInfos.Select(addFriendInfo =>
      {
        return addFriendInfo.srcUserId;
      }).ToList();
      FiberUsersComponent fiberUsersComponent = scene.Root().GetComponent<FiberUsersComponent>();
      resp.UserSimpleInfos = await fiberUsersComponent.GetSimpleUserByIds(friendIds.ToArray());
    }
  }

  [MessageLocationHandler(SceneType.Social)]
  public class DelFriendHandler : MessageHandler<Scene, DelFriendReq, DelFriendResp>
  {
    protected override async ETTask Run(Scene scene, DelFriendReq req, DelFriendResp resp)
    {
      LogicRet getUserRet = UserProcSystem.GetUserWithCheck(req.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        resp.SetError(getUserRet.Message);
        return;
      }
      if (req.friendRecordId == 0 && req.friendType == FriendTypeEnum.None)
      {
        resp.SetError("输入为空");
        return;
      }
      if (req.friendType != FriendTypeEnum.Teacher && req.friendRecordId == 0)
      {
        resp.SetError("输入为空");
        return;
      }
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      FriendInfoRecord friendInfoRecord = relationComponent.GetFriendRecord(req.friendRecordId, req.friendType);
      FiberUsersComponent fiberUsersComponent = scene.Root().GetComponent<FiberUsersComponent>();
      if (friendInfoRecord == null)
      {
        resp.SetError("关系记录不存在");
        return;
      }
      if (friendInfoRecord.friendType == FriendTypeEnum.Teacher)
      {
        // 如果是徒弟，可以直接解除师徒关系
        if (friendInfoRecord.srcUserId != user.Id)
        {
          // 如果是师傅，要徒弟2个星期没上线才能主动解除
          ShowSimpleUser srcUserInfo = await fiberUsersComponent.GetSimpleUserById(friendInfoRecord.srcUserId);
          if (srcUserInfo != null)
          {
            if (srcUserInfo.lastLogInTime > TimeInfo.Instance.ServerNow() - 14 * 24 * 60 * 60 * 1000)
            {
              resp.SetError("徒弟离线时间不超过2个星期");
              return;
            }
          }
        }
      }
      RelationManageComponent relationManageComponent = scene.Root().GetComponent<RelationManageComponent>();
      await relationManageComponent.DelFriend(friendInfoRecord);
      resp.showMessage = "关系解除成功";
    }
  }
}