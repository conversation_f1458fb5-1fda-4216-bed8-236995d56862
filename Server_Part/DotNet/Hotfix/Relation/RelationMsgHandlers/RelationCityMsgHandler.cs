namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientSubmitDaTaoShaJiangZhangHandler : MessageLocationHandler<MapNode, ClientSubmitDaTaoShaJiangZhangMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientSubmitDaTaoShaJiangZhangMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing dataoShaJiangZhang = bagComponent.GetThingInBag<Thing>(ThingNameEnum.DaTaoSha_JiangZhang);
      if (dataoShaJiangZhang == null)
      {
        user.SendToast("背包中没有大逃杀奖章");
        return;
      }
      if (dataoShaJiangZhang.num < msg.daTaoShaJiangZhangNum)
      {
        user.SendToast("大逃杀奖章数量不足");
        return;
      }
      bagComponent.AddThingNumWithSend(dataoShaJiangZhang, -msg.daTaoShaJiangZhangNum);
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      relationComponent.AddRelationExp(RelationTypeEnum.FanShuTuan_Relation, msg.daTaoShaJiangZhangNum * 20);
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class GetRelationInfoHandler : MessageLocationHandler<MapNode, GetRelationInfoReq, GetRelationInfoResp>
  {
    protected override async ETTask Run(MapNode nowMap, GetRelationInfoReq msg, GetRelationInfoResp resp)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        resp.SetError(getUserRet.Message);
        return;
      }
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      resp.RelationInfos = new(relationComponent.relationInfoMap);
      await ETTask.CompletedTask;
    }
  }
}
