namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ShowTaskListHandler : MessageLocationHandler<MapNode, ShowTaskListReq, ShowTaskListResp>
  {
    protected override async ETTask Run(MapNode nowMap, ShowTaskListReq request, ShowTaskListResp response)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(request.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        response.SetError(getUserRet.Message);
        return;
      }
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      response.TaskListDaoInfo = taskComponent.GetTaskListDaoInfo();
      await ETTask.CompletedTask;
    }
  }
}