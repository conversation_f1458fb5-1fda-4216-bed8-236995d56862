using System;

namespace MaoYouJi
{
  [Event(SceneType.Map)]
  public class UserKillMonsterDropEventHandler : AEvent<Scene, UserKillMonsterEvent>
  {
    protected override async ETTask Run(Scene root, UserKillMonsterEvent args)
    {
      User killedBy = args.killedBy;
      MonsterInfo deader = args.deader;
      if (deader.monDescType == MonDescType.BOSS)
      {
        // 击杀BOSS后，如果怪物有拥有者，则怪物拥有者获得掉落
        if (deader.ownUser != 0)
        {
          User monsterUser = GlobalInfoCache.Instance.GetOnlineUser(deader.ownUser);
          if (monsterUser != null)
          {
            // monsterUser.GetComponent<BagComponent>().AddItem(deader.dropTableIds);
          }
        }
        else
        {
          // 否则全队伍掉落
          // TeamSystem teamSystem = null;
          // if (user.teamInfo != null)
          // {
          //   teamSystem = TeamSystem.getTeamSystem(user.teamInfo.teamId);
          // }
          // if (teamSystem != null)
          // {
          //   bagProc.teamDropThing(teamSystem, monsterInfo, baseMonster.dropTableIds);
          // }
          // else
          // {
          //   bagProc.monDropThing(userCacheInfo, monsterInfo, baseMonster.dropTableIds);
          // }
        }
      }
      else
      {
        // 普通怪物只对击杀者掉落
        // bagProc.monDropThing(userCacheInfo, monsterInfo, baseMonster.dropTableIds);
      }
      await ETTask.CompletedTask;
    }
  }
}