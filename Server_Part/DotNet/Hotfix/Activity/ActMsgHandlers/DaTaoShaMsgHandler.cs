using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageHandler(SceneType.Global)]
  public class ClientEntryDaToShaMsgHandler : MessageHandler<Entity, ClientEntryDaToShaMsg>
  {
    protected override async ETTask Run(Entity entity, ClientEntryDaToShaMsg msg)
    {
      using (await entity.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DaTaoSha, msg.UserId))
      {
        LogicRet logicRet = UserProcSystem.GetUserWithCheck(msg.UserId, out User user);
        if (logicRet != LogicRet.Success)
        {
          user?.SendToast(logicRet.Message);
          return;
        }
        DaTaoShaActComp daTaoShaActComp = entity.Root().GetComponent<DaTaoShaActComp>();
        logicRet = daTaoShaActComp.CanEnterDaTaoSha(user);
        if (logicRet != LogicRet.Success)
        {
          user.SendToast(logicRet.Message);
          return;
        }
        // 随机移动到岛上的一个点
        MoveComponent moveComponent = user.GetComponent<MoveComponent>();
        List<string> dataShaPoints = GlobalInfoCache.Instance.mapPoints[MapNameConstant.DaTaoShaDao];
        string randomPoint = RandomGenerator.RandomArray(dataShaPoints);
        InnerMoveUserReq outMoveUserReq = new InnerMoveUserReq
        {
          TargetUserId = msg.UserId,
          OutNowMapName = moveComponent.nowMap,
          OutNowPointName = moveComponent.nowPoint,
          TargetMapName = MapNameConstant.DaTaoShaDao,
          TargetPointName = randomPoint,
          IsForceMove = false
        };
        InnerMoveUserResp outMoveUserResp = await entity.Root().GetComponent<MessageSender>().Call(user.GetParent<MapNode>().GetActorId(), outMoveUserReq) as InnerMoveUserResp;
        if (outMoveUserResp.Error != 0)
        {
          ETLog.Error($"进入大逃杀活动失败: {outMoveUserResp.Error} {outMoveUserResp.showMessage}");
          user.SendToast("进入大逃杀活动失败");
          return;
        }
        user.AddComponent<UserDaTaoShaInfoComp, DaTaoShaActComp>(daTaoShaActComp);
      }
    }
  }

  [MessageHandler(SceneType.Global)]
  public class ClientExitDaToShaMsgHandler : MessageHandler<Entity, ClientExitDaToShaMsg>
  {
    protected override async ETTask Run(Entity entity, ClientExitDaToShaMsg msg)
    {
      using (await entity.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DaTaoSha, msg.UserId))
      {
        LogicRet logicRet = UserProcSystem.GetUserWithCheck(msg.UserId, out User user, true);
        if (logicRet != LogicRet.Success)
        {
          user?.SendToast(logicRet.Message);
          return;
        }
        DaTaoShaActComp daTaoShaActComp = entity.Root().GetComponent<DaTaoShaActComp>();
        logicRet = daTaoShaActComp.CanExitDaTaoSha(user);
        if (logicRet != LogicRet.Success)
        {
          user.SendToast(logicRet.Message);
          return;
        }

        string targetMapName = MapNameConstant.MaoYinCun;
        string targetPointName = "猫隐村广场";

        // 移动用户离开大逃杀岛
        MoveComponent moveComponent = user.GetComponent<MoveComponent>();
        InnerMoveUserReq outMoveUserReq = new InnerMoveUserReq
        {
          TargetUserId = msg.UserId,
          OutNowMapName = moveComponent.nowMap,
          OutNowPointName = moveComponent.nowPoint,
          TargetMapName = targetMapName,
          TargetPointName = targetPointName,
          IsForceMove = false // 强制移动
        };
        InnerMoveUserResp outMoveUserResp = await entity.Root().GetComponent<MessageSender>().Call(user.GetParent<MapNode>().GetActorId(), outMoveUserReq) as InnerMoveUserResp;
        if (outMoveUserResp.Error != 0)
        {
          ETLog.Error($"离开大逃杀活动失败: {outMoveUserResp.Error} {outMoveUserResp.showMessage}");
          user.SendToast("离开大逃杀活动失败");
          return;
        }

        // 恢复用户数据
        daTaoShaActComp.QuitDaTaoSha(user);

        int count = daTaoShaActComp.DaTaoShaUserList.Count;
        ETLog.Info($"离开大逃杀活动: {msg.UserId} {count}");
        ChatProSystem.SendMessageToAllUser(new ServerSendChatMsg
        {
          content = $"【大逃杀】 <u>{user.nickname}</u> 离开了大逃杀岛({count})！",
          chatType = ChatType.World_Chat
        });

        user.SendToast("成功离开大逃杀活动");
      }
    }
  }
}