namespace MaoYouJi
{
  [MessageHandler(SceneType.Global)]
  [FriendOf(typeof(DaTaoShaActComp))]
  public class InnerUserQuitDaTaoShaHandler : MessageHandler<Entity, InnerUserQuitDaTaoSha>
  {
    protected override async ETTask Run(Entity entity, InnerUserQuitDaTaoSha msg)
    {
      User targetUser = GlobalInfoCache.Instance.GetOnlineUser(msg.targetUserId);
      if (targetUser == null)
      {
        return;
      }
      DaTaoShaActComp daTaoShaActComp = entity.Root().GetComponent<DaTaoShaActComp>();
      daTaoShaActComp.QuitDaTaoSha(targetUser, msg.killedSrc, msg.isBoom);
      await ETTask.CompletedTask;
    }
  }
}