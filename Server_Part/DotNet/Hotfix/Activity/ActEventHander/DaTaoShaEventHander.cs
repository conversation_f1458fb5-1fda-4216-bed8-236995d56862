namespace MaoYouJi
{
  // 禁止玩家联合
  [Event(SceneType.Map)]
  public class MapUserMoveEvent_ForbidUserUnion : AEvent<Scene, MapUserMoveEvent>
  {
    protected override async ETTask Run(Scene root, MapUserMoveEvent args)
    {
      MapNode fromMapNode = args.fromMapNode;
      MapNode toMapNode = args.toMapNode;
      if (fromMapNode.mapName != MapNameConstant.DaTaoShaDao && toMapNode.mapName != MapNameConstant.DaTaoShaDao)
        return;
      await ETTask.CompletedTask;
    }
  }

  // 大逃杀用户死亡，需要退出大逃杀
  [Event(SceneType.Map)]
  public class DaTaoShaUserQuitAttackEventHandler : AEvent<Scene, UserQuitAttackEvent>
  {
    protected override async ETTask Run(Scene root, UserQuitAttackEvent args)
    {
      User user = args.user;
      MapNode mapNode = user.GetParent<MapNode>();
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (mapNode.mapName == MapNameConstant.DaTaoShaDao && attackComponent.LiveState == LiveStateEnum.DEAD)
      {
        KilledComponent killedComponent = attackComponent.GetComponent<KilledComponent>();
        FightInfo killedSrc = killedComponent?.killed;
        root.GetComponent<MessageSender>().Send(GlobalActorInfo.Instance.GlobalActorId, new InnerUserQuitDaTaoSha()
        {
          targetUserId = user.Id,
          killedSrc = killedSrc,
          isBoom = false
        });
        MapNode newMapNode = GlobalInfoCache.Instance.GetMapNode(MapNameConstant.MaoYinCun, "猫隐村广场");
        user.GetComponent<MoveComponent>().MoveTo(newMapNode);
      }
      await ETTask.CompletedTask;
    }
  }
}