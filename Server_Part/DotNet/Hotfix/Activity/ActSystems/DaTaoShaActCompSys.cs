using System.Linq;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(DaTaoShaActComp))]
  [FriendOf(typeof(DaTaoShaActComp))]
  public static partial class DaTaoShaActCompSys
  {
    [EntitySystem]
    private static void Awake(this DaTaoShaActComp self, DaTaoShaActConf conf)
    {
      self.State = 0;
      self.StopEnterTime = conf.stopEnterTime;
      self.AddForbiddenInterval = conf.addForbiddenInterval;
      self.KillUserInterval = conf.killUserInterval;
      self.ForbiddenAreas = conf.forbiddenAreas;
      self.PreCalcSubArea = conf.preCalcSubArea;
      self.NowForbiddenAreaIndex = 0;
      GlobalInfoCache.Instance.daTaoShaActComp = self;
    }

    public static LogicRet CanEnterDaTaoSha(this DaTaoShaActComp self, User user)
    {
      if (self.State == 0)
      {
        return LogicRet.Failed("大逃杀活动没有开始哦！");
      }
      else if (self.State == 2)
      {
        return LogicRet.Failed("大逃杀活动入口已经关闭了，下次请早点来吧！");
      }
      if (self.DaTaoShaUserList.Count >= 100)
      {
        return LogicRet.Failed("大逃杀活动人数已满，请下次再来吧！");
      }
      MapNode mapNode = user.GetParent<MapNode>();
      if (mapNode == null || mapNode.nodeType != MapNodeType.CITY)
      {
        return LogicRet.Failed("只能在城镇中进入大逃杀！");
      }
      if (user.HasUserState(UserStateEnum.Evil_State))
      {
        return LogicRet.Failed("红名状态下无法进入大逃杀！");
      }
      if (user.activityName != ActNameEnum.None)
      {
        return LogicRet.Failed("您当前正在参与其他活动，请先退出哦！");
      }
      if (user.teamInfo != null)
      {
        return LogicRet.Failed("您当前正在队伍中，请先退出队伍哦！");
      }
      if (user.IsInVfxSystem())
      {
        return LogicRet.Failed("挂机状态无法进入活动地图！");
      }
      return LogicRet.Success;
    }

    public static LogicRet CanExitDaTaoSha(this DaTaoShaActComp self, User user)
    {
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("您当前没有参与大逃杀活动！");
      }
      UserDaTaoShaInfoComp userDaTaoShaInfoComp = user.GetComponent<UserDaTaoShaInfoComp>();
      if (userDaTaoShaInfoComp == null)
      {
        return LogicRet.Failed("您当前没有参与大逃杀活动！");
      }
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      if (moveComponent.nowMap != MapNameConstant.DaTaoShaDao)
      {
        return LogicRet.Failed("只能在大逃杀岛上离开活动！");
      }
      return LogicRet.Success;
    }

    public static void ClearUserAttackStates(this DaTaoShaActComp self, User user)
    {
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      attackComponent.RemoveComponent<InFightComponent>();
      attackComponent.RemoveComponent<KilledComponent>();
      attackComponent.blood = attackComponent.maxBlood;
      attackComponent.blue = attackComponent.maxBlue;
      attackComponent.damageReduce = 0;
      attackComponent.damageAdd = 0;
      attackComponent.exp = 0;
      attackComponent.maxFightNum = 3;
      skillComponent.skillMap.Clear();
      skillComponent.skillIds.Clear();
      for (int i = 0; i < 12; ++i)
      {
        skillComponent.equipSkills[i] = SkillIdEnum.None;
      }
      for (int i = 0; i < 10; ++i)
      {
        skillComponent.quickFoods[i] = 0;
      }
      equipComponent.equipList.Clear();
      equipComponent.specialEquipList.Clear();
      skillComponent.skillCool.Clear();
      attackComponent.LiveState = LiveStateEnum.ALIVE;
    }

    public static void QuitDaTaoSha(this DaTaoShaActComp self, User user, FightInfo src = null, bool boom = false)
    {
      UserDaTaoShaInfoComp userDaTaoShaInfoComp = user.GetComponent<UserDaTaoShaInfoComp>();
      if (userDaTaoShaInfoComp == null)
      {
        return;
      }
      self.DaTaoShaUserList.TryRemove(user.Id);
      int remainCount = self.DaTaoShaUserList.Count;
      ETLog.Info($"大逃杀用户退出：{user.Id} {boom} {remainCount}");
      // if (user.checkLiveState(LiveStateEnum.FIGHTING)) {
      //   attackProc.outCallQuitAttack(user, null);
      // }
      if (remainCount > 0)
      {
        ServerSendChatMsg chatMsg = new ServerSendChatMsg();
        chatMsg.chatType = ChatType.World_Chat;
        if (src != null)
        {
          chatMsg.content = "【大逃杀】 " + user.nickname + " 被 "
              + (src.liveType == LiveType.ROLE ? "玩家" : "怪物") + " "
              + src.fightName + " 杀死了（" + remainCount + "）";
        }
        else if (boom)
        {
          chatMsg.content = "【大逃杀】 " + user.nickname + " 因身处禁区被炸死了（"
              + remainCount + "）";
        }
        else
        {
          chatMsg.content = "【大逃杀】 " + user.nickname + " 退出了大逃杀岛（"
              + remainCount + "）";
        }
        ChatProSystem.SendMessageToAllUser(chatMsg);
      }
      // 活动开启击杀后，退出时需要计算排名和奖章
      int jiangZhangCount = 0;
      if (self.State == 2)
      {
        userDaTaoShaInfoComp.ranking = remainCount + 1;
        user.SendToast("您退出了大逃杀岛，排名为第" + userDaTaoShaInfoComp.ranking + "名");
        if (userDaTaoShaInfoComp.ranking <= 10)
        {
          jiangZhangCount += 11 - userDaTaoShaInfoComp.ranking;
        }
        jiangZhangCount += userDaTaoShaInfoComp.killOver6;
      }
      user.RemoveComponent<UserDaTaoShaInfoComp>();
      if (jiangZhangCount > 0)
      {
        BagComponent bagComponent = user.GetComponent<BagComponent>();
        bagComponent.GiveThing(new ThingGiveInfo
        {
          thingName = ThingNameEnum.DaTaoSha_JiangZhang,
          ownType = OwnType.PRIVATE,
          num = userDaTaoShaInfoComp.jiangZhangCount
        }, ThingFromType.Activity);
      }
      if (self.State == 2 && remainCount == 0)
      {
        self.EndDaTaoSha(user.GetComponent<AttackComponent>().fightInfo);
      }
      user.RecalcUserAttrs();
      if (self.State == 2 && remainCount == 1)
      {
        long lastUserId = self.DaTaoShaUserList.First();
        self.QuitDaTaoSha(GlobalInfoCache.Instance.GetOnlineUser(lastUserId), null, false);
      }
    }

    public static void EndDaTaoSha(this DaTaoShaActComp self, FightInfo winner)
    {
      ETLog.Info("大逃杀活动结束");
      self.State = 0;
      if (winner != null)
      {
        ChatProSystem.SendMessageToAllUser(new ServerSendChatMsg
        {
          content = "【大逃杀】大逃杀活动结束！<u>" + winner.fightName + "</u> 成为了大逃杀的冠军！",
          chatType = ChatType.World_Chat
        });
      }
      else
      {
        ChatProSystem.SendMessageToAllUser(new ServerSendChatMsg
        {
          content = "【大逃杀】大逃杀活动结束！没有胜出者！",
          chatType = ChatType.World_Chat
        });
      }
    }
  }
}