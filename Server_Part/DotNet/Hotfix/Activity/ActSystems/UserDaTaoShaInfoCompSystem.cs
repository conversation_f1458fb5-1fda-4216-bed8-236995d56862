namespace MaoYouJi
{
  [EntitySystemOf(typeof(UserDaTaoShaInfoComp))]
  [FriendOf(typeof(UserDaTaoShaInfoComp))]
  public static partial class UserDaTaoShaInfoCompSystem
  {
    [EntitySystem]
    private static void Awake(this UserDaTaoShaInfoComp self, DaTaoShaActComp daTaoShaActComp)
    {
      User user = self.GetParent<User>();
      user.activityName = ActNameEnum.Da_TaoSha;
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      self.bagDaoInfo = bagComponent.GetBagDaoInfo();
      self.attackDaoInfo = attackComponent.GetAttackDaoInfo();
      int count = daTaoShaActComp.DaTaoShaUserList.Count;
      ETLog.Info($"进入大逃杀活动: {user.Id} {count}");
      ChatProSystem.SendMessageToAllUser(new ServerSendChatMsg
      {
        content = $"【大逃杀】 <u>{user.nickname}</u> 进入了大逃杀岛({count})！",
        chatType = ChatType.World_Chat
      });
      daTaoShaActComp.DaTaoShaUserList.Add(user.Id);
    }

    [EntitySystem]
    private static void Destroy(this UserDaTaoShaInfoComp self)
    {
      User user = self.GetParent<User>();
      user.activityName = ActNameEnum.None;

      // 恢复攻击组件数据
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (self.attackDaoInfo != null)
      {
        attackComponent.ParseAttackDaoInfo(self.attackDaoInfo);
      }

      // 恢复背包组件数据
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      if (self.bagDaoInfo != null)
      {
        bagComponent.ParseBagDaoInfo(self.bagDaoInfo);
      }

      // 重置活动名称
      user.activityName = ActNameEnum.None;
    }
  }
}