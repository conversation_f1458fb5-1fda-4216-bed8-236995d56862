namespace MaoYouJi
{
  [Event(SceneType.Map)]
  public class MapStartTaskEventHandler : AEvent<Scene, StartTaskEvent>
  {
    protected override async ETTask Run(Scene scene, StartTaskEvent taskEvent)
    {
      User user = taskEvent.user;
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      BaseTask task = taskEvent.task;
      if (task.taskSubType == TaskSubTypeEnum.BiaoChe_Task)
      {
        int level = task.preCondLists[0].preCondNum;
        BaseMonster baseMonster = GlobalInfoCache.Instance.GetBaseMonster(MonBaseType.BiaoChe, true);
        if (baseMonster == null)
        {
          ETLog.Error($"找不到BaseMonster {MonBaseType.BiaoChe}");
          return;
        }
        baseMonster.level = [level, level];
        baseMonster.maxBlood = [50 + level * 1, 50 + level * 1];
        baseMonster.moveEvent.maxStep = 1;
        baseMonster.moveEvent.targetMap = task.targetMap;
        baseMonster.moveEvent.targetPoint = task.targetPoint;
        MapNode mapNode = user.GetParent<MapNode>();
        MonsterInfo monsterInfo = mapNode.AddChild<MonsterInfo, BaseMonster, User>(baseMonster, user);
        taskComponent.AddComponent<BiaoCheInfoComp, MonsterInfo>(monsterInfo);
      }
      else if (task.idEnum == TaskIdEnum.Job_QiangFaDe_ChuangChen_10)
      {
        // 霸王枪任务写死怪物生成
        // try
        // {
        //   Pair<MonsterInfo, MapNode> pair = monsterProc.genMonster(MonBaseType.BaiWangQiang_XiaoTuDi, nowMap.mapName,
        //       nowMap.pointName, userCacheInfo.user);
        //   if (pair == null || pair.getFirst() == null)
        //   {
        //     return;
        //   }
        //   attackSche.startAttackBase(nowMap, pair.getFirst().getFightInfo(), user.getFightInfo());
        // }
        // catch (Exception e)
        // {
        //   LogUtil.ERROR(e);
        // }
      }
      await ETTask.CompletedTask;
    }
  }

  [Event(SceneType.Map)]
  public class MapUserQuitAttackEventHandler : AEvent<Scene, UserQuitAttackEvent>
  {
    protected override async ETTask Run(Scene scene, UserQuitAttackEvent userQuitAttackEvent)
    {
      User user = userQuitAttackEvent.user;
      MapNode mapNode = user.GetParent<MapNode>();
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      // 大逃杀地图，用户死亡，需要移除用户，不在这里处理
      if (mapNode.mapName != MapNameConstant.DaTaoShaDao || attackComponent.LiveState != LiveStateEnum.DEAD)
      {
        mapNode.PutUserInMapNode(userQuitAttackEvent.user);
      }
      await ETTask.CompletedTask;
    }
  }

  [Event(SceneType.Map)]
  public class MapMonsterQuitAttackEventHandler : AEvent<Scene, MonsterQuitAttackEvent>
  {
    protected override async ETTask Run(Scene scene, MonsterQuitAttackEvent monsterQuitAttackEvent)
    {
      MonsterInfo monsterInfo = monsterQuitAttackEvent.monsterInfo;
      MapNode mapNode = monsterInfo.GetParent<MapNode>();
      mapNode.PutMonInMapNode(monsterInfo);
      AttackComponent attackComponent = monsterInfo.GetComponent<AttackComponent>();
      if (attackComponent.LiveState != LiveStateEnum.DEAD)
      {
        attackComponent.blood = attackComponent.maxBlood;
        attackComponent.blue = attackComponent.maxBlue;
      }
      await ETTask.CompletedTask;
    }
  }
}