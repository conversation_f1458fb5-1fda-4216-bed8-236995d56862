using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using ConcurrentCollections;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(FiberMapManage))]
  [FriendOf(typeof(FiberMapManage))]
  [FriendOf(typeof(MapNode))]
  public static partial class FiberMapManageSystem
  {
    [EntitySystem]
    public static void Awake(this FiberMapManage self)
    {
      LoadMapNode(self).Coroutine();
    }

    public static async ETTask LoadMapNode(this FiberMapManage self)
    {
      DBManagerComponent dbManagerComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>();
      DBComponent dbComponent = dbManagerComponent.GetMyZoneDB();
      DBComponent netDbComponent = dbManagerComponent.GetZoneDB(1);
      List<BaseMapNode> baseMapNodes = await netDbComponent.QueryClass<BaseMapNode>(n => n.pointName != null);
      List<BaseNpc> baseNpcs = await netDbComponent.QueryClass<BaseNpc>(n => n.name != NpcNameEnum.None);
      Dictionary<string, List<BaseNpc>> npcInfos = new();
      foreach (BaseNpc baseNpc in baseNpcs)
      {
        if (!npcInfos.TryGetValue(baseNpc.nowMap + "_" + baseNpc.nowPoint, out var npcList))
        {
          npcList = [];
          npcInfos[baseNpc.nowMap + "_" + baseNpc.nowPoint] = npcList;
        }
        npcList.Add(baseNpc);
      }
      ETLog.Info($"地图节点加载开始，地图节点总数：{baseMapNodes.Count}");
      int nowFiberId = self.Root().Fiber.Id;
      foreach (BaseMapNode baseMapNode in baseMapNodes)
      {
        // 根据地图名称获取纤程ID
        int fiberId = MapProcSystem.GetMapNodeFiberId(baseMapNode.mapName);
        if (fiberId == nowFiberId)
        {
          MapNode mapNode = self.AddChild<MapNode>();
          mapNode.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.UnOrderedMessage);
          mapNode.mapName = baseMapNode.mapName;
          mapNode.pointName = baseMapNode.pointName;
          mapNode.monstorNum = baseMapNode.monstorNum;
          mapNode.nodeType = baseMapNode.nodeType;
          mapNode.nodeStates = baseMapNode.nodeStates;
          mapNode.nears = baseMapNode.nears;
          mapNode.stepLimit = baseMapNode.stepLimit;
          mapNode.outMap = baseMapNode.outMap;
          mapNode.userInPoint = new();
          mapNode.npcInPoint = new();
          mapNode.monInPoint = new();
          mapNode.AddComponent<MapAttackManage>();
          GenMonComponent genMonComponent = mapNode.AddComponent<GenMonComponent, ConcurrentHashSet<MonsterGen>, ConcurrentDictionary<MonBaseType, int>>(baseMapNode.monsterGens, baseMapNode.fixedMonsters);
          while (mapNode.monInPoint.Count < mapNode.monstorNum && genMonComponent.monsterGens.Count > 0)
          {
            genMonComponent.GenOneNormalMon();
          }
          self.mapNodes.TryAdd(mapNode.GetMapNodeQueryKey(), mapNode);
          if (npcInfos.TryGetValue(mapNode.GetMapNodeQueryKey(), out var npcList))
          {
            foreach (BaseNpc baseNpc in npcList)
            {
              NpcInfo npcInfo = mapNode.AddChild<NpcInfo, BaseNpc>(baseNpc);
              mapNode.PutNpcInMapNode(npcInfo);
              GlobalInfoCache.Instance.AddNpcInfo(npcInfo);
            }
          }
          GlobalInfoCache.Instance.AddMapNode(mapNode.mapName, mapNode.pointName, mapNode);
        }
      }
      if (GlobalInfoCache.Instance.allMapNodeCache.Count == baseMapNodes.Count)
      {
        List<EntityRef<MapNode>> allMapNodes = [.. GlobalInfoCache.Instance.allMapNodeCache.Values];
        buildMapPoints(allMapNodes);
        calcDistMap(allMapNodes);
      }
      ETLog.Info($"{self.Root().Name} 地图节点加载完成，当前地图节点数量：{self.mapNodes.Count}");
    }

    public static void buildMapPoints(List<EntityRef<MapNode>> mapNodes)
    {
      ConcurrentDictionary<string, List<string>> mapPoints = new();
      foreach (MapNode mapNode in mapNodes)
      {
        if (!mapPoints.TryGetValue(mapNode.mapName, out var mapPoint))
        {
          mapPoint = [];
          mapPoints[mapNode.mapName] = mapPoint;
        }
        mapPoint.Add(mapNode.pointName);
      }
      ETLog.Info($"地图点构建完成：{mapPoints.Count}");
      GlobalInfoCache.Instance.mapPoints = mapPoints;
    }

    /// <summary>
    /// 计算所有地图点之间的最短路径信息，并存入全局缓存。
    /// </summary>
    /// <param name="mapNodes">所有地图节点的列表</param>
    public static void calcDistMap(List<EntityRef<MapNode>> mapNodes)
    {
      GlobalInfoCache.Instance.dijkstraInfoMap = new();
      GlobalInfoCache.Instance.dijkstraInfoForCity = new();
      // 用于存储每个地图下每个点的相邻点
      var mapDist = new Dictionary<string, Dictionary<string, List<string>>>();

      // 新增：收集所有出地图节点
      var outMapNodes = new Dictionary<string, HashSet<string>>();
      foreach (MapNode mapNode in mapNodes)
      {
        // 假设nodeType==MapNodeType.OutMap为出地图节点，请根据你的实际判断条件修改
        if (mapNode.nodeStates.Contains(MapNodeState.OUT_MAP))
        {
          if (!outMapNodes.TryGetValue(mapNode.mapName, out var outMapNode))
          {
            outMapNode = new HashSet<string>();
            outMapNodes[mapNode.mapName] = outMapNode;
          }
          outMapNode.Add(mapNode.pointName);
        }

        // 初始化每个地图下的点字典
        if (!mapDist.TryGetValue(mapNode.mapName, out var pointDict))
        {
          pointDict = new Dictionary<string, List<string>>();
          mapDist[mapNode.mapName] = pointDict;
        }
        // 初始化每个点的邻居列表
        if (!pointDict.TryGetValue(mapNode.pointName, out var mapList))
        {
          mapList = new List<string>();
          pointDict[mapNode.pointName] = mapList;
        }
        // 添加所有邻居
        foreach (var near in mapNode.nears)
        {
          mapList.Add(near.pointName);
        }
      }

      // 对每个地图的每个点，计算其到所有其他点的最短路径
      foreach (var entry in mapDist)
      {
        string mapName = entry.Key;
        var pointDist = entry.Value;
        foreach (var pointEntry in pointDist)
        {
          var dijkstraInfo = new DijkstraInfo();
          string pointName = pointEntry.Key;
          try
          {
            // 计算以pointName为起点的最短路径，传入outMapNodes
            dijkstra(pointDist, pointName, dijkstraInfo.distance, dijkstraInfo.previous, outMapNodes.TryGetValue(mapName, out var outMapNode) ? outMapNode : null);
          }
          catch (Exception e)
          {
            ETLog.Error($"{mapName}_{pointName} 计算最短路径出错: {e}");
            break;
          }
          // 缓存结果，key为"地图名_点名"
          GlobalInfoCache.Instance.dijkstraInfoMap[$"{mapName}_{pointName}"] = dijkstraInfo;
        }
      }
      ETLog.Info($"地图距离计算完成：{GlobalInfoCache.Instance.dijkstraInfoMap.Count}");

      // 计算城市之间的最短路径（城市视为特殊地图）
      foreach (var mapName in MapConstant.mapNearMaps.Keys)
      {
        var dijkstraInfo = new DijkstraInfo();
        try
        {
          dijkstra(MapConstant.mapNearMaps, mapName, dijkstraInfo.distance, dijkstraInfo.previous);
          GlobalInfoCache.Instance.dijkstraInfoForCity[mapName] = dijkstraInfo;
        }
        catch (Exception e)
        {
          ETLog.Error($"{mapName} 计算城市最短路径出错: {e}");
        }
      }
      ETLog.Info($"城市距离计算完成：{GlobalInfoCache.Instance.dijkstraInfoForCity.Count}");
    }

    /// <summary>
    /// Dijkstra最短路径算法实现
    /// </summary>
    /// <param name="dist">图的邻接表，key为点名，value为相邻点列表</param>
    /// <param name="start">起点</param>
    /// <param name="distances">输出：每个点到起点的最短距离</param>
    /// <param name="previous">输出：每个点在最短路径上的前驱点</param>
    /// <param name="outMapNodes">所有出地图节点的点名集合</param>
    public static void dijkstra(
        Dictionary<string, List<string>> dist,
        string start,
        Dictionary<string, int> distances,
        Dictionary<string, string> previous,
        HashSet<string> outMapNodes = null
    )
    {
      // 获取所有节点
      var nodes = dist.Keys;
      // 优先队列，按距离排序，(距离, 节点名)
      var priorityQueue = new SortedSet<(int dist, string node)>();
      // 初始化所有点的距离和前驱
      foreach (var node in nodes)
      {
        distances[node] = node == start ? 0 : int.MaxValue; // 起点距离为0，其它为无穷大
        previous[node] = null;
      }
      priorityQueue.Add((0, start)); // 起点入队

      while (priorityQueue.Count > 0)
      {
        // 取出当前距离最小的点
        var (curDist, current) = priorityQueue.Min;
        priorityQueue.Remove(priorityQueue.Min);

        // 新增逻辑：如果当前节点是出地图节点且不是起点，则不再扩展
        if (outMapNodes != null && outMapNodes.Contains(current) && current != start)
          continue;

        // 获取当前点的所有邻居
        if (!dist.TryGetValue(current, out var neighbors))
          continue;
        foreach (var neighbor in neighbors)
        {
          int alt = distances[current] + 1; // 假设所有边权重为1
          if (alt < distances[neighbor])
          {
            // 如果优先队列中已存在该邻居，先移除旧的
            if (distances[neighbor] != int.MaxValue)
            {
              priorityQueue.Remove((distances[neighbor], neighbor));
            }
            // 更新距离和前驱
            distances[neighbor] = alt;
            previous[neighbor] = current;
            // 重新加入优先队列
            priorityQueue.Add((alt, neighbor));
          }
        }
      }
    }
  }
}