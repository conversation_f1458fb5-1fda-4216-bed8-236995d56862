using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [FriendOf(typeof(MapNode))]
  public static partial class MapNodeSystem
  {
    public static void PutUserInMapNode(this MapNode self, User user, bool sendMessage = true)
    {
      // 如果目标地图的线程和用户线程不一致，则转移用户
      if (user.GetParent<MapNode>() == null || user.GetParent<MapNode>().Id != self.Id)
      {
        Fiber preFiber = user.Fiber();
        if (preFiber.Id != self.Fiber().Id)
        {
          preFiber.Root.GetComponent<FiberUsersComponent>().TransferUser(user, self.Fiber());
        }
        self.AddChild(user);
      }
      if (self.nodeStates.Contains(MapNodeState.No_Add_User) || user.HasUserState(UserStateEnum.YingShen_State))
      {
        return;
      }
      UserInMap userInMap = user.GetUserInMap();
      self.userInPoint.TryGetValue(userInMap.id, out UserInMap preUserInMap);
      if (preUserInMap != null)
      {
        userInMap.time = preUserInMap.time;
      }
      else
      {
        userInMap.time = TimeInfo.Instance.ServerNow();
      }
      self.userInPoint[userInMap.id] = userInMap;
      if (sendMessage)
      {
        MapUpdateSystem.SendUpdateInfo(self, userInMap);
      }
      return;
    }
    public static void RemoveUserInMapNode(this MapNode self, User user)
    {
      self.userInPoint.TryRemove(user.Id, out _);
      MapUpdateSystem.SendRemoveInfo(self, user.Id);
    }
    public static HashSet<long> GetMapUsers(this MapNode self)
    {
      return [.. self.userInPoint.Keys];
    }

    public static void PutNpcInMapNode(this MapNode self, NpcInfo npcInfo, bool sendMessage = true)
    {
      self.npcInPoint.TryGetValue(npcInfo.Id, out SimpleNpc preNpcInMap);
      SimpleNpc simpleNpc = npcInfo.GetSimpleNpc();
      if (preNpcInMap != null)
      {
        simpleNpc.time = preNpcInMap.time;
      }
      else
      {
        simpleNpc.time = TimeInfo.Instance.ServerNow();
      }
      self.npcInPoint[simpleNpc.id] = simpleNpc;
      if (npcInfo.GetParent<MapNode>().Id != self.Id)
      {
        self.AddChild(npcInfo);
      }
      if (sendMessage)
      {
        MapUpdateSystem.SendUpdateInfo(self, null, simpleNpc);
      }
      return;
    }

    public static void RemoveNpcInMapNode(this MapNode self, NpcInfo npcInfo)
    {
      self.npcInPoint.TryRemove(npcInfo.Id, out _);
      MapUpdateSystem.SendRemoveInfo(self, npcInfo.Id);
    }

    public static HashSet<NpcNameEnum> GetNpcInMapNode(this MapNode self)
    {
      return [.. self.npcInPoint.Values.Select(npc => npc.name)];
    }

    public static void PutMonInMapNode(this MapNode self, MonsterInfo monster, bool sendMessage = true)
    {
      self.monInPoint.TryGetValue(monster.Id, out SimpleMon preMonsterInMap);
      SimpleMon simpleMon = monster.GetSimpleMon();
      if (preMonsterInMap != null)
      {
        simpleMon.time = preMonsterInMap.time;
      }
      else
      {
        simpleMon.time = TimeInfo.Instance.ServerNow();
      }
      self.monInPoint[simpleMon.id] = simpleMon;
      if (monster.GetParent<MapNode>().Id != self.Id)
      {
        self.AddChild(monster);
      }
      if (sendMessage)
      {
        MapUpdateSystem.SendUpdateInfo(self, null, null, simpleMon);
      }
      return;
    }

    public static void RemoveMonInMapNode(this MapNode self, MonsterInfo monster)
    {
      self.monInPoint.TryRemove(monster.Id, out _);
      MapUpdateSystem.SendRemoveInfo(self, monster.Id);
    }

    public static HashSet<MonBaseType> GetMonInMapNode(this MapNode self)
    {
      return [.. self.monInPoint.Values.Select(mon => mon.monBaseType)];
    }

    public static void SendMessageToMapUser(this MapNode self, MaoYouMessage message, long exceptUserId = 0)
    {
      List<long> userIds = new(self.userInPoint.Keys);
      foreach (long userId in userIds)
      {
        User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
        if (user == null || user.Id == exceptUserId)
        {
          continue;
        }
        user.SendMessage(message);
      }
    }

    public static void SendMessagesToMapUser(this MapNode self, params MaoYouMessage[] messages)
    {
      List<long> userIds = new(self.userInPoint.Keys);
      foreach (long userId in userIds)
      {
        User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
        user.SendMessage(messages);
      }
    }

    public static LogicRet GetUserWithCheck(this MapNode self, long userId, out User user, bool checkInMap = false, bool checkAlive = false)
    {
      user = GlobalInfoCache.Instance.GetOnlineUser(userId);
      if (user == null)
      {
        ETLog.Warning($"用户不存在: {userId}");
        return LogicRet.Failed("用户不存在");
      }
      if (checkInMap && user.GetParent<MapNode>().Id != self.Id)
      {
        ETLog.Warning($"用户已不在当前地图: {userId} {user.GetParent<MapNode>().Id} {self.Id}");
        return LogicRet.Failed("用户不在当前地图");
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (checkAlive && attackComponent.LiveState != LiveStateEnum.ALIVE)
      {
        ETLog.Info($"当前状态无法进行该操作: {userId} {attackComponent.LiveState}");
        return LogicRet.Failed("当前状态无法进行该操作");
      }
      return LogicRet.Success;
    }
  }
}