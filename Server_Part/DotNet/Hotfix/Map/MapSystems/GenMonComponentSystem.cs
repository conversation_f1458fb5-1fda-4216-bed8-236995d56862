using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using ConcurrentCollections;
using DotRecast.Core;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(GenMonComponent))]
  [FriendOf(typeof(GenMonComponent))]
  [FriendOf(typeof(MapNode))]
  public static partial class GenMonComponentSystem
  {
    [EntitySystem]
    public static void Awake(this GenMonComponent self, ConcurrentHashSet<MonsterGen> monsterGens, ConcurrentDictionary<MonBaseType, int> fixedMonsters)
    {
      self.monsterGens = monsterGens;
      self.fixedMonsters = fixedMonsters;

    }

    public static void GenOneNormalMon(this GenMonComponent self, MonBaseType monBaseType = MonBaseType.None, User ownUser = null)
    {
      MapNode mapNode = self.GetParent<MapNode>();
      if (monBaseType == MonBaseType.None)
      {
        monBaseType = self.GetNeedGenNormalMon();
      }
      if (monBaseType == MonBaseType.None)
      {
        return;
      }
      MonsterInfo monsterInfo = mapNode.AddChild<MonsterInfo, MonBaseType, User>(monBaseType, ownUser);
    }

    public static MonBaseType GetNeedGenNormalMon(this GenMonComponent self)
    {
      MapNode mapNode = self.GetParent<MapNode>();
      int nowNormalMonCount = 0;

      Dictionary<MonBaseType, int> monBaseTypeCount = new();
      mapNode.monInPoint.Values.ForEach(mon =>
      {
        if (mon.monDescType == MonDescType.NORMAL || mon.monDescType == MonDescType.NpcMon)
        {
          nowNormalMonCount++;
        }
        if (monBaseTypeCount.TryGetValue(mon.monBaseType, out int count))
        {
          monBaseTypeCount[mon.monBaseType] = count + 1;
        }
        else
        {
          monBaseTypeCount[mon.monBaseType] = 1;
        }
      });
      if (nowNormalMonCount >= mapNode.monstorNum)
      {
        return MonBaseType.None;
      }
      self.monsterGens.ForEach(monsterGen =>
      {
        if (!monBaseTypeCount.TryGetValue(monsterGen.monBaseName, out int count))
        {
          monBaseTypeCount[monsterGen.monBaseName] = 0;
        }
      });
      return monBaseTypeCount.OrderBy(mon => mon.Value).First().Key;
    }
  }
}