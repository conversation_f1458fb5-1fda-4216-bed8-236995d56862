using System.Collections.Generic;

namespace MaoYouJi
{
  [FriendOf(typeof(MapNode))]
  public static class MapProcSystem
  {
    public static string GetMapNodeQueryKey(string mapName, string pointName)
    {
      return mapName + "_" + pointName;
    }
    // 这个FiberId一旦确定就不会改变
    public static int GetMapNodeFiberId(string mapName)
    {
      long hash = mapName.GetLongHashCode();
      if (hash < 0)
      {
        hash = -hash;
      }
      int idx = (int)(hash % GlobalInfoCache.Instance.mapScenes.Count);
      return GlobalInfoCache.Instance.mapScenes[idx].Id;
    }

    public static List<MapNode> GetMapNodesFromCache(string mapName, bool countainOut = false)
    {
      List<string> mapPoints = GlobalInfoCache.Instance.mapPoints[mapName];
      List<MapNode> mapNodes = [];
      foreach (string point in mapPoints)
      {
        mapNodes.Add(GlobalInfoCache.Instance.allMapNodeCache[mapName + "_" + point]);
      }
      if (!countainOut)
      {
        mapNodes.RemoveAll(node => node == null || node.nodeStates.Contains(MapNodeState.OUT_MAP));
      }
      return mapNodes;
    }
  }
}
