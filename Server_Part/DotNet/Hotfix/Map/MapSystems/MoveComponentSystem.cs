using System.Collections.Generic;

namespace MaoYouJi
{
  [FriendOf(typeof(MoveComponent))]
  [FriendOf(typeof(MapNode))]
  public static partial class MoveComponentSystem
  {
    public static LogicRet MoveTo(this MoveComponent self, string mapName, string pointName)
    {
      MapNode targetMapNode = GlobalInfoCache.Instance.GetMapNode(mapName, pointName);
      if (targetMapNode == null)
      {
        ETLog.Error($"MoveTargetMapNode is null, mapName: {mapName}, pointName: {pointName}");
        return LogicRet.Failed("目标节点为空");
      }
      return MoveTo(self, targetMapNode);
    }

    // 直接移动，移动的时候要注意检查！
    public static LogicRet MoveTo(this MoveComponent self, MapNode targetMapNode, bool autoSend = false)
    {
      MapNode nowMapNode = self.Parent.GetParent<MapNode>();
      if (targetMapNode.nodeStates.Contains(MapNodeState.OUT_MAP))
      {
        PointInfo outMapInfo = targetMapNode.outMap;
        targetMapNode = GlobalInfoCache.Instance.GetMapNode(outMapInfo.mapName, outMapInfo.pointName);
      }
      if (!InnerRealMove(self, targetMapNode))
      {
        return LogicRet.Failed("移动失败");
      }
      string showName = null;
      long exceptUserId = 0;
      if (self.Parent is User user)
      {
        nowMapNode.RemoveUserInMapNode(user);
        if (user.HasUserState(UserStateEnum.YingShen_State))
        {
          autoSend = false;
        }
        showName = user.nickname;
        exceptUserId = user.Id;
        EventSystem.Instance.Publish(self.Scene(), new MapUserMoveEvent
        {
          user = user,
          fromMapNode = nowMapNode,
          toMapNode = targetMapNode
        });
      }
      else if (self.Parent is MonsterInfo monsterInfo)
      {
        nowMapNode.RemoveMonInMapNode(monsterInfo);
        showName = monsterInfo.monName;
        EventSystem.Instance.Publish(self.Scene(), new MapMonMoveEvent
        {
          monsterInfo = monsterInfo,
          fromMapNode = nowMapNode,
          toMapNode = targetMapNode
        });
      }
      else if (self.Parent is NpcInfo npcInfo)
      {
        nowMapNode.RemoveNpcInMapNode(npcInfo);
        showName = npcInfo.showName;
        EventSystem.Instance.Publish(self.Scene(), new MapNpcMoveEvent
        {
          npcInfo = npcInfo,
          fromMapNode = nowMapNode,
          toMapNode = targetMapNode
        });
      }
      if (autoSend)
      {
        nowMapNode.SendMessageToMapUser(new ServerSendChatMsg
        {
          content = "<u>" + showName + "</u>向<u>" + targetMapNode.mapName + "/" + targetMapNode.pointName + "</u>离开",
          chatType = ChatType.Local_Chat
        });
        targetMapNode.SendMessageToMapUser(new ServerSendChatMsg
        {
          content = "<u>" + showName + "</u>从<u>" + nowMapNode.mapName + "/" + nowMapNode.pointName + "</u>走了过来",
          chatType = ChatType.Local_Chat
        }, exceptUserId);
      }
      return LogicRet.Success;
    }

    public static LogicRet CanMove(this MoveComponent self, MapNode targetMapNode)
    {
      Entity parent = self.Parent;
      MapNode nowMapNode = parent.GetParent<MapNode>();
      if (parent is User user)
      {
        AttackComponent attackComponent = user.GetComponent<AttackComponent>();
        if (attackComponent.LiveState != LiveStateEnum.ALIVE)
        {
          return LogicRet.Failed("当前状态无法移动");
        }
      }
      else if (parent is MonsterInfo monsterInfo)
      {
        AttackComponent attackComponent = monsterInfo.GetComponent<AttackComponent>();
        if (attackComponent.LiveState != LiveStateEnum.ALIVE)
        {
          return LogicRet.Failed("当前状态无法移动");
        }
      }
      else if (parent is NpcInfo npcInfo)
      {
        return LogicRet.Success;
      }
      else
      {
        ETLog.Error($"MoveTo: Invalid parent type, parent: {parent.GetType()}");
        return LogicRet.Failed("无效的移动对象");
      }

      return LogicRet.Success;
    }

    public static LogicRet CanReach(this MoveComponent self, MapNode targetMapNode)
    {
      MapNode nowMapNode = self.Parent.GetParent<MapNode>();
      int stepLimit = self.GetStepLimit();
      if (stepLimit <= 0)
      {
        return LogicRet.Failed("当前行动力为0");
      }
      if (stepLimit < MapConstant.NoStepLimit)
      {
        DijkstraInfo dijkstraInfo = GlobalInfoCache.Instance.dijkstraInfoMap[nowMapNode.mapName + "_" + nowMapNode.pointName];
        bool isReach = dijkstraInfo.distance.TryGetValue(targetMapNode.pointName, out int step);
        if (!isReach)
        {
          return LogicRet.Failed("目标节点不可到达");
        }
        if (step > stepLimit)
        {
          return LogicRet.Failed($"需要行动力{step}步，您为{stepLimit}步");
        }
      }
      return LogicRet.Success;
    }

    public static int GetStepLimit(this MoveComponent self)
    {
      MapNode mapNode = self.Parent.GetParent<MapNode>();
      if (mapNode.nodeType == MapNodeType.CITY)
      {
        return MapConstant.NoStepLimit;
      }
      return self.stepLimit;
    }

    private static bool InnerRealMove(this MoveComponent self, MapNode targetMapNode)
    {
      Entity parent = self.Parent;
      if (parent is User user)
      {
        targetMapNode.PutUserInMapNode(user, false);
        user.SendMessage(new ServerMoveOutMsg
        {
          targetMap = targetMapNode.mapName,
          targetPoint = targetMapNode.pointName,
          mapDaoInfo = targetMapNode.GetMapDaoInfo()
        });
      }
      else if (parent is MonsterInfo monsterInfo)
      {
        targetMapNode.PutMonInMapNode(monsterInfo, true);
      }
      else if (parent is NpcInfo npcInfo)
      {
        targetMapNode.PutNpcInMapNode(npcInfo, true);
      }
      else
      {
        ETLog.Error($"InnerRealMove: Invalid parent type, parent: {parent.GetType()}");
        return false;
      }
      self.nowMap = targetMapNode.mapName;
      self.nowPoint = targetMapNode.pointName;
      return true;
    }
    // 用在自动寻路的时候，自动寻找到目标地图的路径
    public static List<string> buildToMapPath(string nowMap, string nowPoint, string targetMap, string targetPoint)
    {
      if (!nowMap.Equals(targetMap))
      {
        DijkstraInfo mapDijkstraInfo = GlobalInfoCache.Instance.dijkstraInfoForCity[nowMap];
        List<string> mapPath = mapDijkstraInfo.getPath(targetMap, nowMap);
        string nextMap = mapPath[1];
        targetPoint = getTargetMapPoint(nextMap, nowMap);
      }
      DijkstraInfo dijkstraInfo = GlobalInfoCache.Instance.dijkstraInfoMap[nowMap + "_" + nowPoint];
      List<string> path = dijkstraInfo.getPath(targetPoint, nowPoint);
      return path;
    }

    // 获取到目标地图的路径
    public static string getTargetMapPoint(string targetMap, string nowMap)
    {
      string targetPoint = null;
      foreach (string nowPoint in GlobalInfoCache.Instance.mapPoints[nowMap])
      {
        if (nowPoint.Contains(targetMap))
        {
          targetPoint = nowPoint;
        }
        string specialName = MapConstant.specialMapPoint[targetMap];
        if (specialName != null && nowPoint.Contains(specialName))
        {
          targetPoint = nowPoint;
        }
      }
      if (targetPoint == null)
      {
        ETLog.Error($"无法找到目标点, targetMap: {targetMap}, nowMap: {nowMap}");
        return null;
      }
      return targetPoint;
    }
  }
}