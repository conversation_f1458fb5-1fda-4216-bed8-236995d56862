using System.Collections.Concurrent;
using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(MapAttackManage))]
  [FriendOf(typeof(MapAttackManage))]
  public static partial class MapAttackManageSys
  {
    [EntitySystem]
    private static void Awake(this MapAttackManage self)
    {
      self.MapAttacks = new ConcurrentDictionary<string, EntityRef<AttackInCache>>();
    }

    public static LogicRet CheckUserAttackUser(this MapAttackManage self, User src, User target)
    {
      AttackComponent srcAttack = src.GetComponent<AttackComponent>();
      AttackComponent targetAttack = target.GetComponent<AttackComponent>();
      InFightComponent targetInFightComp = target.GetComponent<AttackComponent>().GetComponent<InFightComponent>();
      // 大逃杀开启击杀后才能攻击
      if (src.activityName == ActNameEnum.Da_TaoSha)
      {
        DaTaoShaActComp daTaoShaActComp = GlobalInfoCache.Instance.daTaoShaActComp;
        if (daTaoShaActComp.State != 2)
        {
          return LogicRet.Failed("现阶段还不能攻击其它玩家哦！");
        }
      }
      else
      {
        // 对方正在攻击镖车，不做任何限制
        if (targetInFightComp != null && targetInFightComp.attackTarget.monBaseType == MonBaseType.BiaoChe)
        {
          return LogicRet.Success;
        }
        if (srcAttack.level <= 10)
        {
          return LogicRet.Failed("毛都还没长齐，不要打打杀杀的");
        }
        else if (targetAttack.level <= 10)
        {
          return LogicRet.Failed("对方还没长大，不要欺负小朋友了");
        }
        else if (targetAttack.level < srcAttack.level - 10)
        {
          return LogicRet.Failed("对方等级太低，不要欺负小朋友了");
        }
      }
      if (targetInFightComp.fightList.Count >= targetAttack.maxFightNum)
      {
        return LogicRet.Failed("对方战斗人数过多，请稍后再试！");
      }
      if (src.GetParent<MapNode>().nodeType == MapNodeType.CITY)
      {
        return LogicRet.Failed("城市中禁止攻击玩家！");
      }
      return LogicRet.Success;
    }

    public static LogicRet CheckUserAttackMonster(this MapAttackManage self, User src, MonsterInfo target)
    {
      AttackComponent srcAttack = src.GetComponent<AttackComponent>();
      SkillComponent srcSkill = srcAttack.GetComponent<SkillComponent>();
      if (target.monDescType == MonDescType.HunPo)
      {
        Skill skill = srcSkill.GetSkill(SkillIdEnum.HunPo_CaiJiShu);
        if (skill == null)
        {
          return LogicRet.Failed("您还没有学会魂魄采集技能！");
        }
        if (target.monBaseType == MonBaseType.SiLing_HunPo && skill.level < 10)
        {
          return LogicRet.Failed("您的魂魄采集技能等级不足10级！");
        }
        else if (target.monBaseType == MonBaseType.KuiGui_HunPo && skill.level < 20)
        {
          return LogicRet.Failed("您的魂魄采集技能等级不足20级！");
        }
        else if (target.monBaseType == MonBaseType.MoWang_HunPo && skill.level < 30)
        {
          return LogicRet.Failed("您的魂魄采集技能等级不足30级！");
        }
        else if (target.monBaseType == MonBaseType.NiGuang_HunPo && skill.level < 40)
        {
          return LogicRet.Failed("您的魂魄采集技能等级不足40级！");
        }
        else if (target.monBaseType == MonBaseType.TaiYangShen_HunPo && skill.level < 50)
        {
          return LogicRet.Failed("您的魂魄采集技能等级不足50级！");
        }
      }
      if (target.monBaseType == MonBaseType.BiaoChe)
      {
        if (src.GetParent<MapNode>().nodeType == MapNodeType.CITY)
        {
          return LogicRet.Failed("不能在城市攻击镖车");
        }
        if (target.ownUser != 0 && target.ownUser == src.Id)
        {
          return LogicRet.Failed("不能攻击自己的镖车");
        }
        ChatProSystem.SendMessageToUser(target.ownUser, new ServerShowToastMsg
        {
          message = src.nickname + "开始攻击您的镖车！"
        }, new ServerSendChatMsg
        {
          content = src.nickname + "开始攻击您的镖车！",
        });
      }
      else if (target.ownUser != 0 && target.ownUser != src.Id)
      {
        // if (!TeamProSystem.IsTargetTeamMember(src.GetComponent<TeamComponent>(), target.ownUser))
        // {
        //   throw new MyWarn("您不是怪物拥有者，不能攻击怪物！");
        // }
      }
      if (target.attackLevel != null && target.attackLevel.Length > 1)
      {
        long level = srcAttack.level;
        long minLevel = target.attackLevel[0];
        long maxLevel = target.attackLevel[1];
        if (level < minLevel)
        {
          return LogicRet.Failed(minLevel + "级以上才能攻击该怪物");
        }
        if (level > maxLevel)
        {
          return LogicRet.Failed(maxLevel + "级以下才能攻击该怪物");
        }
      }
      return LogicRet.Success;
    }

    public static LogicRet CheckMonsterAttackUser(this MapAttackManage self, MonsterInfo src, User target)
    {
      return LogicRet.Success;
    }

    public static LogicRet CheckMonsterAttackMonster(this MapAttackManage self, MonsterInfo src, MonsterInfo target)
    {
      if (target.monBaseType == MonBaseType.BiaoChe)
      {
        ChatProSystem.SendMessageToUser(target.ownUser, new ServerShowToastMsg
        {
          message = src.monName + "开始攻击您的镖车！"
        }, new ServerSendChatMsg
        {
          content = src.monName + "开始攻击您的镖车！",
        });
      }
      return LogicRet.Success;
    }

    public static async ETTask<LogicRet> StartAttackBase(this MapAttackManage self, AttackComponent src, AttackComponent target)
    {
      LogicRet ret = LogicRet.Success;
      if (GlobalInfoCache.Instance.allAttackInCache.Count > GlobalInfoCache.Instance.Config.attackConf.attackCacheNum)
      {
        return LogicRet.Failed("战斗人数过多，请稍后再试！");
      }
      if (src.LiveState != LiveStateEnum.ALIVE)
      {
        return LogicRet.Failed("当前无法进行该操作");
      }
      if (target.LiveState == LiveStateEnum.DEAD)
      {
        return LogicRet.Failed("目标已死亡！");
      }
      MapNode srcMapNode = src.Parent.GetParent<MapNode>();
      MapNode targetMapNode = target.Parent.GetParent<MapNode>();
      if (srcMapNode.Id != targetMapNode.Id)
      {
        return LogicRet.Failed("必须在同一格地图中进行战斗！");
      }
      if (src.fightInfo.liveType == LiveType.ROLE)
      {
        if (target.fightInfo.liveType == LiveType.ROLE)
        {
          ret = self.CheckUserAttackUser(src.GetParent<User>(), target.GetParent<User>());
        }
        else if (target.fightInfo.liveType == LiveType.MONSTER)
        {
          ret = self.CheckUserAttackMonster(src.GetParent<User>(), target.GetParent<MonsterInfo>());
        }
      }
      else if (src.fightInfo.liveType == LiveType.MONSTER)
      {
        if (target.fightInfo.liveType == LiveType.ROLE)
        {
          ret = self.CheckMonsterAttackUser(src.GetParent<MonsterInfo>(), target.GetParent<User>());
        }
        else if (target.fightInfo.liveType == LiveType.MONSTER)
        {
          ret = self.CheckMonsterAttackMonster(src.GetParent<MonsterInfo>(), target.GetParent<MonsterInfo>());
        }
      }
      if (!ret.IsSuccess)
      {
        return ret;
      }
      ServerStartAttackMsg serverStartAttackMsg = new();
      if (src.LiveState == LiveStateEnum.ALIVE && target.LiveState == LiveStateEnum.ALIVE)
      {
        long nextAttackId = IdGenerater.Instance.GenerateId();
        List<StartSceneConfig> attackScenes = GlobalInfoCache.Instance.attackScenes;
        StartSceneConfig attackScene = RandomGenerator.RandomArray(attackScenes);
        InnerStartAttackResp startAttackResp = await self.Root().GetComponent<MessageSender>().Call(attackScene.ActorId, new InnerStartAttackReq
        {
          attackId = nextAttackId,
          srcFightInfo = src.fightInfo,
          targetFightInfo = target.fightInfo
        }) as InnerStartAttackResp;
        if (startAttackResp.Error != 0)
        {
          return LogicRet.Failed(startAttackResp.showMessage);
        }
        else if (GlobalInfoCache.Instance.GetAttackInCache(nextAttackId) == null)
        {
          return LogicRet.Failed("战斗创建失败");
        }
        serverStartAttackMsg.attackId = nextAttackId;
      }
      else if (src.LiveState == LiveStateEnum.ALIVE && target.LiveState == LiveStateEnum.FIGHTING)
      {
        InFightComponent targetInFightComp = target.GetComponent<InFightComponent>();
        AttackInCache attackInCache = targetInFightComp?.attackInCache;
        if (attackInCache == null)
        {
          ETLog.Warning($"发起战斗失败: {target.fightInfo.fightName}-{target.fightInfo.fightId}");
          return LogicRet.Failed("发起战斗失败");
        }
        InnerJoinAttackResp joinAttackResp = await self.Root().GetComponent<MessageSender>().Call(attackInCache.GetActorId(), new InnerJoinAttackReq
        {
          attackId = attackInCache.Id,
          srcFightInfo = src.fightInfo,
          targetFightInfo = target.fightInfo
        }) as InnerJoinAttackResp;
        if (joinAttackResp.Error != 0)
        {
          return LogicRet.Failed(joinAttackResp.showMessage);
        }
        serverStartAttackMsg.attackId = attackInCache.Id;
      }
      serverStartAttackMsg.src = src.GetAttackDaoInfo();
      serverStartAttackMsg.target = target.GetAttackDaoInfo();
      List<long> userIds = new();
      if (src.fightInfo.liveType == LiveType.ROLE)
      {
        userIds.Add(src.GetParent<User>().Id);
      }
      if (target.fightInfo.liveType == LiveType.ROLE)
      {
        userIds.Add(target.GetParent<User>().Id);
      }
      if (userIds.Count > 0)
      {
        ChatProSystem.SendMessageToUsers(userIds, serverStartAttackMsg);
      }
      srcMapNode.SendMessageToMapUser(new ServerSendChatMsg
      {
        content = src.fightInfo.fightName + "向" + target.fightInfo.fightName + "发起攻击",
        chatType = ChatType.Local_Chat
      });
      return LogicRet.Success;
    }
  }
}