using System.Collections.Generic;

namespace MaoYouJi
{
  public static partial class MapUpdateSystem
  {
    // 发送移除信息
    public static void SendRemoveInfo(MapNode mapNode, params long[] ids)
    {
      ServerUpdateMapNodeMsg updateNodeInfoOut = new()
      {
        mapName = mapNode.mapName,
        pointName = mapNode.pointName,
        removedIds = new(ids)
      };
      mapNode.SendMessageToMapUser(updateNodeInfoOut);
    }
    // 发送地图节点消息
    public static void SendUpdateState(MapNode mapNode)
    {
      ServerUpdateMapNodeMsg updateNodeInfoOut = new()
      {
        mapName = mapNode.mapName,
        pointName = mapNode.pointName,
        updatedStates = new(mapNode.nodeStates)
      };
      mapNode.SendMessageToMapUser(updateNodeInfoOut);
    }

    // 发送更新信息
    public static void SendUpdateInfo(MapNode mapNode, UserInMap userInMap = null, SimpleNpc simpleNpc = null, SimpleMon simpleMon = null)
    {
      ServerUpdateMapNodeMsg updateNodeInfoOut = new()
      {
        mapName = mapNode.mapName,
        pointName = mapNode.pointName,
      };
      bool hasUpdate = false;
      if (userInMap != null)
      {
        updateNodeInfoOut.updatedUsers = new() { userInMap };
        hasUpdate = true;
      }
      if (simpleNpc != null)
      {
        updateNodeInfoOut.updatedNpcs = new() { simpleNpc };
        hasUpdate = true;
      }
      if (simpleMon != null)
      {
        updateNodeInfoOut.updatedMons = new() { simpleMon };
        hasUpdate = true;
      }
      if (hasUpdate)
      {
        mapNode.SendMessageToMapUser(updateNodeInfoOut);
      }
    }
  }
}