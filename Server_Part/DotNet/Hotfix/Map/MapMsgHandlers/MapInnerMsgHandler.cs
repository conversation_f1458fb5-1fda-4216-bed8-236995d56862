using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class OutMoveUserHandler : MessageHandler<MapNode, InnerMoveUserReq, InnerMoveUserResp>
  {
    protected override async ETTask Run(MapNode nowMap, InnerMoveUserReq request, InnerMoveUserResp response)
    {
      User targetUser = nowMap.GetChild<User>(request.TargetUserId);
      if (targetUser == null)
      {
        response.SetError("不在当前地图");
        return;
      }
      if (request.OutNowMapName != null && request.OutNowPointName != null && (request.OutNowMapName != nowMap.mapName || request.OutNowPointName != nowMap.pointName))
      {
        response.SetError("不在当前地图");
        return;
      }
      AttackComponent attackComponent = targetUser.GetComponent<AttackComponent>();
      if (attackComponent.LiveState == LiveStateEnum.FIGHTING)
      {
        if (!request.IsForceMove)
        {
          response.SetError("目标用户正在战斗");
          return;
        }
        else
        {
          // 强制移动，需要先退出战斗，待实现
          // attackComponent.ExitFighting();
        }
      }
      // 移动到目标地图
      MoveComponent moveComponent = targetUser.GetComponent<MoveComponent>();
      LogicRet logicRet = moveComponent.MoveTo(request.TargetMapName, request.TargetPointName);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }
      await ETTask.CompletedTask;
    }
  }
}