using System;
using System.Collections.Generic;
using System.Net;

namespace MaoYouJi
{
  [Event(SceneType.Main)]
  public class EntryEvent2_InitServer : AEvent<Scene, EntryEvent2>
  {
    protected override async ETTask Run(Scene root, EntryEvent2 args)
    {
      switch (Options.Instance.AppType)
      {
        case AppType.Server:
          {
            root.AddComponent<DBManagerComponent>();
            root.AddComponent<SkillManageComponent>();
            int process = root.Fiber.Process;
            StartProcessConfig startProcessConfig = StartProcessConfigCategory.Instance.Get(process);
            if (startProcessConfig.Port != 0)
            {
              await FiberManager.Instance.Create(SchedulerType.ThreadPool, ConstFiberId.NetInner, 0, SceneType.NetInner, "NetInner");
            }

            // 根据配置创建纤程
            var processScenes = StartSceneConfigCategory.Instance.GetByProcess(process);

            int zone = 0;
            if (processScenes.Count > 0)
            {
              foreach (StartSceneConfig startConfig in processScenes)
              {
                if (startConfig.Type == SceneType.Map)
                {
                  zone = startConfig.Zone;
                  break;
                }
              }
            }

            World.Instance.AddSingleton<QuartzScheduler>();
            World.Instance.AddSingleton<GlobalInfoCache>();
            World.Instance.AddSingleton<GlobalActorInfo>();
            List<StartSceneConfig> mapScenes = new(), attackScenes = new();
            foreach (StartSceneConfig startConfig in processScenes)
            {
              if (startConfig.Type == SceneType.Location)
              {
                ETLog.Info($"LocationConfig: {startConfig.Name}");
                StartSceneConfigCategory.Instance.LocationConfig = startConfig;
              }
              else if (startConfig.Type == SceneType.Map)
              {
                mapScenes.Add(startConfig);
              }
              else if (startConfig.Type == SceneType.Attack)
              {
                attackScenes.Add(startConfig);
              }
            }
            GlobalInfoCache.Instance.mapScenes = mapScenes;
            GlobalInfoCache.Instance.attackScenes = attackScenes;
            // 1区不进行初始化
            if (zone != 1)
            {
              await FiberManager.Instance.Create(SchedulerType.ThreadPool, ConstFiberId.Global, zone, SceneType.Global, "Global");
              await InitWorldBaseData(root);
            }

            foreach (StartSceneConfig startConfig in processScenes)
            {
              await FiberManager.Instance.Create(SchedulerType.ThreadPool, startConfig.Id, startConfig.Zone, startConfig.Type, startConfig.Name);
            }
            break;
          }
        case AppType.Watcher:
          {
            root.AddComponent<WatcherComponent>();
            break;
          }
        case AppType.GameTool:
          {
            break;
          }
      }

      if (Options.Instance.Console == 1)
      {
        root.AddComponent<ConsoleComponent>();
      }
    }

    private async ETTask InitWorldBaseData(Scene root)
    {
      DBComponent dbComponent = root.GetComponent<DBManagerComponent>().GetZoneDB(1);
      SkillManageComponent skillManageComponent = root.GetComponent<SkillManageComponent>();

      var mallShopList = await dbComponent.QueryClass<MallShopInfo>(x => x.disable != true);
      foreach (MallShopInfo mallShop in mallShopList)
      {
        GlobalInfoCache.Instance.allMallShopCache.TryAdd(mallShop.id.ToString(), mallShop);
      }
      ETLog.Info($"mallShopList: {mallShopList.Count}");

      var comShopList = await dbComponent.QueryClass<ComShopInfo>(x => x.disable != true);
      foreach (ComShopInfo comShop in comShopList)
      {
        GlobalInfoCache.Instance.allComShopCache.TryAdd(comShop.id.ToString(), comShop);
      }
      ETLog.Info($"comShopList: {comShopList.Count}");

      var foodList = await dbComponent.QueryClass<Food>(f => f.thingName != ThingNameEnum.None);
      foreach (Food food in foodList)
      {
        GlobalInfoCache.Instance.AddThing(food.thingName, food);
      }
      ETLog.Info($"foodList: {foodList.Count}");

      var taskThingList = await dbComponent.QueryClass<TaskThing>(t => t.thingName != ThingNameEnum.None);
      foreach (TaskThing taskThing in taskThingList)
      {
        GlobalInfoCache.Instance.AddThing(taskThing.thingName, taskThing);
      }
      ETLog.Info($"taskThingList: {taskThingList.Count}");

      var treasureList = await dbComponent.QueryClass<Treasure>(t => t.thingName != ThingNameEnum.None);
      foreach (Treasure treasure in treasureList)
      {
        GlobalInfoCache.Instance.AddThing(treasure.thingName, treasure);
      }
      ETLog.Info($"treasureList: {treasureList.Count}");

      var materialList = await dbComponent.QueryClass<Material>(m => m.thingName != ThingNameEnum.None);
      foreach (Material material in materialList)
      {
        GlobalInfoCache.Instance.AddThing(material.thingName, material);
      }
      ETLog.Info($"materialList: {materialList.Count}");

      var equipList = await dbComponent.QueryClass<Equipment>(e => e.thingName != ThingNameEnum.None);
      foreach (Equipment equip in equipList)
      {
        GlobalInfoCache.Instance.AddThing(equip.thingName, equip);
      }
      ETLog.Info($"equipList: {equipList.Count}");

      var mineList = await dbComponent.QueryClass<Mine>(m => m.thingName != ThingNameEnum.None);
      foreach (Mine mine in mineList)
      {
        GlobalInfoCache.Instance.AddThing(mine.thingName, mine);
      }
      ETLog.Info($"mineList: {mineList.Count}");

      var monsterList = await dbComponent.QueryClass<BaseMonster>(m => m.monBaseType != MonBaseType.None);
      foreach (BaseMonster monster in monsterList)
      {
        GlobalInfoCache.Instance.AddBaseMonster(monster.monBaseType, monster);
      }
      ETLog.Info($"monsterList: {monsterList.Count}");

      var baseAttackList = await dbComponent.QueryClass<BaseAttack>(a => a.baseJob != BaseJob.None);
      foreach (BaseAttack attack in baseAttackList)
      {
        GlobalInfoCache.Instance.AddBaseAttack(attack.baseJob, attack);
      }
      ETLog.Info($"baseAttackList: {baseAttackList.Count}");

      var baseSkillList = await dbComponent.QueryClass<BaseSkill>(s => s.skillId != SkillIdEnum.None);
      foreach (BaseSkill baseSkill in baseSkillList)
      {
        GlobalInfoCache.Instance.baseSkillCache.TryAdd(baseSkill.skillId, baseSkill);
        List<Skill> skills = skillManageComponent.ParseBaseSkill(baseSkill);
        GlobalInfoCache.Instance.AddSkill(baseSkill.skillId, skills);
      }
      ETLog.Info($"baseSkillList: {baseSkillList.Count}");

      var baseTaskList = await dbComponent.QueryClass<BaseTask>(t => t.idEnum != TaskIdEnum.None);
      foreach (BaseTask baseTask in baseTaskList)
      {
        GlobalInfoCache.Instance.AddBaseTask(baseTask.idEnum, baseTask);
      }
      ETLog.Info($"baseTaskList: {baseTaskList.Count}");
    }
  }
}