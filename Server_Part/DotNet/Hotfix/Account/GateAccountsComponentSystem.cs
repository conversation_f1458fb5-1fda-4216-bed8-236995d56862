using MongoDB.Driver;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(GateAccountsComponent))]
  [FriendOf(typeof(GateAccountsComponent))]
  [FriendOf(typeof(Account))]
  public static partial class GateAccountsComponentSystem
  {
    [EntitySystem]
    private static void Awake(this GateAccountsComponent self)
    {
    }

    public static void AddAccount(this GateAccountsComponent self, long accountId, Account account)
    {
      self.AddChild(account);
      GlobalInfoCache.Instance.AddOnlineAccount(accountId, account);
    }

    public static void RemoveAccount(this GateAccountsComponent self, long accountId)
    {
      self.RemoveChild(accountId);
      GlobalInfoCache.Instance.RemoveOnlineAccount(accountId);
    }

    public async static ETTask<Account> GetAccount(this GateAccountsComponent self, long netAccountId)
    {
      Scene root = self.Root();
      // 根据网关账户ID获取本地账号
      DBComponent dBComponent = root.GetComponent<DBManagerComponent>().GetMyZoneDB();
      Account account = GlobalInfoCache.Instance.GetOnlineAccount(netAccountId);
      if (account == null)
      {
        account = await dBComponent.QueryClass(new FilterDefinitionBuilder<Account>().Eq(a => a.netAccountId, netAccountId));
        if (account != null)
        {
          self.AddAccount(netAccountId, account);
        }
      }
      return account;
    }
  }
}