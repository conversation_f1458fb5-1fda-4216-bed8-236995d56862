using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Driver;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(Account))]
  [FriendOf(typeof(Account))]
  public static partial class AccountSystem
  {
    [EntitySystem]
    private static void Awake(this Account self)
    {
    }

    // 如果AttackComponent嵌入在User文档中
    public static async ETTask<List<AccountRoleInfo>> GetRoleInfoListEmbedded(this Account self)
    {
      if (self.roleList == null || self.roleList.Count == 0)
      {
        return new List<AccountRoleInfo>();
      }

      // 构建聚合管道，直接映射为AccountRoleInfo结构
      var pipeline = new BsonDocument[]
      {
        // 匹配指定ID的用户
        new BsonDocument("$match", new BsonDocument("_id", new BsonDocument("$in", new BsonArray(self.roleList)))),
        // 先用$addFields提取attackComp
        new BsonDocument("$addFields", new BsonDocument
        {
          { "attackComp", new BsonDocument("$arrayElemAt", new BsonArray
            {
              new BsonDocument("$filter", new BsonDocument
                {
                  { "input", "$C" },
                  { "as", "comp" },
                  { "cond", new BsonDocument("$eq", new BsonArray { "$$comp._t", "AttackComponent" }) }
                }),
              0
            })
          }
        }),
        // 再用$project映射为我们需要的结构
        new BsonDocument("$project", new BsonDocument
        {
          { "_id", 0 },
          { "accountId", self.Id },
          { "userId", "$_id" },
          { "name", "$nickname" },
          { "skinId", new BsonDocument("$ifNull", new BsonArray { "$attackComp.NowSkin", SkinIdEnum.default_0 }) },
          { "level", new BsonDocument("$ifNull", new BsonArray { "$attackComp.level", 1 }) },
          { "job", new BsonDocument("$ifNull", new BsonArray { "$attackComp.job", BaseJob.None }) }
        })
      };
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      // 执行聚合查询
      var collection = dbComponent.GetCollection<BsonDocument>("MaoYouJi.User");
      var cursor = await collection.AggregateAsync<AccountRoleInfo>(pipeline);

      return await cursor.ToListAsync();
    }
  }
}
