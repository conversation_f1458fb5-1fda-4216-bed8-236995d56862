using System;

namespace MaoYouJi
{
  [FriendOf(typeof(MonsterInfo))]
  [FriendOf(typeof(AttackComponent))]
  [FriendOf(typeof(MapNode))]
  public static partial class MonsterInfoSystem
  {

    [EntitySystem]
    public static void Awake(this MonsterInfo self, MonBaseType monBaseType, User user)
    {
      self.monBaseType = monBaseType;
      BaseMonster baseMonster = GlobalInfoCache.Instance.GetBaseMonster(monBaseType);
      if (baseMonster == null)
      {
        ETLog.Error($"MonsterInfoSystem.Awake(this MonsterInfo self, MonBaseType monBaseType) 找不到BaseMonster {monBaseType}");
        return;
      }
      self.Awake(baseMonster, user);
    }

    [EntitySystem]
    public static void Awake(this MonsterInfo self, BaseMonster baseMonster, User user)
    {
      self.monBaseType = baseMonster.monBaseType;
      self.monDescType = baseMonster.descType;
      self.monName = baseMonster.name;
      self.selfStatus = new();
      baseMonster.selfStatus?.ForEach(status =>
        {
          self.selfStatus.Add(status.Clone() as AttachStatus);
        });
      if (baseMonster.tags != null)
      {
        self.tags = [.. baseMonster.tags];
      }
      if (user != null)
      {
        self.ownUser = user.Id;
        self.ownName = user.nickname;
        if (self.monDescType == MonDescType.BiaoChe)
        {
          self.monName = user.nickname + "的镖车";
        }
      }
      else
      {
        self.attackLevel = baseMonster.attackLevel;
      }
      self.genTime = TimeInfo.Instance.ServerNow();
      self.updateTime = TimeInfo.Instance.ServerNow();
      self.skillInfos = new();
      baseMonster.skillInfos?.ForEach(skillInfo =>
        {
          self.skillInfos.Add(skillInfo.Clone() as MonSkillInfo);
        });
      MapNode mapNode = self.GetParent<MapNode>();
      self.AddComponent<MoveComponent, string, string, int>(mapNode.mapName, mapNode.pointName, 1);
      if (baseMonster.moveEvent != null)
      {
        // self.AddComponent<AutoMoveComponent>();
      }
      FightInfo fightInfo = new()
      {
        fightName = self.monName,
        liveType = LiveType.MONSTER,
        monBaseType = self.monBaseType,
        fightId = self.Id
      };
      AttackComponent attackComponent = self.AddComponent<AttackComponent, FightInfo>(fightInfo);
      attackComponent.LiveState = LiveStateEnum.ALIVE;
      attackComponent.job = baseMonster.job;
      attackComponent.level = RandomGenerator.RandomArray(baseMonster.level);
      attackComponent.exp = GetRandVal(baseMonster.exp, attackComponent.level, baseMonster.level);
      attackComponent.maxBlood = GetRandVal(baseMonster.maxBlood, attackComponent.level, baseMonster.level);
      attackComponent.blood = attackComponent.maxBlood;
      attackComponent.maxBlue = GetRandVal(baseMonster.maxBlue, attackComponent.level, baseMonster.level);
      attackComponent.blue = attackComponent.maxBlue;
      attackComponent.maxFightNum = baseMonster.maxFightNum;
      long attack1 = GetRandVal(baseMonster.attack, attackComponent.level, baseMonster.level);
      long attack2 = GetRandVal(baseMonster.attack, attackComponent.level, baseMonster.level);
      attackComponent.minAttack = Math.Min(attack1, attack2);
      attackComponent.maxAttack = Math.Max(attack1, attack2);
      attackComponent.realAttack = GetRandVal(baseMonster.realAttack, attackComponent.level, baseMonster.level);
      attackComponent.hitRate = GetRandVal(baseMonster.hitRate, attackComponent.level, baseMonster.level);
      attackComponent.attackRate = GetRandVal(baseMonster.attackRate, attackComponent.level, baseMonster.level);
      attackComponent.crit = GetRandVal(baseMonster.crit, attackComponent.level, baseMonster.level);
      attackComponent.critDmg = GetRandVal(baseMonster.critDmg, attackComponent.level, baseMonster.level);
      attackComponent.defense = GetRandVal(baseMonster.defense, attackComponent.level, baseMonster.level);
      attackComponent.magicDefense = GetRandVal(baseMonster.magicDefense, attackComponent.level, baseMonster.level);
      attackComponent.miss = GetRandVal(baseMonster.miss, attackComponent.level, baseMonster.level);
      attackComponent.strength = GetRandVal(baseMonster.strength, attackComponent.level, baseMonster.level);
      attackComponent.power = GetRandVal(baseMonster.power, attackComponent.level, baseMonster.level);
      attackComponent.quick = GetRandVal(baseMonster.quick, attackComponent.level, baseMonster.level);
      attackComponent.iq = GetRandVal(baseMonster.iq, attackComponent.level, baseMonster.level);
      attackComponent.mind = GetRandVal(baseMonster.mind, attackComponent.level, baseMonster.level);
      attackComponent.damageAdd = GetRandVal(baseMonster.damageAdd, attackComponent.level, baseMonster.level);
      attackComponent.damageReduce = GetRandVal(baseMonster.damageReduce, attackComponent.level, baseMonster.level);
      if (baseMonster.skillInfos != null)
      {
        self.skillInfos = baseMonster.skillInfos;
      }
      if (self.monDescType == MonDescType.NpcMon)
      {
        self.monName = RandomGenerator.RandomArray(MonsterInfo.cityNpcPres) + " " + RandomGenerator.RandomArray(MonsterInfo.cityNpcNames)
            + RandomGenerator.RandomArray(MonsterInfo.cityNpcAfters);
      }
      mapNode.PutMonInMapNode(self);
      GlobalInfoCache.Instance.AddMonsterInfo(self.Id, self);
    }

    [EntitySystem]
    public static void Destroy(this MonsterInfo self)
    {
      MapNode mapNode = self.GetParent<MapNode>();
      mapNode.RemoveMonInMapNode(self);
      GlobalInfoCache.Instance.RemoveMonsterInfo(self.Id);
    }

    public static long GetRandVal(long[] range, long level, long[] levelRange)
    {
      long min = (level - levelRange[0]) * (range[1] - range[0]) / (levelRange[1] - levelRange[0] + 1) + range[0];
      long max = (level + 1 - levelRange[0]) * (range[1] - range[0]) / (levelRange[1] - levelRange[0] + 1) + range[0];
      return RandomGenerator.RandomNumber((int)min, (int)max);
    }
  }
}