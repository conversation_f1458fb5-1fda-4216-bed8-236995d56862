namespace MaoYouJi
{
  [Invoke((long)NpcNameEnum.Mu_JinJin)]
  public class MuJinJinHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      DaTaoShaActComp daTaoShaActComp = GlobalInfoCache.Instance.daTaoShaActComp;
      if (daTaoShaActComp.State != 0)
        resp.talkList = "can_enter_daTaoSha";
      else
        resp.talkList = "default";
      await ETTask.CompletedTask;
    }
  }
}