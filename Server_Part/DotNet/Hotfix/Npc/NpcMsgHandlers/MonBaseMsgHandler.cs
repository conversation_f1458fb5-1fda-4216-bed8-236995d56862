namespace MaoYouJi
{

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ShowMonHandler : MessageLocationHandler<MapNode, ShowMonReq, ShowMonResp>
  {
    protected override async ETTask Run(MapNode nowMap, ShowMonReq request, ShowMonResp response)
    {
      MonsterInfo mon = nowMap.GetChild<MonsterInfo>(request.MonId);
      if (mon == null)
      {
        response.SetError("怪物不存在");
        return;
      }
      response.monDaoInfo = mon.GetMonDaoInfo();
      await ETTask.CompletedTask;
    }
  }
}