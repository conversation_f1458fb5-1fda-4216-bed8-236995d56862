namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  public class GetComShopListHandler : MessageLocationHandler<MapNode, GetComShopListReq, GetComShopListResp>
  {
    protected override async ETTask Run(MapNode nowMap, GetComShopListReq req, GetComShopListResp response)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }
      if ((req.shopName == null || req.comShopType == ComShopType.None) && req.relationType == RelationTypeEnum.None)
      {
        response.SetError("商店名称不能为空");
        return;
      }
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      response.ComShopList = ComShopProcSys.getComShopInfoList(req.shopName, req.comShopType, req.relationType);
      if (req.relationType != RelationTypeEnum.None)
      {
        RelationInfo relationInfo = relationComponent.GetRelationInfo(req.relationType);
        response.RelationLevel = relationInfo.level;
        response.RelationExp = relationInfo.relationExp;
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientBuyComShopMsgHandler : MessageLocationHandler<MapNode, ClientBuyComShopMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientBuyComShopMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      if (!GlobalInfoCache.Instance.allComShopCache.TryGetValue(msg.comShopId, out ComShopInfo comShopInfo))
      {
        user.SendToast("商品不存在");
        return;
      }
      if (user.activityName == ActNameEnum.Da_TaoSha)
      {
        user.SendToast("大逃杀活动期间，无法购买道具店商品");
        return;
      }
      if (msg.num <= 0)
      {
        user.SendToast("购买数量不能小于等于0");
        return;
      }
      if (comShopInfo.relationType != RelationTypeEnum.None)
      {
        RelationComponent relationComponent = user.GetComponent<RelationComponent>();
        RelationInfo relationInfo = relationComponent.GetRelationInfo(comShopInfo.relationType);
        if (relationInfo.level < comShopInfo.needMinRelationLevel)
        {
          user.SendToast("关系等级不足");
          return;
        }
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (comShopInfo.minLevel > attackComponent.level)
      {
        user.SendToast("等级不足");
        return;
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      long cost = comShopInfo.price * msg.num;
      if (bagComponent.coin < cost)
      {
        user.SendToast("金币不足");
        return;
      }
      if (!bagComponent.HasEnoughCapacity(1))
      {
        user.SendToast("背包已满");
        return;
      }
      ThingGiveInfo thingGiveInfo = new ThingGiveInfo
      {
        thingName = comShopInfo.thingName,
        ownType = comShopInfo.ownType,
        num = msg.num * comShopInfo.shopNum
      };
      bagComponent.GiveThing(thingGiveInfo);
      bagComponent.AddAllCoinWithSend(-cost);
      user.SendToast("购买成功");
      await ETTask.CompletedTask;
    }
  }
}