using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ShowMallShopListHandler : MessageLocationHandler<MapNode, ShowMallShopListReq, ShowMallShopListResp>
  {
    protected override async ETTask Run(MapNode nowMap, ShowMallShopListReq message, ShowMallShopListResp response)
    {
      response.MallShopList = GlobalInfoCache.Instance.allMallShopCache.Values.ToList();
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientBuyMallShopMsgHandler : MessageLocationHandler<MapNode, ClientBuyMallShopMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientBuyMallShopMsg message)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(message.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      if (!GlobalInfoCache.Instance.allMallShopCache.TryGetValue(message.mallShopId, out MallShopInfo mallShopInfo))
      {
        user.SendToast("该商品不存在");
        return;
      }
      if (message.num <= 0)
      {
        user.SendToast("购买数量不能小于等于0");
        return;
      }
      if (user.activityName == ActNameEnum.Da_TaoSha)
      {
        user.SendToast("大逃杀活动期间，无法购买商城商品");
        return;
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      long cost = mallShopInfo.price * message.num;
      Thing thing = GlobalInfoCache.Instance.GetBaseThing(mallShopInfo.thingName);
      int needBagCapacity = 1;
      if (thing.stackNum <= 1)
      {
        needBagCapacity = message.num;
      }
      if (!bagComponent.HasEnoughCapacity(needBagCapacity))
      {
        user.SendToast("背包容量不足");
        return;
      }
      ThingGiveInfo thingGiveInfo = new ThingGiveInfo
      {
        thingName = mallShopInfo.thingName,
        ownType = mallShopInfo.ownType,
        num = message.num * mallShopInfo.shopNum
      };
      if (mallShopInfo.useCatEye)
      {
        if (bagComponent.catEye < cost)
        {
          user.SendToast("猫眼不足");
          return;
        }
        bagComponent.GiveThing(thingGiveInfo, ThingFromType.Shop);
        bagComponent.catEye -= cost;
      }
      else
      {
        if (bagComponent.catBean < cost)
        {
          user.SendToast("猫豆不足");
          return;
        }
        bagComponent.GiveThing(thingGiveInfo, ThingFromType.Shop);
        bagComponent.catBean -= cost;
      }
      bagComponent.SendNewCoin();
      user.SendToast("购买成功");
      await ETTask.CompletedTask;
    }
  }
}