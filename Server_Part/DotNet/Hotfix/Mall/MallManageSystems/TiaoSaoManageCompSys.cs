using System.Collections.Generic;
using System.Threading.Tasks;
using MongoDB.Bson;
using MongoDB.Driver;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(TiaoSaoManageComp))]
  public static partial class TiaoSaoManageCompSys
  {
    [EntitySystem]
    public static void Awake(this TiaoSaoManageComp self)
    {
    }

    private static FilterDefinition<TiaoSaoShopInfo> GetTiaoSaoShopInfoFilter(ClientGetTiaoSaoShopMsg msg)
    {
      var builder = Builders<TiaoSaoShopInfo>.Filter;
      var filter = builder.Gt(x => x.price, 0); // 只查未禁用的
      if (msg.thingType != ThingType.None)
        filter &= builder.Eq("thing.thingType", msg.thingType);
      if (msg.thingGrade != ThingGrade.None)
        filter &= builder.Eq("thing.grade", msg.thingGrade);
      if (msg.equipPart != EquipPart.None)
        filter &= builder.Eq("thing.equipPart", msg.equipPart);
      if (msg.weaponType != WeaponType.None)
        filter &= builder.Eq("thing.weaponType", msg.weaponType);
      if (msg.levelRange != null && msg.levelRange.Length == 2)
        filter &= builder.Gte("thing.level", msg.levelRange[0]) & builder.Lte("thing.level", msg.levelRange[1]);
      if (!string.IsNullOrEmpty(msg.thingName))
        filter &= builder.Regex("shopName", new MongoDB.Bson.BsonRegularExpression(msg.thingName, "i"));
      if (msg.isMyShop)
        filter &= builder.Eq("sellUserInfo.userId", msg.UserId);
      return filter;
    }

    public static async ETTask<List<TiaoSaoShopInfo>> GetTiaoSaoShopList(this TiaoSaoManageComp self, ClientGetTiaoSaoShopMsg msg)
    {
      var dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<TiaoSaoShopInfo>();

      var filter = GetTiaoSaoShopInfoFilter(msg);

      var sort = msg.sortType == 0
        ? Builders<TiaoSaoShopInfo>.Sort.Ascending("price")
        : Builders<TiaoSaoShopInfo>.Sort.Descending("price");

      var result = await collection
        .Find(filter)
        .Sort(sort)
        .Skip(msg.page * msg.pageSize)
        .Limit(msg.pageSize)
        .ToListAsync();
      return result;
    }

    public static async ETTask<int> GetTiaoSaoShopInfoListCount(this TiaoSaoManageComp self, ClientGetTiaoSaoShopMsg msg)
    {
      var dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<TiaoSaoShopInfo>();
      var filter = GetTiaoSaoShopInfoFilter(msg);
      return (int)await collection.CountDocumentsAsync(filter);
    }

    public static async ETTask AddTiaoSaoShopInfo(this TiaoSaoManageComp self, TiaoSaoShopInfo tiaoSaoShopInfo)
    {
      var dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<TiaoSaoShopInfo>();
      await collection.InsertOneAsync(tiaoSaoShopInfo);
    }

    public static async ETTask<TiaoSaoShopInfo> GetTiaoSaoShopInfo(this TiaoSaoManageComp self, string tiaoSaoShopId)
    {
      var dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<TiaoSaoShopInfo>();
      return await collection.Find(x => x.id == new ObjectId(tiaoSaoShopId)).FirstOrDefaultAsync();
    }

    public static async ETTask<bool> DeleteTiaoSaoShopInfo(this TiaoSaoManageComp self, string tiaoSaoShopId)
    {
      var dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<TiaoSaoShopInfo>();
      var filter = Builders<TiaoSaoShopInfo>.Filter.Eq(x => x.id, new ObjectId(tiaoSaoShopId));
      var deletedDoc = await collection.FindOneAndDeleteAsync(filter);
      if (deletedDoc == null)
      {
        ETLog.Error($"下架失败，商品不存在: {tiaoSaoShopId}");
        return false;
      }
      if (deletedDoc.sellUserInfo == null)
      {
        ETLog.Error($"下架失败，不存在卖方: {tiaoSaoShopId}");
        return false;
      }
      if (deletedDoc.thing == null)
      {
        ETLog.Error($"下架失败，不存在物品: {tiaoSaoShopId}");
        return false;
      }
      ETLog.Info($"downTiaoSaoShopInfo, {tiaoSaoShopId}, {deletedDoc.sellUserInfo.userId}, {deletedDoc.shopName}, {deletedDoc.thing.thingName}, {deletedDoc.thing.grade}, {deletedDoc.price}, {deletedDoc.thing.num}, {deletedDoc.endTime}");
      ChatManageComp chatManageComp = self.GetParent<Scene>().GetComponent<ChatManageComp>();
      await chatManageComp.SendMail(deletedDoc.sellUserInfo.userId, "跳蚤商品下架通知",
          "您上架的" + deletedDoc.shopName + "已下架", 0, 0, 0, [deletedDoc.thing]);
      return true;
    }

    public static async ETTask<TiaoSaoShopInfo> SubTiaoSaoShopInfoNum(this TiaoSaoManageComp self, string tiaoSaoShopId, int num)
    {
      var dbComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var collection = dbComponent.GetCollection<TiaoSaoShopInfo>();
      var query = Builders<TiaoSaoShopInfo>.Filter.Eq(x => x.id, new ObjectId(tiaoSaoShopId));
      query &= Builders<TiaoSaoShopInfo>.Filter.Gte(x => x.thing.num, num);
      var update = Builders<TiaoSaoShopInfo>.Update.Inc(x => x.thing.num, -num);
      var result = await collection.FindOneAndUpdateAsync(query, update, new FindOneAndUpdateOptions<TiaoSaoShopInfo, TiaoSaoShopInfo> { ReturnDocument = ReturnDocument.After });
      return result;
    }
  }
}