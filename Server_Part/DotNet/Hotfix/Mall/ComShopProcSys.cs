using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  public static class ComShopProcSys
  {
    public static List<ComShopInfo> getComShopInfoList(string mapName, ComShopType comShopType, RelationTypeEnum relationType, bool isAll = false)
    {
      List<ComShopInfo> comShopInfos = GlobalInfoCache.Instance.allComShopCache.Values.ToList();
      if (mapName != null)
      {
        comShopInfos = comShopInfos.Where(comShopInfo => mapName != null && mapName == comShopInfo.mapName).ToList();
      }
      if (comShopType != ComShopType.None)
      {
        comShopInfos = comShopInfos.Where(comShopInfo => comShopType == comShopInfo.comShopType).ToList();
      }
      if (relationType != RelationTypeEnum.None)
      {
        comShopInfos = comShopInfos.Where(comShopInfo => relationType == comShopInfo.relationType).ToList();
      }
      comShopInfos = comShopInfos.Where(comShopInfo => isAll || !comShopInfo.disable).ToList();
      comShopInfos.Sort((a, b) => a.sort.CompareTo(b.sort));
      return comShopInfos;
    }
  }
}
