using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ShowSkillListHandler : MessageLocationHandler<MapNode, ShowSkillListReq, ShowSkillListResp>
  {
    protected override async ETTask Run(MapNode nowMap, ShowSkillListReq request, ShowSkillListResp response)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(request.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        response.SetError(getUserRet.Message);
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      response.skillComponentDaoInfo = skillComponent.ToDaoInfo();
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientChangeQuickSkillHandler : MessageLocationHandler<MapNode, ClientChangeQuickSkillMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientChangeQuickSkillMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, false, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      if (msg.SkillIds.Count != 12)
      {
        user.SendToast("非法的快捷参数");
        return;
      }

      List<Skill> equipSkills = new();
      foreach (SkillIdEnum skillId in msg.SkillIds)
      {
        if (skillId == SkillIdEnum.None)
          continue;
        Skill skill = skillComponent.GetSkill(skillId);
        if (skill == null)
        {
          user.SendToast("快捷失败，您并未拥有快捷栏中的部分技能！");
          return;
        }
        if (!skill.isCanUse())
        {
          user.SendToast("被动技能无法快捷");
          return;
        }
        equipSkills.Add(skill);
      }
      int talentNum = 0, baseNum = 0, jobNum = 0, lifeNum = 0;
      foreach (Skill skill in equipSkills)
      {
        if (skill.skillType == SkillTypeEnum.TALENT_ACTIVE)
          ++talentNum;
        else if (skill.skillType == SkillTypeEnum.BASE_SKILL)
          ++baseNum;
        else if (skill.skillType == SkillTypeEnum.JOB_SKILL)
          ++jobNum;
        else if (skill.skillType == SkillTypeEnum.LIFE_SKILL)
          ++lifeNum;
      }
      if (talentNum > 1)
      {
        user.SendToast("天赋技能最多快捷1个！");
        return;
      }
      if (baseNum > 5)
      {
        user.SendToast("基础技能最多快捷5个！");
        return;
      }
      if (jobNum > 6)
      {
        user.SendToast("职业技能最多快捷6个！");
        return;
      }
      if (lifeNum > 1)
      {
        user.SendToast("生活技能最多快捷2个！");
        return;
      }
      skillComponent.equipSkills = msg.SkillIds.ToArray();
      user.SendToast("保存快捷栏信息成功！");
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Quick_Bar));
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class LearnSkillHandler : MessageLocationHandler<MapNode, LearnSkillReq, LearnSkillResp>
  {
    protected override async ETTask Run(MapNode nowMap, LearnSkillReq request, LearnSkillResp response)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(request.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        response.SetError(getUserRet.Message);
        return;
      }
      GlobalInfoCache.Instance.baseSkillCache.TryGetValue(request.SkillId, out BaseSkill baseSkill);
      if (baseSkill == null)
      {
        response.SetError("技能不存在");
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Skill skill = skillComponent.GetSkill(request.SkillId);
      if (skill == null && request.Level > 1)
      {
        response.SetError("技能前置等级不够！");
        return;
      }
      if (skill != null)
      {
        if (skill.level < request.Level - 1)
        {
          response.SetError("技能前置等级不够！");
          return;
        }
        if (skill.level > request.Level - 1)
        {
          response.SetError("无法重复学习！");
          return;
        }
      }
      if (baseSkill.skillInfos.Count < request.Level)
      {
        response.SetError("技能等级不存在！");
        return;
      }
      SkillInfo skillInfo = baseSkill.skillInfos[request.Level - 1];
      if (!skillInfo.isJustLearn)
      {
        response.SetError("该技能无法直接学习！");
        return;
      }
      if (skillInfo.jobLevel > attackComponent.level)
      {
        response.SetError("等级不足！");
        return;
      }
      if (baseSkill.needBaseJob != BaseJob.None && baseSkill.needBaseJob != attackComponent.job)
      {
        response.SetError("职业不匹配！");
        return;
      }
      if (skillInfo.needGold > bagComponent.coin)
      {
        response.SetError("金币不足！");
        return;
      }
      if (skillInfo.needBaseLevel >= 1 && baseSkill.job != MaoJob.None)
      {
        SkillIdEnum baseSkillId = SkillConstant.GetSkillIdByMaoJob(baseSkill.job);
        Skill jobBaseSkill = skillComponent.GetSkill(baseSkillId);
        if (jobBaseSkill == null || jobBaseSkill.level < skillInfo.needBaseLevel)
        {
          response.SetError("职业技能等级不足！");
          return;
        }
      }
      Skill targetSkill = GlobalInfoCache.Instance.GetSkill(request.SkillId, request.Level);
      skillComponent.AddLearnSkill(targetSkill);
      if (targetSkill.skillType == SkillTypeEnum.LIFE_SKILL)
      {
        targetSkill.isActive = true;
        targetSkill.maxExp = SkillComponentProc.calcLevelExp(1);
      }
      // taskProc.fillTaskRequire(userCacheInfo, TaskRequireType.Learn_Skill_Cond, inParams.skillId.toString(),
      //     inParams.level);
      EventSystem.Instance.Publish(nowMap.Root(), new LearnSkillEvent() { user = user, skill = targetSkill });
      user.SendMessage(ServerUpdateSkillInfoMsg.Create(targetSkill));
      if (skillInfo.needGold > 0)
      {
        bagComponent.AddAllCoinWithSend(-skillInfo.needGold);
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientActiveSkillHandler : MessageLocationHandler<MapNode, ClientActiveSkillMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientActiveSkillMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      Skill skill = skillComponent.GetSkill(msg.SkillId);
      if (skill == null)
      {
        user.SendToast("技能不存在");
        return;
      }
      ServerUpdateSkillInfoMsg updateSimpleSkillInfoOut = ServerUpdateSkillInfoMsg.Create(skill);
      if (skill.skillType == SkillTypeEnum.JOB_Base_SKILL)
      {
        skill.isActive = !skill.isActive;
        foreach (Skill otherSkill in skillComponent.skillMap.Values)
        {
          if (otherSkill.skillType == SkillTypeEnum.JOB_Base_SKILL
              && otherSkill.skillId != msg.SkillId)
          {
            otherSkill.isActive = false;
            updateSimpleSkillInfoOut.SkillSimpleInfos.Add(otherSkill);
          }
        }
      }
      else if (skill.skillType == SkillTypeEnum.TALENT_SKILL)
      {
        skill.isActive = !skill.isActive;
        foreach (Skill otherSkill in skillComponent.skillMap.Values)
        {
          if (otherSkill.skillType == SkillTypeEnum.TALENT_SKILL && otherSkill.skillId != msg.SkillId)
          {
            otherSkill.isActive = false;
            updateSimpleSkillInfoOut.SkillSimpleInfos.Add(otherSkill);
          }
        }
      }
      else if (skill.skillId == SkillIdEnum.Chan || skill.skillId == SkillIdEnum.Dao)
      {
        skill.isActive = !skill.isActive;
        foreach (Skill otherSkill in skillComponent.skillMap.Values)
        {
          if ((otherSkill.skillId == SkillIdEnum.Chan || otherSkill.skillId == SkillIdEnum.Dao)
              && otherSkill.skillId != msg.SkillId)
          {
            otherSkill.isActive = false;
            updateSimpleSkillInfoOut.SkillSimpleInfos.Add(otherSkill);
          }
        }
      }
      else
      {
        user.SendToast("无法激活该技能！");
        return;
      }
      user.RecalcUserAttrs();
      user.SendMessage(updateSimpleSkillInfoOut, ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue), new ServerShowToastMsg() { message = skill.isActive ? "技能已激活！" : "技能取消激活！" });
      await ETTask.CompletedTask;
    }
  }
}