namespace MaoYouJi
{
  [Event(SceneType.Map)]
  public class KillHunPoEventHanlder : AEvent<Scene, UserKillMonsterEvent>
  {
    protected override async ETTask Run(Scene root, UserKillMonsterEvent args)
    {
      User killedBy = args.killedBy;
      MonsterInfo deader = args.deader;
      AttackInCache attackInCache = args.attackInCache;
      AttackComponent userAttack = killedBy.GetComponent<AttackComponent>();
      SkillComponent skillComp = userAttack.GetComponent<SkillComponent>();
      if (deader.monDescType == MonDescType.HunPo)
      {
        Skill skill = skillComp.GetSkill(SkillIdEnum.HunPo_CaiJiShu);
        if (skill != null)
        {
          // 击杀魂魄怪物后增加魂魄采集技能经验
          if (deader.monBaseType == MonBaseType.YuanLing_HunPo)
          {
            skillComp.AddLifeSkillExp(skill, 1);
          }
          else if (deader.monBaseType == MonBaseType.SiLing_HunPo)
          {
            skillComp.AddLifeSkillExp(skill, 2);
          }
          else if (deader.monBaseType == MonBaseType.KuiGui_HunPo)
          {
            skillComp.AddLifeSkillExp(skill, 3);
          }
          else if (deader.monBaseType == MonBaseType.MoWang_HunPo)
          {
            skillComp.AddLifeSkillExp(skill, 4);
          }
          else if (deader.monBaseType == MonBaseType.NiGuang_HunPo)
          {
            skillComp.AddLifeSkillExp(skill, 5);
          }
          else if (deader.monBaseType == MonBaseType.TaiYangShen_HunPo)
          {
            skillComp.AddLifeSkillExp(skill, 6);
          }
        }
      }
      await ETTask.CompletedTask;
    }
  }
}