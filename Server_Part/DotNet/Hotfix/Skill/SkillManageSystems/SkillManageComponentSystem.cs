using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(SkillManageComponent))]
  [FriendOf(typeof(SkillManageComponent))]
  public static partial class SkillManageComponentSystem
  {
    [EntitySystem]
    public static void Awake(this SkillManageComponent self)
    {
    }

    public static List<Skill> ParseBaseSkill(this SkillManageComponent self, BaseSkill baseSkill)
    {
      List<Skill> skills = new();
      int idx = 1;
      if (baseSkill.skillType == SkillTypeEnum.JOB_Base_SKILL)
      {
        for (int i = 0; i < 99; ++i)
        {
          SkillInfo skillInfo = baseSkill.skillInfos[0];
          Skill skill = new Skill();
          skill.skillId = baseSkill.skillId;
          skill.name = baseSkill.name;
          skill.needBaseJob = baseSkill.needBaseJob;
          skill.job = baseSkill.job;
          skill.typeFlag = baseSkill.typeFlag;
          skill.secondUseNeedState = baseSkill.secondUseNeedState;
          skill.level = idx;
          skill.damage = skillInfo.vals[0] + skillInfo.vals[1] * i;
          skill.maxExp = SkillComponentProc.calcLevelExp(idx + 1);
          skills.Add(skill);
          ++idx;
        }
      }
      else
      {
        foreach (SkillInfo skillInfo in baseSkill.skillInfos)
        {
          Skill skill = new();
          skill.skillId = baseSkill.skillId;
          skill.name = baseSkill.name;
          skill.needBaseJob = baseSkill.needBaseJob;
          skill.job = baseSkill.job;
          skill.typeFlag = baseSkill.typeFlag;
          skill.secondUseNeedState = baseSkill.secondUseNeedState;
          skill.level = idx;
          skill.damage = skillInfo.damage;
          skill.extraDmg = skillInfo.extraDmg;
          skill.expend = (int)skillInfo.expend;
          skill.delayTime = skillInfo.delayTime;
          skill.cd = skillInfo.cd;
          skill.targetStatus = skillInfo.targetStatus;
          skill.selfStatus = skillInfo.selfStatus;
          skill.vals = skillInfo.vals;
          skill.targetNum = skillInfo.targetNum;
          skills.Add(skill);
          ++idx;
        }
      }
      return skills;
    }
  }
}
