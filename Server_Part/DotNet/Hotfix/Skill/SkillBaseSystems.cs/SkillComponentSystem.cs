using System;
using System.Linq;

namespace MaoYouJi
{
  [FriendOf(typeof(SkillComponent))]
  public static partial class SkillComponentSystem
  {
    public static bool RemoveQuickFood(this SkillComponent self, long thingId)
    {
      for (int i = 0; i < self.quickFoods.Length; i++)
      {
        if (self.quickFoods[i] == thingId)
        {
          self.quickFoods[i] = 0;
          return true;
        }
      }
      return false;
    }
    public static void AddLearnSkill(this SkillComponent self, SkillIdEnum skillIdEnum)
    {
      Skill skill = GlobalInfoCache.Instance.GetSkill(skillIdEnum, 1);
      self.AddLearnSkill(skill);
    }

    public static void AddLearnSkill(this SkillComponent self, Skill skill)
    {
      self.skillMap ??= new();
      if (skill.skillType == SkillTypeEnum.JOB_Base_SKILL || skill.skillId == SkillIdEnum.Chan
          || skill.skillId == SkillIdEnum.Dao || skill.skillType == SkillTypeEnum.TALENT_SKILL)
      {
        skill.isActive = false;
      }
      else
      {
        skill.isActive = true;
      }
      self.skillMap.TryAdd(skill.skillId, skill);
      if (self.skillIds.Contains(skill.skillId))
      {
        return;
      }
      self.skillIds.Add(skill.skillId);
    }

    public static void AddBaseSkillExp(this SkillComponent self, SkillIdEnum skillIdEnum, long needAddExp, bool canMultiLevel = false, bool isSysChat = true)
    {
      Skill skill = self.GetSkill(skillIdEnum);
      if (skill == null)
      {
        return;
      }
      self.AddBaseSkillExp(skill, needAddExp, canMultiLevel, isSysChat);
    }

    public static void AddBaseSkillExp(this SkillComponent self, Skill skill, long needAddExp, bool canMultiLevel = false, bool isSysChat = true)
    {
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      User user = self.GetParent<User>();
      if ((skill.level == 99 || skill.level >= attackComponent.level) && skill.nowExp >= skill.maxExp)
      {
        return;
      }
      user.SendChat(skill.name + "的熟练度增加" + needAddExp,
          isSysChat ? ChatType.Sys_Chat : ChatType.Fight_Chat);
      if (skill.level == 99 || skill.level >= attackComponent.level)
      {
        skill.nowExp += needAddExp;
        if (skill.nowExp >= skill.maxExp)
        {
          skill.nowExp = skill.maxExp;
        }
        return;
      }
      long remainExp = needAddExp;
      if (!canMultiLevel)
      {
        remainExp = Math.Min(remainExp, skill.maxExp - skill.nowExp);
      }
      bool levelUp = false;
      while (remainExp > 0)
      {
        long nextExp = skill.maxExp - skill.nowExp;
        if (remainExp >= nextExp)
        {
          skill.nowExp = 0;
          skill.level++;
          skill.maxExp = SkillComponentProc.calcLevelExp(skill.level);
          levelUp = true;
          user.SendChat(skill.name + "的等级升到" + skill.level, ChatType.Sys_Chat);
          remainExp -= nextExp;
        }
        else
        {
          skill.nowExp += remainExp;
          remainExp = 0;
        }
      }
      user.SendMessage(ServerUpdateSkillInfoMsg.Create(skill));
      if (levelUp)
      {
        user.RecalcUserAttrs();
        ServerUpdatePartUserInfoMsg updatePartUserInfoOut = ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack);
        user.SendMessage(updatePartUserInfoOut);
      }
    }
    /**
    * 增加生活技能经验
    * 
    * @param user        用户
    * @param skillIdEnum 技能ID
    * @param addExp      需要增加的经验
    */
    public static long AddLifeSkillExp(this SkillComponent self, Skill skill, long addExp)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      User user = self.GetParent<User>();
      if (user == null || attackComponent == null)
      {
        return 0;
      }
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      Thing hufu = equipComponent.GetUserSpecialEquip(ThingSubType.Special_Equip_Hufu);
      long realAdd = 1;
      if (hufu != null)
      {
        if (skill.skillId == SkillIdEnum.Chan && hufu.thingName.ToString().Contains("ChongYang"))
        {
          realAdd += hufu.GetHuFuAddExp();
        }
        else if (skill.skillId == SkillIdEnum.Dao && hufu.thingName.ToString().Contains("SanFeng"))
        {
          realAdd += hufu.GetHuFuAddExp();
        }
        else if (skill.skillId == SkillIdEnum.Da_Gong && hufu.thingName.ToString().Contains("DaGong"))
        {
          realAdd += hufu.GetHuFuAddExp();
        }
      }
      realAdd *= addExp;
      self.AddBaseSkillExp(skill, realAdd, true);
      return realAdd;
    }

    public static long AddLifeSkillExp(this SkillComponent self, SkillIdEnum skillIdEnum, long addExp)
    {
      Skill skill = self.GetSkill(skillIdEnum);
      if (skill == null)
      {
        return 0;
      }
      return self.AddLifeSkillExp(skill, addExp);
    }
  }
}
