using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class TransferTeamLeaderHandler : MessageLocationHandler<MapNode, ClientTransferTeamLeaderMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientTransferTeamLeaderMsg msg)
    {
      LogicRet getUserRet = TeamProcSys.GetUserTeamWithCheck(msg.UserId, out TeamInfo teamInfo, out User user, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      if (msg.transferUserId == teamInfo.leaderId)
      {
        user.SendToast("不能转让队长给自己");
        return;
      }
      teamInfo.memberInfos.TryGetValue(msg.transferUserId, out TeamMemberInfo transferMemberInfo);
      if (transferMemberInfo == null)
      {
        user.SendToast("目标用户不在队伍中");
        return;
      }
      teamInfo.leaderId = msg.transferUserId;
      teamInfo.updateTime = TimeInfo.Instance.ServerNow();
      teamInfo.SendMessageToAllMember(new ServerUpdateTeamMsg
      {
        teamDaoInfo = teamInfo.ToDaoInfo()
      }, new ServerSendChatMsg
      {
        chatType = ChatType.Team_Chat,
        content = $"<u>{user.nickname}</u> 将队长职位转让给了 <u>{transferMemberInfo.name}</u>"
      });
      user.SendToast("转让队长成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class RemoveTeamMemberHandler : MessageLocationHandler<MapNode, ClientRemoveTeamMemberMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientRemoveTeamMemberMsg msg)
    {
      LogicRet getUserRet = TeamProcSys.GetUserTeamWithCheck(msg.UserId, out TeamInfo teamInfo, out User user, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      teamInfo.memberInfos.TryGetValue(msg.removeUserId, out TeamMemberInfo removeMemberInfo);
      if (removeMemberInfo == null)
      {
        user.SendToast("目标用户不在队伍中");
        return;
      }
      if (removeMemberInfo.userId == teamInfo.leaderId)
      {
        user.SendToast("队长不能被踢出队伍");
        return;
      }
      teamInfo.RemoveChild(removeMemberInfo.userId);
      teamInfo.updateTime = TimeInfo.Instance.ServerNow();
      teamInfo.SendMessageToAllMember(new ServerUpdateTeamMsg
      {
        teamDaoInfo = teamInfo.ToDaoInfo()
      }, new ServerSendChatMsg
      {
        chatType = ChatType.Team_Chat,
        content = $"<u>{user.nickname}</u> 将 <u>{removeMemberInfo.name}</u> 踢出了队伍"
      });
      user.SendToast("移除队员成功");
      User targetUser = GlobalInfoCache.Instance.GetOnlineUser(removeMemberInfo.userId);
      targetUser?.SendToast("您被踢出了队伍");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class InviteFollowHandler : MessageLocationHandler<MapNode, ClientInviteFollowMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientInviteFollowMsg msg)
    {
      LogicRet getUserRet = TeamProcSys.GetUserTeamWithCheck(msg.UserId, out TeamInfo teamInfo, out User user, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      List<TeamMemberInfo> memberInfos = teamInfo.GetMemberInfos();
      foreach (TeamMemberInfo memberInfo in memberInfos)
      {
        if (!memberInfo.isFollow && !memberInfo.userId.Equals(user.Id))
        {
          User targetUser = GlobalInfoCache.Instance.GetOnlineUser(memberInfo.userId);
          if (targetUser == null)
          {
            continue;
          }
          targetUser.SendMessage(new ServerProcInviteFollowMsg());
        }
      }
      teamInfo.SendMessageToAllMember(new ServerSendChatMsg
      {
        chatType = ChatType.Team_Chat,
        content = $"<u>{user.nickname}</u> 邀请所有成员跟随队长行动"
      });
      user.SendToast("邀请跟随成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class FollowLeaderHandler : MessageLocationHandler<MapNode, ClientFollowLeaderMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientFollowLeaderMsg msg)
    {
      LogicRet getUserRet = TeamProcSys.GetUserTeamWithCheck(msg.UserId, out TeamInfo teamInfo, out User user);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      if (teamInfo.leaderId == user.Id)
      {
        user.SendToast("您是队长，无法跟随队长");
        return;
      }
      bool isFollow = teamInfo.FollowLeader(user);
      teamInfo.SendMessageToAllMember(new ServerUpdateTeamMsg
      {
        teamDaoInfo = teamInfo.ToDaoInfo()
      }, new ServerSendChatMsg
      {
        chatType = ChatType.Team_Chat,
        content = $"<u>{user.nickname}</u> " + (isFollow ? "决定跟随队长行动" : "取消跟随队长行动")
      });
      user.SendToast(isFollow ? "跟随队长成功" : "取消跟随队长成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class QuitTeamHandler : MessageLocationHandler<MapNode, ClientQuitTeamMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientQuitTeamMsg msg)
    {
      LogicRet getUserRet = TeamProcSys.GetUserTeamWithCheck(msg.UserId, out TeamInfo teamInfo, out User user);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      if (user.teamInfo == null)
      {
        user.SendToast("不在队伍中，无法退出队伍");
        return;
      }
      if (teamInfo.IsLeader(user.Id))
      {
        teamInfo.SendMessageToAllMember(new ServerSendChatMsg
        {
          chatType = ChatType.Team_Chat,
          content = $"<u>{user.nickname}</u> 解散了队伍"
        });
        teamInfo.Dispose();
        return;
      }
      else
      {
        teamInfo.RemoveChild(user.Id);
        teamInfo.updateTime = TimeInfo.Instance.ServerNow();
        teamInfo.SendMessageToAllMember(new ServerUpdateTeamMsg
        {
          teamDaoInfo = teamInfo.ToDaoInfo()
        }, new ServerSendChatMsg
        {
          chatType = ChatType.Team_Chat,
          content = $"<u>{user.nickname}</u> 退出了队伍"
        });
        user.SendToast("退出队伍成功");
        user.SendMessage(new ServerUpdateTeamMsg
        {
          teamDaoInfo = null
        }, ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Social_Info));
      }
      await ETTask.CompletedTask;
    }
  }

}