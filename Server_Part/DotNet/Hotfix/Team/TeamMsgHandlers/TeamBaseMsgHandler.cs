using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ShowMyTeamHandler : MessageLocationHandler<MapNode, ShowMyTeamReq, ShowMyTeamResp>
  {
    protected override async ETTask Run(MapNode nowMap, ShowMyTeamReq request, ShowMyTeamResp response)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(request.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        response.SetError(getUserRet.Message);
        return;
      }
      UserTeamInfo userTeamInfo = user.teamInfo;
      if (userTeamInfo == null)
      {
        response.teamDaoInfo = null;
        return;
      }
      GlobalInfoCache.Instance.allTeamCache.TryGetValue(userTeamInfo.teamId, out TeamInfo teamInfo);
      if (teamInfo == null)
      {
        response.teamDaoInfo = null;
        user.teamInfo = null;
        return;
      }
      teamInfo.RefreshMemberInfo();
      response.teamDaoInfo = teamInfo.ToDaoInfo();
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class QueryOpenTeamHandler : MessageLocationHandler<MapNode, QueryOpenTeamReq, QueryOpenTeamResp>
  {
    protected override async ETTask Run(MapNode nowMap, QueryOpenTeamReq request, QueryOpenTeamResp response)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(request.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        response.SetError(getUserRet.Message);
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (request.canEnter)
      {
        request.minLevel = (int)attackComponent.level;
        request.maxLevel = (int)attackComponent.level;
        request.minAttack = (int)attackComponent.attackNum;
      }
      TeamManageComponent teamManageComponent = nowMap.Root().GetComponent<TeamManageComponent>();
      List<TeamDaoInfo> teamList = teamManageComponent.QueryOpenTeam(request);
      response.teamInfos = teamList;
      response.nowPage = request.page;
      response.totalPage = teamManageComponent.CountOpenTeam(request);
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class CreateTeamHandler : MessageLocationHandler<MapNode, ClientCreateTeamMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientCreateTeamMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, false, true);
      if (!getUserRet.IsSuccess)
      {
        return;
      }
      if (user.activityName == ActNameEnum.Da_TaoSha)
      {
        user.SendToast("大逃杀活动中不能创建队伍");
        return;
      }
      if (user.teamInfo != null)
      {
        user.SendToast("已在队伍中，无法创建队伍");
        return;
      }
      if (msg.teamName == null || msg.teamName.Length == 0)
      {
        user.SendToast("队伍名称不能为空");
        return;
      }
      if (msg.teamDesc == null || msg.teamDesc.Length == 0)
      {
        msg.teamDesc = "队长很懒，什么都没写";
      }
      TeamManageComponent teamManageComponent = nowMap.Root().GetComponent<TeamManageComponent>();
      TeamInfo teamInfo = teamManageComponent.AddChild<TeamInfo, ClientCreateTeamMsg, User>(msg, user);
      user.SendMessage(new ServerUpdateTeamMsg
      {
        teamDaoInfo = teamInfo.ToDaoInfo()
      });
      user.SendToast($"创建队伍成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ApplyTeamHandler : MessageLocationHandler<MapNode, ClientApplyTeamMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientApplyTeamMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, false, true);
      if (!getUserRet.IsSuccess)
      {
        return;
      }
      if (user.activityName == ActNameEnum.Da_TaoSha)
      {
        user.SendToast("大逃杀活动中不能申请加入队伍");
        return;
      }
      if (user.teamInfo != null)
      {
        user.SendToast("已在队伍中，无法申请加入队伍");
        return;
      }
      GlobalInfoCache.Instance.allTeamCache.TryGetValue(msg.teamId, out TeamInfo teamInfo);
      if (teamInfo == null)
      {
        user.SendToast("队伍不存在");
        return;
      }
      if (teamInfo.memberInfos.Count >= 5)
      {
        user.SendToast("队伍人数已满");
        return;
      }
      if (teamInfo.memberInfos.ContainsKey(user.Id))
      {
        user.SendToast("您已经在队伍中");
        return;
      }
      if (msg.applyMessage != null && msg.applyMessage.Length > 30)
      {
        user.SendToast("申请信息不能超过30个字符");
        return;
      }
      if (msg.applyMessage == null || msg.applyMessage.Length == 0)
      {
        msg.applyMessage = "请求加入队伍";
      }
      LogicRet canApplyRet = teamInfo.CanApply(user);
      if (!canApplyRet.IsSuccess)
      {
        user.SendToast(canApplyRet.Message);
        return;
      }
      TeamManageComponent teamManageComponent = nowMap.Root().GetComponent<TeamManageComponent>();
      int applyCount = teamManageComponent.CountTeamApplyInfo(msg.teamId);
      if (applyCount >= 20)
      {
        teamInfo.SendMessageToAllMember(new ServerSendChatMsg
        {
          chatType = ChatType.Team_Chat,
          content = "队伍申请列表已满，请队长赶快处理"
        });
        user.SendToast("队伍申请列表已满");
        return;
      }
      // 发送红点
      // UpdateMenuReddotOut updateMenuReddotOut = new UpdateMenuReddotOut(MenuReddotEnum.Menu_Team_Reddot, true);
      // SocketUtil.sendMessageToUser(teamSystem.teamInfo.leaderId, updateMenuReddotOut);
      // 添加申请信息
      teamManageComponent.AddApplyInfo(msg, user, false);
      // 发送消息
      teamInfo.SendMessageToAllMember(new ServerSendChatMsg
      {
        chatType = ChatType.Team_Chat,
        content = $"<u>{user.nickname}</u>申请加入队伍"
      });
      user.SendChat($"您申请加入队伍<u>{teamInfo.name}</u>", ChatType.Team_Chat);
      user.SendToast("申请成功，等待队长审批中");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class EditTeamHandler : MessageLocationHandler<MapNode, ClientEditTeamMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientEditTeamMsg msg)
    {
      LogicRet getUserRet = TeamProcSys.GetUserTeamWithCheck(msg.UserId, out TeamInfo teamInfo, out User user, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      teamInfo.EditTeam(msg, user);
      teamInfo.SendMessageToAllMember(new ServerUpdateTeamMsg
      {
        teamDaoInfo = teamInfo.ToDaoInfo()
      });
      user.SendToast("编辑队伍信息成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ProcTeamApplyHandler : MessageLocationHandler<MapNode, ClientProcTeamApplyMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientProcTeamApplyMsg msg)
    {
      LogicRet getUserRet = TeamProcSys.GetUserTeamWithCheck(msg.UserId, out TeamInfo teamInfo, out User user, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      TeamManageComponent teamManageComponent = nowMap.Root().GetComponent<TeamManageComponent>();
      if (msg.isProcAll)
      {
        List<TeamApplyInfo> applyInfos = teamManageComponent.GetApplyInfos(teamInfo.Id);
        if (msg.isAccept)
        {
          if (teamInfo.memberInfos.Count >= 5)
          {
            user.SendToast("队伍人数已满，无法添加成员");
            return;
          }
          List<string> addUserNames = new List<string>();
          for (int i = 0; i < applyInfos.Count && teamInfo.memberInfos.Count < 5; i++)
          {
            User applyUser = GlobalInfoCache.Instance.GetOnlineUser(applyInfos[i].userId);
            if (applyUser == null)
            {
              continue;
            }
            if (applyUser.teamInfo != null || applyUser.activityName == ActNameEnum.Da_TaoSha)
            {
              continue;
            }
            teamInfo.AddChildWithId<TeamMember, User>(applyUser.Id, applyUser);
            addUserNames.Add(applyUser.nickname);
          }
          if (addUserNames.Count > 0)
          {
            teamInfo.SendMessageToAllMember(new ServerSendChatMsg
            {
              chatType = ChatType.Team_Chat,
              content = $"队长已同意<u>{string.Join(",", addUserNames)}</u>加入队伍"
            });
          }
        }
        else
        {
          foreach (TeamApplyInfo teamApplyInfo in applyInfos)
          {
            ChatProSystem.SendChatToUser(teamApplyInfo.userId, $"{teamInfo.name}已拒绝您加入队伍", ChatType.Team_Chat);
          }
          teamInfo.SendMessageToAllMember(new ServerSendChatMsg
          {
            chatType = ChatType.Team_Chat,
            content = "队长已拒绝所有入队申请"
          });
        }
        teamManageComponent.RemoveApplyInfo(teamInfo.Id, 0, false);
        // user.SendMessage(new UpdateMenuReddotOut(MenuReddotEnum.Menu_Team_Reddot,
        //     teamManageComponent.ShowTeamReddot(user)));
      }
      else
      {
        if (teamManageComponent.HasApplyInfo(teamInfo.Id, msg.applyUserId, false))
        {
          teamManageComponent.RemoveApplyInfo(teamInfo.Id, msg.applyUserId, false);
          // user.SendMessage(new UpdateMenuReddotOut(MenuReddotEnum.Menu_Team_Reddot,
          //     teamProc.showTeamReddot(user)));
          User applyUser = GlobalInfoCache.Instance.GetOnlineUser(msg.applyUserId);
          if (applyUser == null)
          {
            user.SendToast("该用户在线");
            return;
          }
          if (msg.isAccept)
          {
            if (applyUser.teamInfo != null)
            {
              user.SendToast("该用户已在队伍中");
              return;
            }
            if (applyUser.activityName == ActNameEnum.Da_TaoSha)
            {
              user.SendToast("大逃杀活动中不能加入队伍");
              return;
            }
            if (teamInfo.memberInfos.Count >= 5)
            {
              applyUser.SendToast("队伍人数已满，无法添加成员");
              return;
            }
            teamInfo.AddChildWithId<TeamMember, User>(applyUser.Id, applyUser);
            teamInfo.SendMessageToAllMember(new ServerSendChatMsg
            {
              chatType = ChatType.Team_Chat,
              content = $"队长已同意<u>{applyUser.nickname}</u>加入队伍"
            });
          }
          else
          {
            teamInfo.SendMessageToAllMember(new ServerSendChatMsg
            {
              chatType = ChatType.Team_Chat,
              content = $"队长已拒绝<u>{applyUser.nickname}</u>加入队伍"
            });
            applyUser.SendChat($"{teamInfo.name}已拒绝您加入队伍", ChatType.Team_Chat);
          }
        }
        else
        {
          user.SendToast("该用户未申请加入队伍");
        }
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ProcTeamInviteHandler : MessageLocationHandler<MapNode, ClientProcTeamInviteMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientProcTeamInviteMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, false, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      if (user.activityName == ActNameEnum.Da_TaoSha)
      {
        user.SendToast("大逃杀活动中不能处理邀请信息");
        return;
      }
      TeamManageComponent teamManageComponent = nowMap.Root().GetComponent<TeamManageComponent>();
      if (msg.isProcAll)
      {
        List<TeamApplyInfo> inviteInfos = teamManageComponent.GetInviteInfos(user.Id);
        foreach (TeamApplyInfo inviteInfo in inviteInfos)
        {
          GlobalInfoCache.Instance.allTeamCache.TryGetValue(inviteInfo.teamId, out TeamInfo teamInfo);
          if (teamInfo == null)
          {
            continue;
          }
          teamInfo.SendMessageToAllMember(new ServerSendChatMsg
          {
            chatType = ChatType.Team_Chat,
            content = $"<u>{user.nickname}</u>已拒绝入队申请"
          });
        }
        teamManageComponent.RemoveApplyInfo(0, user.Id, true);
        // SocketUtil.sendMessageToUser(user.id, new UpdateMenuReddotOut(MenuReddotEnum.Menu_Team_Reddot, false));
      }
      else
      {
        if (!teamManageComponent.HasApplyInfo(msg.teamId, user.Id, true))
        {
          user.SendToast("该队伍的邀请信息已过期");
          return;
        }
        teamManageComponent.RemoveApplyInfo(msg.teamId, user.Id, true);
        // SocketUtil.sendMessageToUser(user.id, new UpdateMenuReddotOut(MenuReddotEnum.Menu_Team_Reddot,
        //     teamProc.showTeamReddot(user)));
        GlobalInfoCache.Instance.allTeamCache.TryGetValue(msg.teamId, out TeamInfo teamInfo);
        if (teamInfo == null)
        {
          user.SendToast("该队伍不存在");
          return;
        }
        if (msg.isAccept)
        {
          if (user.teamInfo != null)
          {
            user.SendToast("您已在队伍中");
            return;
          }
          if (teamInfo.memberInfos.Count >= 5)
          {
            user.SendToast("队伍人数已满，无法添加成员");
            return;
          }
          if (teamInfo.HasSameAccount(user))
          {
            user.SendToast("同账号角色无法加入队伍");
            return;
          }
          teamInfo.AddChildWithId<TeamMember, User>(user.Id, user);
          teamInfo.SendMessageToAllMember(new ServerSendChatMsg
          {
            chatType = ChatType.Team_Chat,
            content = $"<u>{user.nickname}</u>已加入队伍"
          }, new ServerUpdateTeamMsg
          {
            teamDaoInfo = teamInfo.ToDaoInfo()
          });
        }
        else
        {
          user.SendChat($"您已拒绝加入队伍{teamInfo.name}", ChatType.Team_Chat);
          teamInfo.SendMessageToAllMember(new ServerSendChatMsg
          {
            chatType = ChatType.Team_Chat,
            content = $"<u>{user.nickname}</u>已拒绝加入队伍"
          });
        }
      }
      if (user.teamInfo == null)
      {
        // GetInviteListOut out = new GetInviteListOut();
        // out.inviteInfos = teamManageComponent.GetInviteInfosDetail(user.Id);
        // user.SendMessage(out);
      }
      else
      {
        // GetApplyListOut out = new GetApplyListOut();
        // out.applyInfos = teamManageComponent.GetApplyMemberInfos(teamInfo.Id);
        // user.SendMessage(out);
      }
      user.SendToast("处理邀请成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class InviteTeamHandler : MessageLocationHandler<MapNode, ClientInviteTeamMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientInviteTeamMsg msg)
    {
      LogicRet getUserRet = TeamProcSys.GetUserTeamWithCheck(msg.UserId, out TeamInfo teamInfo, out User user, false);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      if (user.activityName == ActNameEnum.Da_TaoSha)
      {
        user.SendToast("大逃杀活动中不能处理邀请信息");
        return;
      }
      User targetUser = GlobalInfoCache.Instance.GetOnlineUser(msg.inviteUserId);
      if (targetUser == null)
      {
        user.SendToast("目标用户不在线");
        return;
      }
      if (targetUser.activityName == ActNameEnum.Da_TaoSha)
      {
        user.SendToast("对方正在大逃杀活动中，无法邀请");
        return;
      }
      if (targetUser.teamInfo != null)
      {
        user.SendToast("该用户已在队伍中");
        return;
      }
      TeamManageComponent teamManageComponent = nowMap.Root().GetComponent<TeamManageComponent>();
      if (teamManageComponent.HasApplyInfo(teamInfo.Id, msg.inviteUserId, true))
      {
        user.SendToast("无法重复邀请");
        return;
      }
      if (teamManageComponent.CountUserInviteInfo(msg.inviteUserId) >= 10)
      {
        user.SendToast("该用户已达到邀请上限");
        targetUser.SendToast("您的队伍邀请已达到上限，请尽快处理邀请信息");
        return;
      }
      teamManageComponent.AddInviteInfo(msg, teamInfo.Id);
      teamInfo.SendMessageToAllMember(new ServerSendChatMsg
      {
        chatType = ChatType.Team_Chat,
        content = $"<u>{user.nickname}</u>邀请<u>{targetUser.nickname}</u>加入队伍"
      });
      // outList.add(new UpdateMenuReddotOut(MenuReddotEnum.Menu_Team_Reddot, true));
      targetUser.SendChat($"您已被邀请加入队伍<u>{teamInfo.name}</u>", ChatType.Team_Chat);
      user.SendToast("邀请成员成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class GetApplyListMsgHandler : MessageLocationHandler<MapNode, ClientGetApplyListMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientGetApplyListMsg msg)
    {
      LogicRet getUserRet = TeamProcSys.GetUserTeamWithCheck(msg.UserId, out TeamInfo teamInfo, out User user, false);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      TeamManageComponent teamManageComponent = nowMap.Root().GetComponent<TeamManageComponent>();
      ServerGetApplyListMsg resp = new()
      {
        applyList = teamManageComponent.GetApplyMemberInfos(teamInfo.Id)
      };
      user.SendMessage(resp);
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class GetInviteListMsgHandler : MessageLocationHandler<MapNode, ClientGetInviteListMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientGetInviteListMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      TeamManageComponent teamManageComponent = nowMap.Root().GetComponent<TeamManageComponent>();
      ServerGetInviteListMsg resp = new()
      {
        inviteInfos = teamManageComponent.GetInviteInfosDetail(user.Id)
      };
      user.SendMessage(resp);
      await ETTask.CompletedTask;
    }
  }
}
