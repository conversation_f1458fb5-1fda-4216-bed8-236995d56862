namespace MaoYouJi
{
  public static class TeamProcSys
  {
    // 填充队伍成员信息
    public static void FillMemberInfo(TeamMemberInfo memberInfo, User user)
    {
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      memberInfo.userId = user.Id;
      memberInfo.name = user.nickname;
      memberInfo.userSkin = attackComponent.NowSkin;
      memberInfo.attackNum = attackComponent.attackNum;
      memberInfo.blood = attackComponent.blood;
      memberInfo.blue = attackComponent.blue;
      memberInfo.maxBlood = attackComponent.maxBlood;
      memberInfo.maxBlue = attackComponent.maxBlue;
      memberInfo.level = (int)attackComponent.level;
      memberInfo.offlineState = user.offlineState;
      memberInfo.liveState = attackComponent.LiveState;
      memberInfo.nowMap = moveComponent.nowMap;
      memberInfo.nowPoint = moveComponent.nowPoint;
      InFightComponent inFightComponent = attackComponent.GetComponent<InFightComponent>();
      if (inFightComponent != null)
      {
        memberInfo.attackTarget = inFightComponent.attackTarget;
      }
      else
      {
        memberInfo.attackTarget = null;
      }
    }

    public static LogicRet GetUserTeamWithCheck(long userId, out TeamInfo teamInfo, out User user, bool checkLeader = false)
    {
      teamInfo = null;
      user = GlobalInfoCache.Instance.GetOnlineUser(userId);
      if (user == null)
      {
        return LogicRet.Failed("用户不存在");
      }
      if (user.teamInfo == null)
      {
        return LogicRet.Failed("您不在队伍中");
      }
      GlobalInfoCache.Instance.allTeamCache.TryGetValue(user.teamInfo.teamId, out teamInfo);
      if (teamInfo == null)
      {
        return LogicRet.Failed("队伍不存在");
      }
      if (checkLeader && teamInfo.leaderId != userId)
      {
        return LogicRet.Failed("您不是队长");
      }
      return LogicRet.Success;
    }
  }
}
