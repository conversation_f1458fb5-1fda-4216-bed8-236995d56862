using System.Collections.Generic;
using MongoDB.Driver;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(TeamManageComponent))]
  [FriendOf(typeof(TeamManageComponent))]
  public static partial class TeamManageCompSystem
  {
    [EntitySystem]
    private static void Awake(this TeamManageComponent self)
    {
    }

    public static List<TeamDaoInfo> QueryOpenTeam(this TeamManageComponent self, QueryOpenTeamReq req)
    {
      // 获取 MongoDB 集合
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamInfo>();

      // 构建查询条件
      var filterBuilder = Builders<TeamInfo>.Filter;
      var filter = filterBuilder.Eq(x => x.isOpen, true);

      if (req.teamType != TeamType.None)
      {
        filter &= filterBuilder.Eq(x => x.teamType, req.teamType);
      }
      filter &= filterBuilder.Lte(x => x.filterInfo.minLevel, req.minLevel);
      filter &= filterBuilder.Gte(x => x.filterInfo.maxLevel, req.maxLevel);
      filter &= filterBuilder.Lte(x => x.filterInfo.minAttack, req.minAttack);

      // 分页
      int skip = req.page * TeamManageComponent.TEAM_PAGE_SIZE;
      int limit = TeamManageComponent.TEAM_PAGE_SIZE;

      // 查询
      var teamList = collection.Find(filter)
        .Project<TeamInfo>(Builders<TeamInfo>.Projection.Include(x => x.Id))
        .Skip(skip)
        .Limit(limit)
        .ToList();
      List<TeamDaoInfo> retList = new List<TeamDaoInfo>();
      foreach (TeamInfo teamInfo in teamList)
      {
        GlobalInfoCache.Instance.allTeamCache.TryGetValue(teamInfo.Id, out TeamInfo teamSystemRef);
        if (teamSystemRef == null)
        {
          continue;
        }
        retList.Add(teamSystemRef.ToDaoInfo());
      }
      return retList;
    }

    // 统计开放队伍数量
    public static int CountOpenTeam(this TeamManageComponent self, QueryOpenTeamReq req)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamInfo>();
      var filterBuilder = Builders<TeamInfo>.Filter;
      var filter = filterBuilder.Eq(x => x.isOpen, true);

      if (req.teamType != TeamType.None)
      {
        filter &= filterBuilder.Eq(x => x.teamType, req.teamType);
      }
      filter &= filterBuilder.Lte(x => x.filterInfo.minLevel, req.minLevel);
      filter &= filterBuilder.Gte(x => x.filterInfo.maxLevel, req.maxLevel);
      filter &= filterBuilder.Lte(x => x.filterInfo.minAttack, req.minAttack);
      long count = collection.CountDocuments(filter);
      return (int)(count / TeamManageComponent.TEAM_PAGE_SIZE + (count % TeamManageComponent.TEAM_PAGE_SIZE == 0 ? 0 : 1));
    }

    // 统计队伍申请人数
    public static int CountTeamApplyInfo(this TeamManageComponent self, long teamId)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamApplyInfo>();
      var filterBuilder = Builders<TeamApplyInfo>.Filter;
      var filter = filterBuilder.Eq(x => x.teamId, teamId) & filterBuilder.Eq(x => x.isInvite, false);
      return (int)collection.CountDocuments(filter);
    }

    // 检查是否已经申请加入队伍
    public static bool HasApplyInfo(this TeamManageComponent self, long teamId, long userId, bool isInvite)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamApplyInfo>();
      var filterBuilder = Builders<TeamApplyInfo>.Filter;
      var filter = filterBuilder.Eq(x => x.isInvite, isInvite);
      if (teamId != 0)
        filter &= filterBuilder.Eq(x => x.teamId, teamId);
      if (userId != 0)
        filter &= filterBuilder.Eq(x => x.userId, userId);
      return collection.Find(filter).Any();
    }

    // // 添加申请信息
    public static void AddApplyInfo(this TeamManageComponent self, ClientApplyTeamMsg input, User user, bool isInvite)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamApplyInfo>();
      var teamApplyInfo = new TeamApplyInfo
      {
        userId = user.Id,
        teamId = input.teamId,
        applyTime = TimeInfo.Instance.ServerNow(),
        applyMessage = input.applyMessage,
        isInvite = isInvite
      };
      collection.InsertOne(teamApplyInfo);
    }

    // 添加邀请信息
    public static void AddInviteInfo(this TeamManageComponent self, ClientInviteTeamMsg input, long teamId)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamApplyInfo>();
      var teamApplyInfo = new TeamApplyInfo
      {
        userId = input.inviteUserId,
        teamId = teamId,
        applyTime = TimeInfo.Instance.ServerNow(),
        applyMessage = input.inviteMessage,
        isInvite = true
      };
      collection.InsertOne(teamApplyInfo);
    }

    // 获取邀请信息
    public static List<TeamApplyInfo> GetInviteInfos(this TeamManageComponent self, long userId)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamApplyInfo>();
      var filterBuilder = Builders<TeamApplyInfo>.Filter;
      var filter = filterBuilder.Eq(x => x.userId, userId) & filterBuilder.Eq(x => x.isInvite, true);
      return collection.Find(filter).ToList();
    }

    // 统计用户邀请人数
    public static int CountUserInviteInfo(this TeamManageComponent self, long userId)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamApplyInfo>();
      var filterBuilder = Builders<TeamApplyInfo>.Filter;
      var filter = filterBuilder.Eq(x => x.userId, userId) & filterBuilder.Eq(x => x.isInvite, true);
      return (int)collection.CountDocuments(filter);
    }

    // 删除申请信息
    public static void RemoveApplyInfo(this TeamManageComponent self, long teamId, long userId, bool isInvite)
    {
      if (teamId == 0 && userId == 0)
      {
        ETLog.Error("删除申请信息失败，teamId和userId都为空");
        return;
      }
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamApplyInfo>();
      var filterBuilder = Builders<TeamApplyInfo>.Filter;
      var filter = filterBuilder.Eq(x => x.isInvite, isInvite);
      if (teamId != 0)
        filter &= filterBuilder.Eq(x => x.teamId, teamId);
      if (userId != 0)
        filter &= filterBuilder.Eq(x => x.userId, userId);
      collection.DeleteMany(filter);
    }

    // 获取队伍申请信息
    public static List<TeamMemberInfo> GetApplyMemberInfos(this TeamManageComponent self, long teamId)
    {
      List<TeamApplyInfo> applyInfos = self.GetApplyInfos(teamId);
      List<TeamMemberInfo> retList = new();
      foreach (TeamApplyInfo applyInfo in applyInfos)
      {
        User applyUser = GlobalInfoCache.Instance.GetOnlineUser(applyInfo.userId);
        if (applyUser == null)
        {
          continue;
        }
        TeamMemberInfo memberInfo = new TeamMemberInfo();
        memberInfo.applyMessage = applyInfo.applyMessage;
        TeamProcSys.FillMemberInfo(memberInfo, applyUser);
        retList.Add(memberInfo);
      }
      return retList;
    }

    public static List<TeamInviteInfo> GetInviteInfosDetail(this TeamManageComponent self, long userId)
    {
      List<TeamApplyInfo> inviteInfos = self.GetInviteInfos(userId);
      List<TeamInviteInfo> retList = new();
      foreach (TeamApplyInfo inviteInfo in inviteInfos)
      {
        GlobalInfoCache.Instance.allTeamCache.TryGetValue(inviteInfo.teamId, out TeamInfo teamInfo);
        if (teamInfo == null)
        {
          continue;
        }
        TeamInviteInfo teamInviteInfo = new TeamInviteInfo();
        teamInviteInfo.teamDaoInfo = teamInfo.ToDaoInfo();
        teamInviteInfo.inviteMsg = inviteInfo.applyMessage;
        retList.Add(teamInviteInfo);
      }
      return retList;
    }

    public static List<TeamApplyInfo> GetApplyInfos(this TeamManageComponent self, long teamId)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamApplyInfo>();
      var filterBuilder = Builders<TeamApplyInfo>.Filter;
      var filter = filterBuilder.Eq(x => x.teamId, teamId) & filterBuilder.Eq(x => x.isInvite, false);
      return collection.Find(filter).ToList();
    }
  }
}
