﻿using System;

namespace MaoYouJi
{
  [FriendOf(typeof(DBManagerComponent))]
  public static partial class DBManagerComponentSystem
  {
    public static DBComponent GetZoneDB(this DBManagerComponent self, int zone)
    {
      DBComponent dbComponent = self.GetChild<DBComponent>(zone);
      if (dbComponent != null)
      {
        return dbComponent;
      }

      StartZoneConfig startZoneConfig = StartZoneConfigCategory.Instance.Get(zone);
      if (startZoneConfig.DBConnection == "")
      {
        throw new Exception($"zone: {zone} not found mongo connect string");
      }

      dbComponent = self.AddChildWithId<DBComponent, string, string>(zone, startZoneConfig.DBConnection, startZoneConfig.DBName);
      return dbComponent;
    }

    public static DBComponent GetMyZoneDB(this DBManagerComponent self)
    {
      return self.GetZoneDB(self.Root().Scene().Zone());
    }
  }
}