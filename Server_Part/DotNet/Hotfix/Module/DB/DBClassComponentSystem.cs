using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using MongoDB.Driver;

namespace MaoYouJi
{
  public static partial class DBComponentSystem
  {
    public static async ETTask<T> QueryClass<T>(this DBComponent self, FilterDefinition<T> filter, string collection = null)
    {
      using (await self.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DB, RandomGenerator.RandInt64() % DBComponent.TaskCount))
      {
        IAsyncCursor<T> cursor = await self.GetCollection<T>(collection).FindAsync(filter);

        return await cursor.FirstOrDefaultAsync();
      }
    }

    public static async ETTask<List<T>> QueryClass<T>(this DBComponent self, Expression<Func<T, bool>> filter, string collection = null)
    {
      using (await self.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DB, RandomGenerator.RandInt64() % DBComponent.TaskCount))
      {
        IAsyncCursor<T> cursor = await self.GetCollection<T>(collection).FindAsync(filter);

        return await cursor.ToListAsync();
      }
    }

    public static async ETTask SaveClass<T>(this DBComponent self, FilterDefinition<T> filter, T obj, string collection = null)
    {
      if (obj == null)
      {
        ETLog.Error($"save entity is null: {typeof(T).FullName}");

        return;
      }

      if (collection == null)
      {
        collection = obj.GetType().FullName;
      }

      using (await self.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DB, RandomGenerator.RandInt64() % DBComponent.TaskCount))
      {
        await self.GetCollection<T>(collection).ReplaceOneAsync(filter, obj, new ReplaceOptions { IsUpsert = true });
      }
    }

    public static async ETTask<bool> InsertClassOne<T>(this DBComponent self, T obj, string collection = null)
    {
      try
      {
        if (collection == null)
        {
          collection = typeof(T).FullName;
        }

        using (await self.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DB, RandomGenerator.RandInt64() % DBComponent.TaskCount))
        {
          await self.GetCollection<T>(collection).InsertOneAsync(obj);
        }
      }
      catch (Exception e)
      {
        ETLog.Error($"insert class one error: {e.Message}");
        return false;
      }
      return true;
    }

    public static async ETTask InsertClassBatch<T>(this DBComponent self, IEnumerable<T> list, string collection = null)
    {
      if (collection == null)
      {
        collection = typeof(T).FullName;
      }

      using (await self.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DB, RandomGenerator.RandInt64() % DBComponent.TaskCount))
      {
        await self.GetCollection<T>(collection).InsertManyAsync(list);
      }
    }
  }
}