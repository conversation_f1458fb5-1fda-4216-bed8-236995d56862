namespace MaoYouJi
{
  public static class EquipCompProc
  {
    // 获取新装备时的处理
    public static void ProcNewEquip(this Equipment equipment)
    {
      equipment.nowAttrInfo ??= equipment.baseAttrInfo.Clone() as EquipAttrInfo;
      equipment.remainUseCnt = equipment.useCnt;
      equipment.gemList = new Material[3];
      equipment.maxGemNum = 0;
    }

    public static int GetHuFuAddExp(this Thing hufu)
    {
      if (hufu == null)
      {
        return 0;
      }
      else if (hufu.thingName.ToString().StartsWith("GaoJi_"))
      {
        return 2;
      }
      else if (hufu.thingName.ToString().StartsWith("QiangXiao_"))
      {
        return 4;
      }
      else
      {
        return 1;
      }
    }
  }
}
