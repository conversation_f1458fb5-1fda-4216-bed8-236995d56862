using System;
using System.Collections.Generic;
using System.Text;

namespace MaoYouJi
{
  public static partial class EquipComponentSystem
  {
    [EntitySystem]
    private static void Load(this EquipComponent self)
    {

    }

    public static Equipment GetEquipByPart(this EquipComponent self, EquipPart equipPart)
    {
      return self.equipList.Find(equipment => equipment.equipPart == equipPart);
    }

    public static void AutoSubUseCnt(this EquipComponent self)
    {
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      User user = attackComponent.GetParent<User>();
      StringBuilder sb = new();
      bool needRecal = false;
      foreach (Equipment equipment in self.equipList)
      {
        if (equipment.remainUseCnt <= 0)
        {
          continue;
        }
        uint randomNum = RandomGenerator.RandUInt32() % 100;
        if (randomNum < 2)
        {
          equipment.remainUseCnt--;
          sb.Append(equipment.equipName + "，");
        }
        if (equipment.remainUseCnt <= 0)
        {
          needRecal = true;
        }
      }
      if (sb.Length > 0)
      {
        user.SendChat("您的装备" + sb.ToString().Substring(0, sb.Length - 1) + "耐久度减少1点", ChatType.Sys_Chat);
      }
      if (needRecal)
      {
        user.RecalcUserAttrs();
        ServerUpdatePartUserInfoMsg updatePartUserInfoOut = ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack);
        user.SendMessage(updatePartUserInfoOut);
      }
    }

    /**
    * 退出战斗前恢复血量和蓝量
    * 
    * @param user 用户
    */
    public static void RecoverUserBeforeQuitAttack(this EquipComponent self)
    {
      Thing bloodRecover = self.GetUserSpecialEquip(ThingSubType.Special_Equip_Recover),
          blueRecover = self.GetUserSpecialEquip(ThingSubType.Special_Equip_Recover_Blue);
      AttackComponent attackComponent = self.GetParent<AttackComponent>();
      User user = attackComponent.GetParent<User>();
      if (bloodRecover != null)
      {
        long needRecoverNum = attackComponent.maxBlood - attackComponent.blood;
        if (bloodRecover.thingName == ThingNameEnum.LingHunZhiZhu_Lv1)
        {
          needRecoverNum = (long)(attackComponent.maxBlood * 0.75) - attackComponent.blood;
        }
        else if (bloodRecover.thingName == ThingNameEnum.LingHunZhiZhu_Lv2)
        {
          needRecoverNum = (long)(attackComponent.maxBlood * 0.85) - attackComponent.blood;
        }
        else if (bloodRecover.thingName == ThingNameEnum.LingHunZhiZhu_Lv3)
        {
          needRecoverNum = (long)(attackComponent.maxBlood * 0.95) - attackComponent.blood;
        }
        needRecoverNum = Math.Min(needRecoverNum, bloodRecover.vals[0]);
        if (needRecoverNum > 0)
        {
          bloodRecover.vals[0] -= needRecoverNum;
          attackComponent.blood += needRecoverNum;
          // UpdateAttackHelper.UpdateAttackBlood(attackComponent, needRecoverNum);
          user.SendChat("您的" + bloodRecover.thingName + "为你恢复了" + needRecoverNum + "点血量", ChatType.Sys_Chat);
        }
      }
      if (blueRecover != null)
      {
        long needRecoverNum = attackComponent.maxBlue - attackComponent.blue;
        if (blueRecover.thingName == ThingNameEnum.MoFaZhiQuan_Lv1)
        {
          needRecoverNum = (long)(attackComponent.maxBlue * 0.75) - attackComponent.blue;
        }
        else if (blueRecover.thingName == ThingNameEnum.MoFaZhiQuan_Lv2)
        {
          needRecoverNum = (long)(attackComponent.maxBlue * 0.85) - attackComponent.blue;
        }
        else if (blueRecover.thingName == ThingNameEnum.MoFaZhiQuan_Lv3)
        {
          needRecoverNum = (long)(attackComponent.maxBlue * 0.95) - attackComponent.blue;
        }
        needRecoverNum = Math.Min(needRecoverNum, blueRecover.vals[0]);
        if (needRecoverNum > 0)
        {
          // UpdateAttackHelper.updateAttackBlue(attackInfo, needRecoverNum);
          blueRecover.vals[0] -= needRecoverNum;
          attackComponent.blue += needRecoverNum;
          user.SendChat("您的" + blueRecover.thingName + "为你恢复了" + needRecoverNum + "点蓝量", ChatType.Sys_Chat);
        }
      }
    }
    /**
    * 增加技能经验的时候，获取具体的经验值
    */
    public static int GetAddSkillExp(this EquipComponent self, MaoJob job)
    {
      Thing hufu = self.GetUserSpecialEquip(ThingSubType.Special_Equip_Hufu);
      if (hufu == null)
      {
        return 1;
      }
      List<ThingNameEnum> hufuList = EquipConstant.JobHufuMap[job];
      if (hufuList == null)
      {
        return 1;
      }
      if (hufuList.Contains(hufu.thingName))
      {
        return 1 + hufu.GetHuFuAddExp();
      }
      return 1;
    }
  }
}
