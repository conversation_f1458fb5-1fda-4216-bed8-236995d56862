namespace MaoYouJi
{
  [Event(SceneType.Map)]
  public class EquipQuitAttackEventHandler : AEvent<Scene, UserQuitAttackEvent>
  {
    protected override async ETTask Run(Scene scene, UserQuitAttackEvent equipQuitEvent)
    {
      User user = equipQuitEvent.user;
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      // 用户不死亡，需要治疗用户
      if (attackComponent.LiveState == LiveStateEnum.DEAD)
      {
        equipComponent.RecoverUserBeforeQuitAttack();
        equipComponent.AutoSubUseCnt();
      }
      await ETTask.CompletedTask;
    }
  }
}