using System.Net;

namespace MaoYouJi
{
  [Invoke((long)SceneType.Social)]
  public class FiberInit_Social : AInvokeHandler<FiberInit, ETTask>
  {
    public override async ETTask Handle(FiberInit fiberInit)
    {
      Scene root = fiberInit.Fiber.Root;
      root.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.UnOrderedMessage);
      root.AddComponent<TimerComponent>();
      root.AddComponent<CoroutineLockComponent>();
      root.AddComponent<ProcessInnerSender>();
      root.AddComponent<MessageSender>();
      root.AddComponent<DBManagerComponent>();
      root.AddComponent<FiberUsersComponent>();
      root.AddComponent<RelationManageComponent>();
      root.AddComponent<TeamManageComponent>();
      StartSceneConfig startSceneConfig = StartSceneConfigCategory.Instance.Get(root.Fiber.Id);
      root.AddComponent<NetComponent, IPEndPoint, NetworkProtocol>(startSceneConfig.InnerIPPort, NetworkProtocol.UDP);

      await ETTask.CompletedTask;
    }
  }
}