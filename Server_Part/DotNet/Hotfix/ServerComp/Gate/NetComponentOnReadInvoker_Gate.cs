﻿using System;

namespace MaoYouJi
{
  [Invoke((long)SceneType.Gate)]
  public class NetComponentOnReadInvoker_Gate : AInvokeHandler<NetComponentOnRead>
  {
    public override void Handle(NetComponentOnRead args)
    {
      HandleAsync(args).Coroutine();
    }

    private async ETTask HandleAsync(NetComponentOnRead args)
    {
      Session session = args.Session;
      object message = args.Message;
      Scene root = args.Session.Root();
      if (message is MaoYouMessage maoYouMessage)
      {
        if (maoYouMessage.RequestId == 0)
          maoYouMessage.RequestId = IdGenerater.Instance.GenerateRequestId();
        MaoAsyncLocal.RequestId = maoYouMessage.RequestId;
      }
      else
      {
        MaoAsyncLocal.RequestId = 0;
      }
      User user = null;
      if (message is not ISessionMessage)
      {
        long userId = session.GetComponent<SessionPlayerComponent>().Player.Id;
        user = GlobalInfoCache.Instance.GetOnlineUser(userId);
        if (user == null)
        {
          ETLog.Warning($"用户不在缓存中: {userId}");
          if (message is IResponse)
          {
            IResponse notFoundUser = MessageHelper.CreateResponse(message.GetType(), 0, "您已下线");
            session.Send(notFoundUser);
          }
          session.Send(new ServerReLogInMsg());
          return;
        }
      }
      // 根据消息接口判断是不是Actor消息，不同的接口做不同的处理,比如需要转发给Chat Scene，可以做一个IChatMessage接口
      switch (message)
      {
        case ISessionMessage:
          {
            MessageSessionDispatcher.Instance.Handle(session, message);
            break;
          }
        case ILocationMessage actorLocationMessage:
          {
            actorLocationMessage.UserId = user.Id;
            ActorId nowActor = user.GetComponent<UserActorComponent>().GetNowActorId(actorLocationMessage is IAttackMessage);
            if (nowActor == default)
            {
              ETLog.Error($"用户没有Actor: {user.Id}");
              session.Send(new ServerShowToastMsg()
              {
                message = "服务器内部错误，请反馈给管理员"
              });
              return;
            }
            root.GetComponent<MessageSender>().Send(nowActor, actorLocationMessage);
            break;
          }
        case ILocationRequest actorLocationRequest: // gate session收到actor rpc消息，先向actor 发送rpc请求，再将请求结果返回客户端
          {
            actorLocationRequest.UserId = user.Id;
            int rpcId = actorLocationRequest.RpcId; // 这里要保存客户端的rpcId
            ActorId nowActor = user.GetComponent<UserActorComponent>().GetNowActorId(actorLocationRequest is IAttackRequest);
            if (nowActor == default)
            {
              ETLog.Error($"用户没有Actor: {user.Id}");
              IResponse notFoundUser = MessageHelper.CreateResponse(actorLocationRequest.GetType(), rpcId, "服务器内部错误，请反馈给管理员");
              session.Send(notFoundUser);
              return;
            }
            long instanceId = session.InstanceId;
            IResponse iResponse = await root.GetComponent<MessageSender>().Call(nowActor, actorLocationRequest);
            iResponse.RpcId = rpcId;
            // session可能已经断开了，所以这里需要判断
            if (session.InstanceId == instanceId)
            {
              if (iResponse is MaoYouOutMessage maoYouOutMessage)
              {
                if (iResponse.Error == ErrorCore.ERR_NotFoundActor)
                {
                  ETLog.Error($"用户Actor不存在: {user.Id} {nowActor.Fiber} {actorLocationRequest.GetType().FullName}");
                  maoYouOutMessage.showMessage = "该场战斗已结束";
                }
                else if (iResponse.Error != 0 && iResponse.Error != ErrorCore.ERR_Show_Msg)
                {
                  maoYouOutMessage.showMessage = "服务器内部错误，请反馈给管理员";
                }
              }
              session.Send(iResponse);
            }
            break;
          }
        case IAttackMessage iAttackMessage:
          {
            iAttackMessage.UserId = user.Id;
            ActorId nowActor = user.GetComponent<UserActorComponent>().GetAttackActorId();
            if (nowActor == default)
            {
              session.Send(new ServerShowToastMsg()
              {
                message = "您不在战斗中"
              });
              return;
            }
            root.GetComponent<MessageSender>().Send(nowActor, iAttackMessage);
            break;
          }
        case IAttackRequest iAttackRequest:
          {
            int rpcId = iAttackRequest.RpcId; // 这里要保存客户端的rpcId
            iAttackRequest.UserId = user.Id;
            ActorId nowActor = user.GetComponent<UserActorComponent>().GetAttackActorId();
            if (nowActor == default)
            {
              ETLog.Warning($"用户不在战斗中: {user.Id} {iAttackRequest.GetType().FullName}");
              IResponse notFoundUser = MessageHelper.CreateResponse(iAttackRequest.GetType(), rpcId, "您不在战斗中");
              session.Send(notFoundUser);
              return;
            }
            long instanceId = session.InstanceId;
            IResponse iResponse = await root.GetComponent<MessageSender>().Call(nowActor, iAttackRequest);
            iResponse.RpcId = rpcId;
            // session可能已经断开了，所以这里需要判断
            if (session.InstanceId == instanceId)
            {
              if (iResponse is MaoYouOutMessage maoYouOutMessage)
              {
                if (iResponse.Error == ErrorCore.ERR_NotFoundActor)
                {
                  ETLog.Warning($"用户Actor不存在: {user.Id} {nowActor.Fiber} {iAttackRequest.GetType().FullName}");
                  maoYouOutMessage.showMessage = "该场战斗已结束";
                }
                else if (iResponse.Error != 0 && iResponse.Error != ErrorCore.ERR_Show_Msg)
                {
                  maoYouOutMessage.showMessage = "服务器内部错误，请反馈给管理员";
                }
              }
              session.Send(iResponse);
            }
            break;
          }
        case ISocialMessage iSocialMessage:
          {
            iSocialMessage.UserId = user.Id;
            StartSceneConfig socialSceneConfig = StartSceneConfigCategory.Instance.GetSceneConfigByType(root.Zone(), SceneType.Social);
            if (socialSceneConfig == null)
            {
              ETLog.Error($"社交场景不存在: {root.Zone()}");
              session.Send(new ServerShowToastMsg()
              {
                message = "服务器内部错误，请反馈给管理员"
              });
              return;
            }
            root.GetComponent<MessageSender>().Send(socialSceneConfig.ActorId, iSocialMessage);
            break;
          }
        case ISocialRequest iSocialRequest:
          {
            iSocialRequest.UserId = user.Id;
            int rpcId = iSocialRequest.RpcId; // 这里要保存客户端的rpcId
            StartSceneConfig socialSceneConfig = StartSceneConfigCategory.Instance.GetSceneConfigByType(root.Zone(), SceneType.Social);
            if (socialSceneConfig == null)
            {
              ETLog.Error($"社交场景不存在: {root.Zone()}");
              IResponse notFoundUser = MessageHelper.CreateResponse(iSocialRequest.GetType(), rpcId, "服务器内部错误，请反馈给管理员");
              session.Send(notFoundUser);
              return;
            }
            long instanceId = session.InstanceId;
            IResponse iResponse = await root.GetComponent<MessageSender>().Call(socialSceneConfig.ActorId, iSocialRequest);
            iResponse.RpcId = rpcId;
            // session可能已经断开了，所以这里需要判断
            if (session.InstanceId == instanceId)
            {
              session.Send(iResponse);
            }
            break;
          }
        case IGlobalMessage iGlobalMessage:
          {
            iGlobalMessage.UserId = user.Id;
            root.GetComponent<MessageSender>().Send(GlobalActorInfo.Instance.GlobalActorId, iGlobalMessage);
            break;
          }
        case IGlobalRequest iGlobalRequest:
          {
            iGlobalRequest.UserId = user.Id;
            int rpcId = iGlobalRequest.RpcId; // 这里要保存客户端的rpcId
            long instanceId = session.InstanceId;
            IResponse iResponse = await root.GetComponent<MessageSender>().Call(GlobalActorInfo.Instance.GlobalActorId, iGlobalRequest);
            iResponse.RpcId = rpcId;
            // session可能已经断开了，所以这里需要判断
            if (session.InstanceId == instanceId)
            {
              session.Send(iResponse);
            }
            break;
          }
        case IRequest actorRequest:  // 分发IActorRequest消息，目前没有用到，需要的自己添加
          {
            break;
          }
        case IMessage actorMessage:  // 分发IActorMessage消息，目前没有用到，需要的自己添加
          {
            break;
          }

        default:
          {
            throw new Exception($"not found handler: {message}");
          }
      }
    }
  }
}