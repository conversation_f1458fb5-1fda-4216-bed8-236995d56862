using System;
using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Driver;


namespace MaoYouJi
{
  [MessageSessionHandler(SceneType.Gate)]
  [FriendOf(typeof(Account))]
  public class C2G_CreateRoleHandler : MessageSessionHandler<CreateRoleReq, CreateRoleResp>
  {
    protected override async ETTask Run(Session session, CreateRoleReq request, CreateRoleResp response)
    {
      Scene root = session.Root();
      long netAccountId = request.accountId;
      Account account = GlobalInfoCache.Instance.GetOnlineAccount(netAccountId);
      // 根据网关账户ID获取本地账号
      DBComponent dBComponent = root.GetComponent<DBManagerComponent>().GetMyZoneDB();
      if (account == null)
      {
        ETLog.Warning($"未找到对应的账户: {netAccountId}");
        GateAccountsComponent gateAccountsComponent = root.GetComponent<GateAccountsComponent>();
        account = await dBComponent.QueryClass(new FilterDefinitionBuilder<Account>().Eq(a => a.netAccountId, netAccountId));
        if (account == null)
        {
          ETLog.Error($"未找到对应的账户: {netAccountId}");
          response.Error = ErrorCore.ERR_Show_Msg;
          response.showMessage = "未找到对应的账户";
          return;
        }
        else
        {
          gateAccountsComponent.AddAccount(netAccountId, account);
        }
      }
      if (account.accessKey != request.accessKey)
      {
        ETLog.Error($"账户密钥不正确: {netAccountId} {account.accessKey} {request.accessKey}");
        response.Error = ErrorCore.ERR_Show_Msg;
        response.showMessage = "账户密钥不正确请重新登录";
        return;
      }
      if (account.roleList.Count >= 5)
      {
        ETLog.Warning($"角色数量已满: {account.roleList.Count} {netAccountId}");
        response.Error = ErrorCore.ERR_Show_Msg;
        response.showMessage = "角色数量已满";
        return;
      }
      FiberUsersComponent gateUsersComponent = root.GetComponent<FiberUsersComponent>();
      if (!gateUsersComponent.CheckNickName(request.nickname, out string msg))
      {
        response.Error = ErrorCore.ERR_Show_Msg;
        response.showMessage = msg;
        return;
      }
      if (request.defaultSkin == SkinIdEnum.None)
      {
        response.Error = ErrorCore.ERR_Show_Msg;
        response.showMessage = "请选择默认皮肤";
        return;
      }
      if (request.job == BaseJob.None)
      {
        response.Error = ErrorCore.ERR_Show_Msg;
        response.showMessage = "请选择职业";
        return;
      }
      User user = await gateUsersComponent.CreateUser(netAccountId, request.nickname, request.job, request.defaultSkin);
      account.roleList.Add(user.Id);
      await dBComponent.Save(account);
      response.accountRoles = await account.GetRoleInfoListEmbedded();
    }
  }
}