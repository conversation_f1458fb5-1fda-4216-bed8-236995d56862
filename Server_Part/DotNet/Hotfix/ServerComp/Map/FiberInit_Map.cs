using System.Net;

namespace MaoYouJi
{
  [Invoke((long)SceneType.Map)]
  public class FiberInit_Map : AInvokeHandler<FiberInit, ETTask>
  {
    public override async ETTask Handle(FiberInit fiberInit)
    {
      Scene root = fiberInit.Fiber.Root;
      root.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.UnOrderedMessage);
      root.AddComponent<TimerComponent>();
      root.AddComponent<CoroutineLockComponent>();
      root.AddComponent<ChatManageComp>();
      root.AddComponent<ProcessInnerSender>();
      root.AddComponent<MessageSender>();
      root.AddComponent<DBManagerComponent>();
      root.AddComponent<TiaoSaoManageComp>();
      root.AddComponent<FiberUsersComponent>();
      root.AddComponent<FiberMapManage>();
      root.AddComponent<TeamManageComponent>();
      StartSceneConfig startSceneConfig = StartSceneConfigCategory.Instance.Get(root.Fiber.Id);
      root.AddComponent<NetComponent, IPEndPoint, NetworkProtocol>(startSceneConfig.InnerIPPort, NetworkProtocol.UDP);

      await ETTask.CompletedTask;
    }
  }
}