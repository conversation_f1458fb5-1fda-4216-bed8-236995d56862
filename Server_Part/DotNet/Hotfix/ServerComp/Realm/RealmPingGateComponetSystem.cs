using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(RealmPingGateComponet))]
  [FriendOf(typeof(RealmPingGateComponet))]
  public static partial class RealmPingGateComponetSystem
  {
    [EntitySystem]
    private static void Awake(this RealmPingGateComponet self)
    {
      self.logicServerStatus = new();
      self.PingAsync().Coroutine();
    }

    [EntitySystem]
    private static void Destroy(this RealmPingGateComponet self)
    {
    }

    public static List<ServerData> GetServerList(this RealmPingGateComponet self)
    {
      return self.logicServerStatus.Values.ToList();
    }

    public static ServerData GetServerData(this RealmPingGateComponet self, int zone)
    {
      self.logicServerStatus.TryGetValue(zone, out ServerData serverData);
      return serverData;
    }

    private static async ETTask PingAsync(this RealmPingGateComponet self)
    {
      Scene root = self.Root();
      long instanceId = self.InstanceId;
      Fiber fiber = self.Fiber();

      while (true)
      {
        await fiber.Root.GetComponent<TimerComponent>().WaitAsync(2000);
        if (self.InstanceId != instanceId)
        {
          return;
        }
        long time1 = TimeInfo.Instance.ClientNow();
        List<StartZoneConfig> allZone = StartZoneConfigCategory.Instance.GetAll().Values.ToList();
        foreach (int zoneId in self.logicServerStatus.Keys)
        {
          if (!allZone.Any(z => z.Id == zoneId))
          {
            self.logicServerStatus.Remove(zoneId);
          }
        }
        for (int i = 1; i < allZone.Count; ++i)
        {
          // 重试多次
          int retryCnt = 3;
          while (retryCnt-- > 0)
          {
            StartZoneConfig oneZoneConfig = allZone[i];
            ServerData serverData = new()
            {
              zone = oneZoneConfig.Id,
              name = oneZoneConfig.ServerName,
              maxOnlineNum = oneZoneConfig.MaxOnlineNum
            };
            List<StartSceneConfig> allGates = StartSceneConfigCategory.Instance.Gates[oneZoneConfig.Id];
            if (allGates == null || allGates.Count <= 0)
            {
              serverData.isEnable = false;
              self.logicServerStatus[oneZoneConfig.Id] = serverData;
              break;
            }
            StartSceneConfig oneGate = allGates[(int)RandomGenerator.RandUInt32() % allGates.Count];
            R2G_Ping c2GPing = R2G_Ping.Create(true);
            c2GPing.RequestId = IdGenerater.Instance.GenerateRequestId();
            MaoAsyncLocal.RequestId = c2GPing.RequestId;
            G2R_Ping response = (G2R_Ping)await self.Fiber().Root.GetComponent<MessageSender>().Call(oneGate.ActorId, c2GPing);
            if (self.InstanceId != instanceId)
            {
              serverData.isEnable = false;
              self.logicServerStatus[oneZoneConfig.Id] = serverData;
              return;
            }
            if (response.Error != 0 && retryCnt == 0)
            {
              serverData.isEnable = false;
              self.logicServerStatus[oneZoneConfig.Id] = serverData;
              break;
            }
            else
            {
              serverData.isEnable = true;
              self.logicServerStatus[oneZoneConfig.Id] = serverData;
              break;
            }
          }
        }
      }
    }
  }
}