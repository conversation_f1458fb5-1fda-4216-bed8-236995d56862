using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Bson;
using MongoDB.Driver;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(ChatManageComp))]
  public static partial class ChatManageCompSys
  {
    [EntitySystem]
    public static void Awake(this ChatManageComp self)
    {
    }

    // 系统发送邮件
    public static async ETTask SendMail(this ChatManageComp self, long userId, string title, string content, long coinNum, long catBeanNum, long catEyeNum, List<Thing> things)
    {
      MailInfo mailInfo = new MailInfo();
      mailInfo.userId = userId;
      mailInfo.title = title;
      mailInfo.content = content;
      mailInfo.createTime = TimeInfo.Instance.ServerNow();
      mailInfo.coinNum = coinNum;
      mailInfo.catBeanNum = catBeanNum;
      mailInfo.catEyeNum = catEyeNum;
      if (things != null && things.Count > 0)
      {
        mailInfo.things = things;
      }
      mailInfo.senderInfo = null;
      // 如果有需要领取的，则设置为未领取
      if (mailInfo.coinNum > 0 || mailInfo.catBeanNum > 0 || mailInfo.catEyeNum > 0
          || (mailInfo.things != null && mailInfo.things.Count > 0))
      {
        mailInfo.hasThing = true;
        mailInfo.isGet = false;
      }
      else
      {
        mailInfo.hasThing = false;
        mailInfo.isGet = true;
      }
      ChatProSystem.SendChatToUser(userId, "您收到一封新邮件", ChatType.Sys_Chat);
      // outList.add(new UpdateMenuReddotOut(MenuReddotEnum.Menu_Mail_Reddot, true));
      // 设置为未读
      mailInfo.isRead = false;
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      // 保存邮件
      await dbComponent.GetCollection<MailInfo>().InsertOneAsync(mailInfo);
    }

    public static async ETTask<long> CountMail(this User self)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      return await dbComponent.GetCollection<MailInfo>().CountDocumentsAsync(x => x.userId == self.Id);
    }

    public static async ETTask<List<MailInfo>> GetMailList(this User self)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var filter = Builders<MailInfo>.Filter.Eq(f => f.userId, self.Id);
      List<MailInfo> mailList = await dbComponent.GetCollection<MailInfo>().Find(filter).SortByDescending(x => x.createTime).ToListAsync();
      return mailList;
    }

    // 读取邮件
    public static async ETTask ReadMail(this User self, List<ObjectId> mailIds)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var filterBuilder = Builders<MailInfo>.Filter;
      var filter = filterBuilder.Eq(f => f.userId, self.Id) & filterBuilder.In(f => f.id, mailIds) & filterBuilder.Eq(f => f.isGet, true) & filterBuilder.Eq(f => f.isRead, false);
      var update = Builders<MailInfo>.Update.Set(f => f.isRead, true);
      await dbComponent.GetCollection<MailInfo>().UpdateManyAsync(filter, update);
    }

    // 删除邮件
    public static async ETTask DelMails(this User self, List<ObjectId> mailIds)
    {
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var filterBuilder = Builders<MailInfo>.Filter;
      var filter = filterBuilder.Eq(f => f.userId, self.Id) & filterBuilder.In(f => f.id, mailIds) & filterBuilder.Eq(f => f.isRead, true) & filterBuilder.Eq(f => f.isGet, true);
      await dbComponent.GetCollection<MailInfo>().DeleteManyAsync(filter);
    }

    // 获取邮箱中的附件列表
    public static async ETTask<LogicRet> GetMailThings(this User self, List<ObjectId> mailIds)
    {
      BagComponent bagComponent = self.GetComponent<BagComponent>();
      DBComponent dbComponent = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB();
      var filterBuilder = Builders<MailInfo>.Filter;
      var filter = filterBuilder.Eq(f => f.userId, self.Id) & filterBuilder.In(f => f.id, mailIds) & filterBuilder.Eq(f => f.hasThing, true) & filterBuilder.Eq(f => f.isGet, false);
      using (await self.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DB, self.Id))
      {
        List<MailInfo> mailList = await dbComponent.GetCollection<MailInfo>().Find(filter).ToListAsync();
        int needCapNum = 0;
        foreach (var mail in mailList)
        {
          if (mail.hasThing && mail.things != null && mail.things.Count > 0)
          {
            needCapNum += mail.things.Count;
          }
        }
        if (!bagComponent.HasEnoughCapacity(needCapNum))
        {
          return LogicRet.Failed("背包容量不足");
        }
        foreach (var mail in mailList)
        {
          if (mail.coinNum > 0 || mail.catBeanNum > 0 || mail.catEyeNum > 0)
          {
            bagComponent.AddAllCoinWithSend(mail.coinNum, mail.catBeanNum, mail.catEyeNum);
          }
          if (mail.things != null && mail.things.Count > 0)
          {
            bagComponent.GiveThingList(mail.things);
          }
        }
        // 更新邮件状态
        var update = Builders<MailInfo>.Update.Set(f => f.isGet, true).Set(f => f.isRead, true);
        await dbComponent.GetCollection<MailInfo>().UpdateManyAsync(filter, update);
        return LogicRet.Success;
      }
    }
  }
}