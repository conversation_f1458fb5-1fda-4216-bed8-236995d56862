namespace MaoYouJi
{
  public static class FightChatSystem
  {
    public static void SendMessageToAllFightUser(this AttackInCache self, MaoYouMessage message)
    {
      foreach (AttackComponent fight in self.FightList.Values)
      {
        if (fight.fightInfo.liveType == LiveType.ROLE)
        {
          User user = fight.GetParent<User>();
          user?.SendMessage(message);
        }
      }
    }

    public static void SendDefendInfo(this AttackInCache self, AttackComponent src, AttackComponent target, string srcSkill, string defendSrc, AttackType attackType)
    {
      ServerFightChatMsg fightChatMsg = new ServerFightChatMsg();
      fightChatMsg.type = FightChatType.Defend_Dmg;
      fightChatMsg.src = src.fightInfo;
      fightChatMsg.target = target.fightInfo;
      fightChatMsg.dmgSrc = srcSkill;
      fightChatMsg.defendSrc = defendSrc;
      fightChatMsg.attackType = (int)attackType;
      self.SendMessageToAllFightUser(fightChatMsg);
    }

    public static void SendCureInfo(this AttackInCache self, long cureHp, long cureSp, string srcSkill, CureType cureType, AttackComponent src, AttackComponent target)
    {
      ServerFightChatMsg fightChatMsg = new ServerFightChatMsg();
      fightChatMsg.type = FightChatType.Cure_Target;
      fightChatMsg.src = src.fightInfo;
      fightChatMsg.dmgSrc = srcSkill;
      fightChatMsg.target = target.fightInfo;
      fightChatMsg.cureHp = cureHp;
      fightChatMsg.cureSp = cureSp;
      fightChatMsg.attackType = (int)cureType;
      self.SendMessageToAllFightUser(fightChatMsg);
    }

    public static void SendDmgInfo(this AttackInCache self, DmgInfo dmgInfo, string srcSkill, AttackType attackType)
    {
      ServerFightChatMsg fightChatMsg = new ServerFightChatMsg();
      fightChatMsg.attackType = (int)attackType;
      fightChatMsg.type = FightChatType.Dmg_Target;
      fightChatMsg.src = dmgInfo.src;
      fightChatMsg.defendNum = dmgInfo.defendNum;
      fightChatMsg.target = dmgInfo.target;
      fightChatMsg.dmgNum = dmgInfo.dmgNum;
      fightChatMsg.realNum = dmgInfo.realNum;
      fightChatMsg.dmgType = dmgInfo.dmgType;
      fightChatMsg.dmgSrc = srcSkill;
      fightChatMsg.isHit = dmgInfo.isHit;
      fightChatMsg.hasCrit = dmgInfo.hasCrit;
      self.SendMessageToAllFightUser(fightChatMsg);
    }

    public static void SendEscapeInfo(this AttackInCache self, AttackComponent src, bool success)
    {
      ServerFightChatMsg fightChatMsg = new ServerFightChatMsg();
      fightChatMsg.type = FightChatType.Escape;
      fightChatMsg.src = src.fightInfo;
      fightChatMsg.attackType = success ? 1 : 0;
      self.SendMessageToAllFightUser(fightChatMsg);
    }

    public static void SendKillInfo(this AttackInCache self, FightInfo src, FightInfo target, MapNode mapNode)
    {
      ServerFightChatMsg fightChatMsg = new()
      {
        type = FightChatType.Kill_Role,
        src = src,
        target = target
      };
      mapNode.SendMessageToMapUser(fightChatMsg);
    }

    public static void SendQuitInfo(this AttackInCache self, AttackComponent src)
    {
      ServerFightChatMsg fightChatMsg = new()
      {
        type = FightChatType.Quit_Fight,
        src = src.fightInfo,
        attackId = self.Id
      };
      self.SendMessageToAllFightUser(fightChatMsg);
    }

    public static void SendChangeTarget(this AttackInCache self, AttackComponent src, FightInfo target)
    {
      // 创建发送战斗聊天信息的输出对象
      ServerFightChatMsg fightChatMsg = new()
      {
        type = FightChatType.Change_Target,
        src = src.fightInfo,
        target = target
      };
      self.SendMessageToAllFightUser(fightChatMsg);
    }

    public static void SendBreakSong(this AttackInCache self, AttackComponent src, Skill skill)
    {
      // 创建发送战斗聊天信息的输出对象
      ServerFightChatMsg fightChatMsg = new()
      {
        type = FightChatType.Break_Song,
        src = src.fightInfo,
        dmgSrc = skill.name
      };
      self.SendMessageToAllFightUser(fightChatMsg);
    }

    public static void SendEndSong(this AttackInCache self, AttackComponent src, Skill skill)
    {
      // 创建发送战斗聊天信息的输出对象
      ServerFightChatMsg fightChatMsg = new()
      {
        type = FightChatType.End_Song,
        src = src.fightInfo,
        dmgSrc = skill.name
      };
      self.SendMessageToAllFightUser(fightChatMsg);
    }

    public static void SendStartSong(this AttackInCache self, AttackComponent src, Skill skill, long songTime)
    {
      // 创建发送战斗聊天信息的输出对象
      ServerFightChatMsg fightChatMsg = new()
      {
        type = FightChatType.Start_Song,
        src = src.fightInfo,
        dmgSrc = skill.name,
        songTime = songTime
      };
      self.SendMessageToAllFightUser(fightChatMsg);
    }
  }
}