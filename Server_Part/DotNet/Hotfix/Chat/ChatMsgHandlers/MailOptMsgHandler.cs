using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  public class GetMailListMsgHandler : MessageLocationHandler<MapNode, GetMailListReq, GetMailListResp>
  {
    protected override async ETTask Run(MapNode nowMap, GetMailListReq req, GetMailListResp resp)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        resp.SetError(logicRet.Message);
        return;
      }
      List<MailInfo> mailList = await user.GetMailList();
      resp.mailList = mailList;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class OptMailMsgHandler : MessageLocationHandler<MapNode, OptMailReq, OptMailResp>
  {
    protected override async ETTask Run(MapNode nowMap, OptMailReq req, OptMailResp resp)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user, false, true);
      if (!logicRet.IsSuccess)
      {
        resp.SetError(logicRet.Message);
        return;
      }
      if (user.activityName == ActNameEnum.Da_TaoSha)
      {
        resp.SetError("大逃杀活动中不能操作邮件");
        return;
      }
      if (req.optType == MailOptType.GetThing)
      {
        logicRet = await user.GetMailThings(req.mailIds);
        if (!logicRet.IsSuccess)
        {
          resp.SetError(logicRet.Message);
          return;
        }
        else
        {
          user.SendToast("领取附件成功，请查看背包！");
        }
      }
      else if (req.optType == MailOptType.Read)
      {
        await user.ReadMail(req.mailIds);
      }
      else if (req.optType == MailOptType.Delete)
      {
        await user.DelMails(req.mailIds);
      }
      // 红点
      // if (req.mailIds.Count > 0)
      // {
      //   user.SendMessage(new UpdateMenuReddotOut(MenuReddotEnum.Menu_Mail_Reddot,
      //       chatProc.showMailReddot(user)));
      // }
    }
  }
}