using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  public class UserChatMsgHandler : MessageLocationHandler<MapNode, SendChatReq, SendChatResp>
  {
    protected override async ETTask Run(MapNode nowMap, SendChatReq req, SendChatResp resp)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        resp.SetError(logicRet.Message);
        return;
      }
      UserTimeInfo userTimeInfo = user.GetComponent<UserTimeInfo>();
      ServerSendChatMsg serverSendChatMsg = new ServerSendChatMsg();
      serverSendChatMsg.chatType = req.chatType;
      serverSendChatMsg.content = req.content;
      serverSendChatMsg.fightInfo = user.GetComponent<AttackComponent>().fightInfo;
      serverSendChatMsg.things = req.things;
      long now = TimeInfo.Instance.ServerNow();
      if (now - userTimeInfo.lastChatTime < 1000 * 1)
      {
        resp.SetError("聊天时间间隔小于1秒");
        return;
      }
      userTimeInfo.lastChatTime = now;
      if (req.chatType == ChatType.World_Chat)
      {
        if (now - userTimeInfo.lastGlobalChatTime < 1000 * 3)
        {
          resp.SetError("世界聊天时间间隔小于3秒");
          return;
        }
        userTimeInfo.lastGlobalChatTime = now;
        ChatProSystem.SendMessageToAllUser(serverSendChatMsg);
      }
      else if (req.chatType == ChatType.Local_Chat)
      {
        ChatProSystem.SendChatToLocalUsers(nowMap.mapName, serverSendChatMsg);
      }
      else if (req.chatType == ChatType.User_Chat)
      {
        List<long> userIds = new();
        if (req.targetId == 0)
        {
          resp.SetError("玩家聊天ID不能为空");
          return;
        }
        else
        {
          if (req.targetId == user.Id)
          {
            resp.SetError("不能和自己聊天");
            return;
          }
          User targetUser = GlobalInfoCache.Instance.GetOnlineUser(req.targetId);
          if (targetUser == null)
          {
            resp.SetError("玩家不在线");
            return;
          }
          if (targetUser.offlineState != OfflineStateEnum.ONLINE)
          {
            resp.SetError("玩家不在线");
            return;
          }
          serverSendChatMsg.targetFightInfo = targetUser.GetComponent<AttackComponent>().fightInfo;
          userIds.Add(req.targetId);
          userIds.Add(user.Id);
          ChatProSystem.SendMessageToUsers(userIds, serverSendChatMsg);
        }
      }
      else if (req.chatType == ChatType.Team_Chat)
      {
        if (user.teamInfo == null)
        {
          resp.SetError("玩家没有加入队伍");
          return;
        }
        if (!GlobalInfoCache.Instance.allTeamCache.TryGetValue(user.teamInfo.teamId, out TeamInfo teamInfo))
        {
          resp.SetError("队伍不存在");
          return;
        }
        teamInfo.SendMessageToAllMember(serverSendChatMsg);
      }
      else if (req.chatType == ChatType.Comm_Chat)
      {
        // chatProc.sendMessageToPrivateUsers(out, in.expectUser);
      }
      await ETTask.CompletedTask;
    }
  }
}