using System.Collections.Generic;

namespace MaoYouJi
{
  [FriendOf(typeof(User))]
  [FriendOf(typeof(UserActorComponent))]
  public static class UserChatSystem
  {
    public static void SendMessage(this User self, params MaoYouMessage[] messages)
    {
      UserActorComponent userActorComponent = self.GetComponent<UserActorComponent>();
      if (userActorComponent == null)
      {
        return;
      }
      ActorId userSessionActorId = userActorComponent.UserSessionActorId;
      if (userSessionActorId == default)
      {
        return;
      }
      MessageSender messageSender = self.Root().GetComponent<MessageSender>();
      foreach (var message in messages)
      {
        messageSender.Send(userSessionActorId, message);
      }
    }

    public static void SendToast(this User self, string message)
    {
      self.SendMessage(new ServerShowToastMsg()
      {
        message = message
      });
    }

    public static void SendChat(this User self, string content, ChatType chatType = ChatType.Sys_Chat)
    {
      self.SendMessage(new ServerSendChatMsg()
      {
        content = content,
        chatType = chatType
      });
    }
  }
}