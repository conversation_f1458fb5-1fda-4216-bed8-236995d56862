using System;
using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(OneTask))]
  [FriendOf(typeof(OneTask))]
  public static partial class OneTaskSystem
  {
    [EntitySystem]
    private static void Awake(this OneTask self, BaseTask myTask)
    {
      TaskComponent taskComponent = self.GetParent<TaskComponent>();
      User user = taskComponent.GetParent<User>();
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      UserDailyCntInfo userDailyCntInfo = user.GetComponent<UserDailyCntInfo>();
      self.TaskId = myTask.idEnum;
      myTask.taskEntityId = self.Id;
      taskComponent.nowTasks.TryAdd(self.TaskId, myTask);
      // 移除物品
      if (myTask.removeThingBeforeList != null)
      {
        bagComponent.RemoveThingsWithSend(myTask.removeThingBeforeList);
      }
      // 给予物品
      if (myTask.taskGiveList != null)
      {
        List<ThingGiveInfo> thingGiveInfos = new();
        foreach (TaskGive taskGive in myTask.taskGiveList)
        {
          thingGiveInfos.Add(new ThingGiveInfo
          {
            thingName = taskGive.thingName,
            ownType = OwnType.PRIVATE,
            num = taskGive.num,
          });
        }
        bagComponent.GiveThing(thingGiveInfos);
      }
      // 消耗货币
      if (myTask.needSubCoinNum > 0)
      {
        bagComponent.AddAllCoinWithSend(-myTask.needSubCoinNum, 0, 0);
      }
      // 检查任务要求
      if (myTask.requireLists != null)
      {
        foreach (TaskRequire taskRequire in myTask.requireLists)
        {
          // 如果任务要求是获得物品，则需要检查背包中是否有足够的物品
          if (taskRequire.requireType == TaskRequireType.Get_Thing_Cond)
          {
            Thing thing = bagComponent.GetThingInBag<Thing>(taskRequire.thingName);
            if (thing == null)
            {
              continue;
            }
            taskRequire.nowNum = Math.Min(thing.num, taskRequire.totalNum);
          }
          else if (taskRequire.requireType == TaskRequireType.Learn_Skill_Cond)
          {
            // 如果是学习技能条件，则需要检查技能是否已经学习
            Skill skill = skillComponent.GetSkill(taskRequire.skillName);
            if (skill == null)
            {
              continue;
            }
            taskRequire.nowNum = Math.Min(skill.level, taskRequire.totalNum);
          }
          else if (taskRequire.requireType == TaskRequireType.Get_Level_Cond)
          {
            // 如果是达到等级条件，则需要检查等级是否达到
            taskRequire.nowNum = (int)Math.Min(attackComponent.level, taskRequire.totalNum);
          }
        }
      }
      if (myTask.taskSubType == TaskSubTypeEnum.BiaoChe_Task)
      {
        userDailyCntInfo.biaoCheCnt++;
      }
      else if (myTask.taskSubType == TaskSubTypeEnum.ShiMing_Task)
      {
        userDailyCntInfo.shiMingCnt++;
      }
      // 添加任务到当前任务列表
      taskComponent.nowTasks.TryAdd(myTask.idEnum, myTask);
      taskComponent.AddTaskRequireMap(myTask);
      user.SendMessage(ServerUpdateTaskInfoMsg.Create(myTask));
      EventSystem.Instance.Publish(user.Scene(), new StartTaskEvent
      {
        user = user,
        task = myTask
      });
    }

    [EntitySystem]
    private static void Destroy(this OneTask self)
    {
      TaskComponent taskComponent = self.GetParent<TaskComponent>();
      User user = taskComponent.GetParent<User>();
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      taskComponent.nowTasks.TryRemove(self.TaskId, out _);
      user.SendMessage(ServerUpdateTaskInfoMsg.CreateRemove(self.TaskId));
      TaskIdEnum taskId = self.TaskId;
      BaseTask task = taskComponent.nowTasks[taskId];
      if (task.requireLists != null)
      {
        foreach (TaskRequire taskRequire in task.requireLists)
        {
          taskComponent.taskRequireMap.TryGetValue(TaskProcSys.getTaskRequireKey(taskRequire), out List<CacheTaskRequireInfo> cacheTaskRequireInfos);
          if (cacheTaskRequireInfos == null)
          {
            continue;
          }
          cacheTaskRequireInfos.RemoveAll(c => c.task.idEnum == taskId);
          if (cacheTaskRequireInfos == null || cacheTaskRequireInfos.Count == 0)
          {
            taskComponent.taskRequireMap.TryRemove(TaskProcSys.getTaskRequireKey(taskRequire), out _);
          }
        }
      }
      HashSet<ThingNameEnum> needRemoveThingNames = new();
      if (task.monDropInfos != null)
      {
        foreach (MonDropInfo monDropInfo in task.monDropInfos)
        {
          needRemoveThingNames.Add(monDropInfo.thingName);
          taskComponent.monDropMap.TryGetValue(monDropInfo.monBaseType, out List<MonDropInfo> monDropInfos);
          if (monDropInfos == null)
          {
            continue;
          }
          monDropInfos.RemoveAll(m => m.taskId == taskId);
          if (monDropInfos == null || monDropInfos.Count == 0)
          {
            taskComponent.monDropMap.TryRemove(monDropInfo.monBaseType, out _);
          }
        }
      }
      if (task.removeThingAfterList != null)
      {
        needRemoveThingNames.UnionWith(task.removeThingAfterList);
      }
      bagComponent.RemoveThingsWithSend(needRemoveThingNames.ToList());
      // 移除给予的物品
      List<ThingGiveInfo> thingGiveInfos = new();
      if (task.taskGiveList != null)
      {
        List<ThingNameEnum> thingNames = new();
        foreach (TaskGive taskGive in task.taskGiveList)
        {
          thingNames.Add(taskGive.thingName);
          thingGiveInfos.Add(new ThingGiveInfo
          {
            thingName = taskGive.thingName,
            ownType = OwnType.PRIVATE,
            num = taskGive.num,
          });
        }
        // 如果任务给予的物品是召唤怪物，则需要移除怪物
        // if (thingGiveInfos.Count > 0)
        // {
        //   Dictionary<ThingNameEnum, Thing> things = bagComponent.GetBaseThings(thingNames);
        //   foreach (Thing thing in things.Values)
        //   {
        //     TaskThing taskThing = (TaskThing)thing;
        //     if (taskThing.callMon != null)
        //     {
        //       List<MonsterInfo> monsterInfos = monsterProc.getMonByOwnUser(user.id);
        //       // 只删除活着的怪物
        //       for (MonsterInfo monsterInfo : monsterInfos)
        //       {
        //         if (monsterInfo.checkLiveState(LiveStateEnum.ALIVE))
        //         {
        //           monsterProc.deleteMonWithMap(Arrays.asList(monsterInfo));
        //         }
        //       }
        //     }
        //   }
        // }
      }
    }
  }
}