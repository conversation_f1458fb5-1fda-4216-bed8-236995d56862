using System;
using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [FriendOf(typeof(TaskComponent))]
  public static partial class TaskComponentSystem
  {
    [EntitySystem]
    private static void Load(this TaskComponent self)
    {
    }

    public static LogicRet IsTaskCanStart(this TaskComponent self, TaskIdEnum taskId)
    {
      BaseTask task = GlobalInfoCache.Instance.GetBaseTask(taskId);
      if (task == null)
      {
        return new LogicRet(false, "任务不存在");
      }
      return self.IsTaskCanStart(task);
    }

    public static LogicRet IsTaskCanStart(this TaskComponent self, BaseTask task)
    {
      User user = self.GetParent<User>();
      if (self.finishedTasks.Contains(task.idEnum))
      {
        return new LogicRet(false, "无法重复进行任务");
      }
      if (self.nowTasks.ContainsKey(task.idEnum))
      {
        return new LogicRet(false, "任务已存在");
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      UserDailyCntInfo userDailyCntInfo = user.GetComponent<UserDailyCntInfo>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      if (task.minLevel != 0 && attackComponent.level < task.minLevel)
      {
        return new LogicRet(false, "等级不足");
      }
      if (task.level != 0 && attackComponent.level < task.level - 5)
      {
        return new LogicRet(false, "等级不足");
      }
      if (task.maxLevel != 0 && attackComponent.level > task.maxLevel)
      {
        return new LogicRet(false, "等级过高");
      }
      if (task.preTask != TaskIdEnum.None)
      {
        if (!self.finishedTasks.Contains(task.preTask))
        {
          return new LogicRet(false, "前置任务未完成");
        }
      }
      if (task.preTasks != null)
      {
        bool hasFinished = false;
        foreach (TaskIdEnum preTask in task.preTasks)
        {
          if (self.finishedTasks.Contains(preTask))
          {
            hasFinished = true;
            break;
          }
        }
        if (!hasFinished)
        {
          return new LogicRet(false, "前置任务未完成");
        }
      }
      if (task.baseJob != BaseJob.None && attackComponent.job != task.baseJob)
      {
        return new LogicRet(false, "职业不匹配");
      }
      if (task.taskSubType == TaskSubTypeEnum.BiaoChe_Task)
      {
        List<BaseTask> preTasks = self.nowTasks.Values.ToList();
        foreach (BaseTask preTask in preTasks)
        {
          if (preTask.taskSubType == TaskSubTypeEnum.BiaoChe_Task)
          {
            return new LogicRet(false, "无法重复接取押镖任务");
          }
        }
        if (userDailyCntInfo.biaoCheCnt >= 10)
        {
          return new LogicRet(false, "押镖次数已达上限");
        }
      }
      else if (task.taskSubType == TaskSubTypeEnum.ShiMing_Task)
      {
        List<BaseTask> preTasks = self.nowTasks.Values.ToList();
        foreach (BaseTask preTask in preTasks)
        {
          if (preTask.taskSubType == TaskSubTypeEnum.ShiMing_Task)
          {
            return new LogicRet(false, "无法重复接取使命任务");
          }
        }
        if (userDailyCntInfo.shiMingCnt >= 20)
        {
          return new LogicRet(false, "使命任务次数已达上限");
        }
      }
      if (task.preCondLists != null)
      {
        foreach (TaskPreCond taskPreCond in task.preCondLists)
        {
          if (taskPreCond.preCondType == TaskPreType.Skill_level_Pre)
          {
            Skill skill = skillComponent.GetSkill(taskPreCond.skillName);
            if (skill == null || skill.level < taskPreCond.preCondNum)
            {
              return new LogicRet(false, skill.name + "不足" + taskPreCond.preCondNum + "级");
            }
          }
          else if (taskPreCond.preCondType == TaskPreType.Relation_level_Pre)
          {
            RelationInfo relationInfo = relationComponent.GetRelationInfo(taskPreCond.relationType);
            if (relationInfo == null || relationInfo.level < taskPreCond.preCondNum)
            {
              return new LogicRet(false, EnumDescriptionCache.GetDescription(taskPreCond.relationType) + "不足");
            }
          }
        }
      }
      if (task.needSubCoinNum > 0)
      {
        if (bagComponent.coin < task.needSubCoinNum)
        {
          return new LogicRet(false, "背包中的金币不足");
        }
      }
      if (self.nowTasks.Count >= 15)
      {
        return new LogicRet(false, "进行中任务已达上限15个");
      }
      return new LogicRet(true, "任务可接受");
    }

    public static void AddTaskRequireMap(this TaskComponent self, BaseTask task)
    {
      if (task.requireLists != null)
      {
        foreach (TaskRequire taskRequire in task.requireLists)
        {
          if (taskRequire.nowNum < taskRequire.totalNum)
          {
            self.taskRequireMap.TryAdd(TaskProcSys.getTaskRequireKey(taskRequire), new List<CacheTaskRequireInfo>());
            self.taskRequireMap[TaskProcSys.getTaskRequireKey(taskRequire)].Add(new CacheTaskRequireInfo
            {
              taskRequire = taskRequire,
              task = task
            });
          }
        }
      }
      if (task.monDropInfos != null)
      {
        foreach (MonDropInfo monDropInfo in task.monDropInfos)
        {
          monDropInfo.taskId = task.idEnum;
          self.monDropMap.TryAdd(monDropInfo.monBaseType, new List<MonDropInfo>());
          self.monDropMap[monDropInfo.monBaseType].Add(monDropInfo);
        }
      }
    }

    private static string GetTaskProgress(CacheTaskRequireInfo cacheTaskRequireInfo)
    {
      string taskName = "任务进度【" + cacheTaskRequireInfo.task.name + "】";
      if (cacheTaskRequireInfo.taskRequire.simpleName != null)
      {
        taskName += cacheTaskRequireInfo.taskRequire.simpleName;
      }
      return taskName + "：" + cacheTaskRequireInfo.taskRequire.nowNum + "/" + cacheTaskRequireInfo.taskRequire.totalNum;
    }

    // 填充任务进度
    public static void FillTaskRequire(this TaskComponent self, TaskRequireType taskRequireType, string taskRequireValue, int num, TaskIdEnum taskId = TaskIdEnum.None)
    {
      User user = self.GetParent<User>();
      string key = taskRequireType.ToString() + "_" + taskRequireValue;
      if (taskRequireType == TaskRequireType.Get_Level_Cond)
      {
        key = taskRequireType.ToString();
      }
      self.taskRequireMap.TryGetValue(key, out List<CacheTaskRequireInfo> cacheTaskRequireInfos);
      if (cacheTaskRequireInfos == null || cacheTaskRequireInfos.Count == 0)
      {
        return;
      }
      List<BaseTask> tasks = new();
      List<int> needRemoveIdxs = new();
      int idx = 0;
      // 填充任务进度
      foreach (CacheTaskRequireInfo cacheTaskRequireInfo in cacheTaskRequireInfos)
      {
        if (taskId != TaskIdEnum.None && cacheTaskRequireInfo.task.idEnum != taskId)
        {
          continue;
        }
        if (cacheTaskRequireInfo.taskRequire.nowNum < cacheTaskRequireInfo.taskRequire.totalNum)
        {
          if (cacheTaskRequireInfo.taskRequire.requireType != TaskRequireType.Learn_Skill_Cond
              && cacheTaskRequireInfo.taskRequire.requireType != TaskRequireType.Get_Level_Cond)
          {
            cacheTaskRequireInfo.taskRequire.nowNum = Math.Min(cacheTaskRequireInfo.taskRequire.nowNum + num,
                cacheTaskRequireInfo.taskRequire.totalNum);
            user.SendChat(GetTaskProgress(cacheTaskRequireInfo));
            if (cacheTaskRequireInfo.taskRequire.nowNum >= cacheTaskRequireInfo.taskRequire.totalNum)
            {
              needRemoveIdxs.Add(idx);
            }
            tasks.Add(cacheTaskRequireInfo.task);
          }
          else
          {
            cacheTaskRequireInfo.taskRequire.nowNum = num;
            user.SendChat(GetTaskProgress(cacheTaskRequireInfo));
            if (cacheTaskRequireInfo.taskRequire.nowNum >= cacheTaskRequireInfo.taskRequire.totalNum)
            {
              needRemoveIdxs.Add(idx);
            }
            tasks.Add(cacheTaskRequireInfo.task);
          }
        }
        else
        {
          needRemoveIdxs.Add(idx);
        }
        idx++;
      }
      // 移除任务进度
      for (int i = needRemoveIdxs.Count - 1; i >= 0; i--)
      {
        CacheTaskRequireInfo cacheTaskRequireInfo = cacheTaskRequireInfos[needRemoveIdxs[i]];
        // 如果任务需求是获得物品，则需要移除怪物掉落信息
        if (cacheTaskRequireInfo != null && cacheTaskRequireInfo.taskRequire.requireType == TaskRequireType.Get_Thing_Cond)
        {
          BaseTask task = cacheTaskRequireInfo.task;
          task.monDropInfos?.Where(monDropInfo => monDropInfo.thingName == cacheTaskRequireInfo.taskRequire.thingName)
                .ToList()
                .ForEach(monDropInfo =>
                {
                  // 移除怪物掉落信息
                  self.monDropMap.TryGetValue(monDropInfo.monBaseType, out List<MonDropInfo> monDropInfos);
                  monDropInfos?.RemoveAll(m => m.thingName == monDropInfo.thingName);
                  if (monDropInfos == null || monDropInfos.Count == 0)
                  {
                    self.monDropMap.TryRemove(monDropInfo.monBaseType, out _);
                  }
                });
        }
      }
      // 移除任务需求
      if (cacheTaskRequireInfos.Count == 0)
      {
        self.taskRequireMap.TryRemove(key, out _);
      }
      user.SendMessage(ServerUpdateTaskInfoMsg.Create([.. tasks]));
    }

    public static LogicRet TaskCanFinish(this TaskComponent self, BaseTask task)
    {
      User user = self.GetParent<User>();
      if (task.taskSubType == TaskSubTypeEnum.BiaoChe_Task)
      {
        return new LogicRet(false, "押镖任务无法直接完成");
      }
      if (task.requireLists == null || task.requireLists.Count == 0)
      {
        return new LogicRet(true, "任务可完成");
      }
      MapNode mapNode = user.GetParent<MapNode>();
      HashSet<NpcNameEnum> mapNpcNames = mapNode.npcInPoint.Values.Select(npc => npc.name).ToHashSet();
      foreach (TaskRequire taskRequire in task.requireLists)
      {
        if (taskRequire.requireType == TaskRequireType.Find_Npc_Cond && mapNpcNames.Contains(taskRequire.npcName))
        {
          continue;
        }
        else if (taskRequire.nowNum < taskRequire.totalNum)
        {
          return new LogicRet(false, taskRequire.simpleName + "未完成");
        }
        if (taskRequire.requireType == TaskRequireType.Get_Thing_Cond)
        {
          int totalNum = user.GetComponent<BagComponent>().GetThingTotalNum(taskRequire.thingName);
          if (totalNum < taskRequire.nowNum)
          {
            return new LogicRet(false, "背包中的" + taskRequire.thingName.ToString() + "不足");
          }
        }
      }
      return new LogicRet(true, "任务可完成");
    }

    public static void FinishTask(this TaskComponent self, TaskIdEnum taskId, ThingNameEnum selectThingName = ThingNameEnum.None)
    {
      User user = self.GetParent<User>();
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      self.nowTasks.TryGetValue(taskId, out BaseTask task);
      if (task == null)
      {
        return;
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      // 经验
      if (task.exp > 0)
      {
        user.AddUserExp(task.exp, false);
      }
      // 完成任务时，需要扣除任务需求的物品
      List<ThingGiveInfo> thingGiveInfos = new();
      if (task.requireLists != null)
      {
        foreach (TaskRequire taskRequire in task.requireLists)
        {
          if (taskRequire.requireType != TaskRequireType.Get_Thing_Cond)
          {
            continue;
          }
          thingGiveInfos.Add(new ThingGiveInfo
          {
            thingName = taskRequire.thingName,
            ownType = OwnType.None,
            num = taskRequire.totalNum
          });
        }
      }
      if (thingGiveInfos.Count > 0)
      {
        bagComponent.RemoveGiveThingWithSend(thingGiveInfos);
      }
      // 货币
      if (task.coin > 0)
      {
        bagComponent.AddAllCoinWithSend(task.coin);
      }
      // 奖励物品，多选一
      if (task.rewardLists != null)
      {
        thingGiveInfos = new();
        foreach (TaskReward taskReward in task.rewardLists)
        {
          if (!taskReward.chooseOne)
          {
            thingGiveInfos.Add(new ThingGiveInfo
            {
              thingName = taskReward.thingName,
              ownType = taskReward.ownType,
              num = taskReward.num
            });
          }
          else if (selectThingName != ThingNameEnum.None && selectThingName == taskReward.thingName)
          {
            thingGiveInfos.Add(new ThingGiveInfo
            {
              thingName = taskReward.thingName,
              ownType = taskReward.ownType,
              num = taskReward.num
            });
          }
        }
        bagComponent.GiveThing(thingGiveInfos);
      }
      // 学习技能
      if (task.learnSkill != SkillIdEnum.None)
      {
        skillComponent.AddLearnSkill(task.learnSkill);
      }
      // 关系经验
      if (task.relationLevelExp != null)
      {
        foreach (RelationTypeEnum relationType in task.relationLevelExp.Keys)
        {
          int addExp = task.relationLevelExp[relationType];
          if (addExp <= 0)
          {
            continue;
          }
          relationComponent.AddRelationExp(relationType, addExp);
        }
      }
      // 技能经验
      if (task.skillLevelExp != null)
      {
        foreach (SkillIdEnum skillId in task.skillLevelExp.Keys)
        {
          int addExp = task.skillLevelExp[skillId];
          if (addExp <= 0)
          {
            continue;
          }
          skillComponent.AddBaseSkillExp(skillId, addExp);
        }
      }
      // 镖车和使命任务只限制次数，不限制完成
      if (task.taskSubType != TaskSubTypeEnum.BiaoChe_Task && task.taskSubType != TaskSubTypeEnum.ShiMing_Task)
      {
        self.finishedTasks.Add(taskId);
      }
      self.RemoveChild(task.taskEntityId);
      user.SendMessage(ServerUpdateTaskInfoMsg.CreateRemove(taskId), new ServerShowToastMsg
      {
        message = "完成任务：" + task.idEnum.ToString()
      });
    }

    public static TaskIdEnum GetNextTaskId(this TaskComponent self, BaseTask task)
    {
      if (task.nextTask != TaskIdEnum.None && self.IsTaskCanStart(task.nextTask).IsSuccess)
      {
        return task.nextTask;
      }
      if (task.nextTasks != null && task.nextTasks.Count > 0)
      {
        foreach (TaskIdEnum nextTask in task.nextTasks)
        {
          BaseTask nextTaskObj = GlobalInfoCache.Instance.GetBaseTask(nextTask);
          if (nextTaskObj == null)
          {
            continue;
          }
          if (self.IsTaskCanStart(nextTaskObj).IsSuccess)
          {
            return nextTask;
          }
        }
      }
      return TaskIdEnum.None;
    }
  }
}