using System.Collections.Generic;
using MongoDB.Driver;
using Quartz.Util;

namespace MaoYouJi
{
  [FriendOf(typeof(TaskComponent))]
  public static partial class TaskQuerySystem
  {
    public static List<BaseTask> GetTasksByStartNpc(this TaskComponent self, NpcNameEnum npcName, TaskTypeEnum taskType, TaskSubTypeEnum taskSubType)
    {
      var collection = self.Root().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<BaseTask>();
      var filterBuilder = Builders<BaseTask>.Filter;
      var filter = filterBuilder.Eq(x => x.startNpc, npcName);
      if (taskType != TaskTypeEnum.None)
      {
        filter &= filterBuilder.Eq(x => x.taskType, taskType);
      }
      if (taskSubType != TaskSubTypeEnum.None)
      {
        filter &= filterBuilder.Eq(x => x.taskSubType, taskSubType);
      }
      return collection.Find(filter).ToList();
    }
  }
}
