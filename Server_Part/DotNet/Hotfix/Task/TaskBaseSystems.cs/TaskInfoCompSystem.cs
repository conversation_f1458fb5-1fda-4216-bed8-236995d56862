namespace MaoYouJi
{
  [EntitySystemOf(typeof(BiaoCheInfoComp))]
  [FriendOf(typeof(BiaoCheInfoComp))]
  public static partial class BiaoCheInfoCompSystem
  {
    [EntitySystem]
    private static void Awake(this BiaoCheInfoComp self, MonsterInfo monsterInfo)
    {
      self.monsterInfo = monsterInfo;
    }

    [EntitySystem]
    private static void Destroy(this BiaoCheInfoComp self)
    {
      MonsterInfo monsterInfo = self.monsterInfo;
      // monsterInfo?.Dispose();
    }
  }
}