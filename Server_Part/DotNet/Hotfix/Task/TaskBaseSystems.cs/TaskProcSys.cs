namespace MaoYouJi
{
  public static class TaskProcSys
  {
    /**
   * 根据任务需求生成唯一的键值
   * 
   * @param taskRequire 任务需求对象
   * @return 对应的键值字符串
   */
    public static string getTaskRequireKey(TaskRequire taskRequire)
    {
      switch (taskRequire.requireType)
      {
        case TaskRequireType.Kill_Mon_Cond: // 击杀怪物条件
          return taskRequire.requireType.ToString() + "_" + taskRequire.monBaseType.ToString();
        case TaskRequireType.Get_Thing_Cond: // 获得物品条件
        case TaskRequireType.Sell_Thing_Cond: // 出售物品条件
        case TaskRequireType.Use_Thing_Cond: // 使用物品条件
          return taskRequire.requireType.ToString() + "_" + taskRequire.thingName.ToString();
        case TaskRequireType.Learn_Skill_Cond: // 学习技能条件
          return taskRequire.requireType.ToString() + "_" + taskRequire.skillName.ToString();
        case TaskRequireType.Find_Npc_Cond: // 寻找NPC条件
          return taskRequire.requireType.ToString() + "_" + taskRequire.npcName.ToString();
        case TaskRequireType.Get_Level_Cond: // 达到等级条件
          return taskRequire.requireType.ToString();
      }
      return taskRequire.requireType.ToString();
    }
  }
}