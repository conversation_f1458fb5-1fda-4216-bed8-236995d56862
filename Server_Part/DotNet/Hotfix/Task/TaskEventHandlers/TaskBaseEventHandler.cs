namespace MaoYouJi
{
  // 玩家升级事件
  [Event(SceneType.Map)]
  public class TaskLevelUpEventHandler : AEvent<Scene, LevelUpEvent>
  {
    protected override async ETTask Run(Scene root, LevelUpEvent args)
    {
      User user = args.user;
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      taskComponent.FillTaskRequire(TaskRequireType.Get_Level_Cond, "", (int)attackComponent.level);
      await ETTask.CompletedTask;
    }
  }

  [Event(SceneType.Map)]
  public class TaskUserKillMonsterEventHandler : AEvent<Scene, UserKillMonsterEvent>
  {
    protected override async ETTask Run(Scene root, UserKillMonsterEvent args)
    {
      User killedBy = args.killedBy;
      MonsterInfo deader = args.deader;
      TaskComponent taskComponent = killedBy.GetComponent<TaskComponent>();
      taskComponent.FillTaskRequire(TaskRequireType.Kill_Mon_Cond, deader.monBaseType.ToString(), 1);
      await ETTask.CompletedTask;
    }
  }
}