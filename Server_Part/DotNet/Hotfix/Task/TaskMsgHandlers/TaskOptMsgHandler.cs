using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class StartTaskHandler : MessageLocationHandler<MapNode, ClientStartTaskMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientStartTaskMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      BaseTask cacheTask = GlobalInfoCache.Instance.GetBaseTask(msg.TaskId);
      if (cacheTask == null)
      {
        user.SendToast("任务不存在");
        return;
      }
      if (taskComponent.nowTasks.ContainsKey(msg.TaskId))
      {
        user.SendToast("任务已存在");
        return;
      }
      if (taskComponent.finishedTasks.Contains(msg.TaskId))
      {
        user.SendToast("无法重复进行任务");
        return;
      }
      if (!nowMap.ContainsNpc(cacheTask.startNpc))
      {
        user.SendToast("与NPC不在同一地点");
        return;
      }
      LogicRet logicRet = taskComponent.IsTaskCanStart(cacheTask);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      BaseTask baseTask = cacheTask.Clone() as BaseTask;
      taskComponent.AddChild<OneTask, BaseTask>(baseTask);
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class GiveUpTaskHandler : MessageLocationHandler<MapNode, ClientGiveUpTaskMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientGiveUpTaskMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      taskComponent.nowTasks.TryGetValue(msg.TaskId, out BaseTask task);
      if (task == null)
      {
        user.SendToast("任务不存在");
        return;
      }
      // if (task.taskSubType == TaskSubTypeEnum.BiaoChe_Task)
      // {
      //   taskComponent.RemoveComponent<BiaoCheInfoComp>();
      // }
      taskComponent.RemoveChild(task.taskEntityId);
      user.SendMessage(ServerUpdateTaskInfoMsg.CreateRemove(msg.TaskId));
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class FillNpcTalkHandler : MessageLocationHandler<MapNode, ClientFillNpcTalkMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientFillNpcTalkMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      if (!nowMap.ContainsNpc(msg.NpcName))
      {
        ETLog.Warning($"NPC不在当前地图: {msg.NpcName} {nowMap.mapName} {nowMap.pointName} {msg.TaskId} {user.Id}");
        user.SendToast("NPC不在当前地图");
        return;
      }
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      taskComponent.FillTaskRequire(TaskRequireType.Find_Npc_Cond, msg.NpcName.ToString(), 1, msg.TaskId);
      // return new QueryNpcTalkOptionsOut("close");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class FinishTaskHandler : MessageLocationHandler<MapNode, ClientFinishTaskMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientFinishTaskMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      taskComponent.nowTasks.TryGetValue(msg.TaskId, out BaseTask task);
      if (task == null)
      {
        user.SendToast("任务不存在");
        return;
      }
      if (task.taskSubType == TaskSubTypeEnum.BiaoChe_Task)
      {
        user.SendToast("押镖任务无法直接完成");
        return;
      }
      LogicRet logicRet = taskComponent.TaskCanFinish(task);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      int needCapNum = 0;
      bool needChoose = false, findSelect = false;
      if (task.rewardLists != null)
      {
        foreach (TaskReward taskGive in task.rewardLists)
        {
          if (taskGive.chooseOne)
          {
            if (needChoose == false)
            {
              ++needCapNum;
            }
            needChoose = true;
            if (taskGive.thingName == msg.SelectThingName)
            {
              findSelect = true;
            }
          }
          else
          {
            ++needCapNum;
          }
        }
      }
      // 检查背包容量，如果不足则提示
      if (!bagComponent.HasEnoughCapacity(needCapNum))
      {
        user.SendToast("背包容量不足");
        return;
      }
      if (needChoose)
      {
        if (msg.SelectThingName == ThingNameEnum.None || !findSelect)
        {
          user.SendToast("请选择任务奖励");
          return;
        }
        taskComponent.FinishTask(msg.TaskId, msg.SelectThingName);
      }
      else
      {
        taskComponent.FinishTask(msg.TaskId);
      }
      // 如果任务没有后续任务，则关闭对话框
      TaskIdEnum nextTaskId = taskComponent.GetNextTaskId(task);
      if (nextTaskId != TaskIdEnum.None)
      {
        user.SendMessage(new ServerStartNextTaskMsg
        {
          TaskId = nextTaskId
        });
      }

      await ETTask.CompletedTask;
      // return new QueryNpcTalkOptionsOut("close");
    }
  }
}