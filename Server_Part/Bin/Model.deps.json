{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Model/1.0.0": {"dependencies": {"ConcurrentHashSet": "1.3.0", "Core": "1.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Quartz": "3.14.0", "Share.Analyzer": "1.0.0", "Share.SourceGenerator": "1.0.0", "ThirdParty": "1.0.0", "Core.Reference": "*******", "ThirdParty.Reference": "*******"}, "runtime": {"Model.dll": {}}}, "CommandLineParser/2.8.0": {"runtime": {"lib/netstandard2.0/CommandLine.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.0.0"}}}, "ConcurrentHashSet/1.3.0": {"runtime": {"lib/netstandard2.0/ConcurrentCollections.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}}, "EPPlus/5.8.8": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "1.4.1", "System.ComponentModel.Annotations": "5.0.0", "System.Drawing.Common": "5.0.0", "System.Security.Cryptography.Pkcs": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/net5.0/EPPlus.dll": {"assemblyVersion": "5.8.8.0", "fileVersion": "5.8.8.0"}}}, "MemoryPack/1.10.0": {"dependencies": {"MemoryPack.Core": "1.10.0", "MemoryPack.Generator": "1.10.0"}}, "MemoryPack.Core/1.10.0": {"runtime": {"lib/net7.0/MemoryPack.Core.dll": {"assemblyVersion": "1.10.0.0", "fileVersion": "1.10.0.0"}}}, "MemoryPack.Generator/1.10.0": {}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {}, "Microsoft.CodeAnalysis.Common/4.0.1": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.2", "System.Collections.Immutable": "5.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.121.55815"}}}, "Microsoft.CodeAnalysis.CSharp/4.0.1": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.121.55815"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.IO.RecyclableMemoryStream/1.4.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "MongoDB.Bson/2.17.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.17.1": {"dependencies": {"MongoDB.Bson": "2.17.1", "MongoDB.Driver.Core": "2.17.1", "MongoDB.Libmongocrypt": "1.5.5"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.Core/2.17.1": {"dependencies": {"DnsClient": "1.6.1", "MongoDB.Bson": "2.17.1", "MongoDB.Libmongocrypt": "1.5.5", "SharpCompress": "0.30.1", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "runtimeTargets": {"runtimes/linux/native/libsnappy64.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux/native/libzstd.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libsnappy64.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libzstd.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/libzstd.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/snappy32.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/snappy64.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "MongoDB.Libmongocrypt/1.5.5": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.5.5.0", "fileVersion": "1.5.5.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "NLog/4.7.15": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "*******", "fileVersion": "4.7.15.867"}}}, "Quartz/3.14.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Quartz.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpZipLib/1.3.3": {"runtime": {"lib/netstandard2.1/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "System.Buffers/4.5.1": {}, "System.Collections.Immutable/5.0.0": {}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Drawing.Common/5.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Formats.Asn1/5.0.0": {}, "System.Memory/4.5.4": {}, "System.Reflection.Metadata/5.0.0": {}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.Pkcs/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "Core/1.0.0": {"dependencies": {"ThirdParty": "1.0.0"}, "runtime": {"Core.dll": {"fileVersion": "0.0.0.0"}}}, "Share.Analyzer/1.0.0": {"runtime": {"Share.Analyzer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Share.SourceGenerator/1.0.0": {"runtime": {"Share.SourceGenerator.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ThirdParty/1.0.0": {"dependencies": {"CommandLineParser": "2.8.0", "EPPlus": "5.8.8", "MemoryPack": "1.10.0", "Microsoft.CodeAnalysis.CSharp": "4.0.1", "Microsoft.CodeAnalysis.Common": "4.0.1", "MongoDB.Driver": "2.17.1", "NLog": "4.7.15", "SharpZipLib": "1.3.3"}, "runtime": {"ThirdParty.dll": {"fileVersion": "0.0.0.0"}}}, "Core.Reference/*******": {"runtime": {"Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ThirdParty.Reference/*******": {"runtime": {"ThirdParty.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Model/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommandLineParser/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-eco2HlKQBY4Joz9odHigzGpVzv6pjsXnY5lziioMveQxr+i2Z7xYcIOMeZTgYiqnMtMAbXMXsVhrNfWO5vJS8Q==", "path": "commandlineparser/2.8.0", "hashPath": "commandlineparser.2.8.0.nupkg.sha512"}, "ConcurrentHashSet/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-a30gfk4WDn2f7sOisXpko+CrQ7s5nL1ZWk4afnRwwRQHWPRrRSZpzn5Dz2YWpJtS90NJqkytQ/MkzHq4QYTIbg==", "path": "concurrenthashset/1.3.0", "hashPath": "concurrenthashset.1.3.0.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "EPPlus/5.8.8": {"type": "package", "serviceable": true, "sha512": "sha512-lt/BK93+0XU1+/fYDof8vdOU1ioNFVDJclx/JBdeVm4bEFsnDKXOUt4YPu5h1tI7qwAB9SQJ2/cnD8/SQUuZIg==", "path": "epplus/5.8.8", "hashPath": "epplus.5.8.8.nupkg.sha512"}, "MemoryPack/1.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-QgvbIRqAXUvp4/3WcFahjOM3Hy42Ma2OxFsXitstc5XGHDpEl6zdV8XedxbBpvl29sS++i3BzT71/8ET1SyBvg==", "path": "memorypack/1.10.0", "hashPath": "memorypack.1.10.0.nupkg.sha512"}, "MemoryPack.Core/1.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-MxQ7jag9AOXyAbq7DGvaJBANINLxnkzibOMuwdYAjQDaeIWamIi8Z/QmkOJGBUPGANjIxv2J07rMsFFnHJW/iQ==", "path": "memorypack.core/1.10.0", "hashPath": "memorypack.core.1.10.0.nupkg.sha512"}, "MemoryPack.Generator/1.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-U6J+mUb95KVO+ytZECLYJ4awuypfiGwNvWQDaEid3fKOz0X5GGQsLyKE4A9CyMqU5SZKri+Md9YcLGYMCqvkKA==", "path": "memorypack.generator/1.10.0", "hashPath": "memorypack.generator.1.10.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7xt6zTlIEizUgEsYAIgm37EbdkiMmr6fP6J9pDoKEpiGM4pi32BCPGr/IczmSJI9Zzp0a6HOzpr9OvpMP+2veA==", "path": "microsoft.codeanalysis.analyzers/3.3.2", "hashPath": "microsoft.codeanalysis.analyzers.3.3.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-SMREwaVD5SzatlWhh9aahQAtSWdb63NcE//f+bQzgHSECU6xtDtaxk0kwV+asdFfr6HtW38UeO6jvqdfzudg3w==", "path": "microsoft.codeanalysis.common/4.0.1", "hashPath": "microsoft.codeanalysis.common.4.0.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Q9RxxydPpUElj/x1/qykDTUGsRoKbJG8H5XUSeMGmMu54fBiuX1xyanom9caa1oQfh5JIW1BgLxobSaWs4WyHQ==", "path": "microsoft.codeanalysis.csharp/4.0.1", "hashPath": "microsoft.codeanalysis.csharp.4.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LN322qEKHjuVEhhXueTUe7RNePooZmS8aGid5aK2woX3NPjSnONFyKUc6+JknOS6ce6h2tCLfKPTBXE3mN/6Ag==", "path": "microsoft.extensions.configuration/5.0.0", "hashPath": "microsoft.extensions.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "path": "microsoft.extensions.configuration.json/5.0.0", "hashPath": "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/1.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-6A0fyZkxoUUj1dpXzLAWwI89YmKZ+ZSp1DCg+gN6llcXJwfYo1IIQZoCkuo6T7vUxw/w1CSk/Pl04NQ4fno+DQ==", "path": "microsoft.io.recyclablememorystream/1.4.1", "hashPath": "microsoft.io.recyclablememorystream.1.4.1.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bh6blKG8VAKvXiLe2L+sEsn62nc1Ij34MrNxepD2OCrS5cpCwQa9MeLyhVQPQ/R4Wlzwuy6wMK8hLb11QPDRsQ==", "path": "microsoft.win32.systemevents/5.0.0", "hashPath": "microsoft.win32.systemevents.5.0.0.nupkg.sha512"}, "MongoDB.Bson/2.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-IBr5w6ygeUCTobiS4J2UlYeIsnjSJvOOf31g60EkRa3NIeyrYs7Y51HeOvJ8r6NPcKv1hLj8xwoop6hDTetAdA==", "path": "mongodb.bson/2.17.1", "hashPath": "mongodb.bson.2.17.1.nupkg.sha512"}, "MongoDB.Driver/2.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-A5Yvm3RUdkSYnvKWVb/bZfvhzG+L+t0n80JuUXf0p2QG1TbtqHE9hX/FBK+BoF7sw9rzUTw8VHYeqX5rT0rxdA==", "path": "mongodb.driver/2.17.1", "hashPath": "mongodb.driver.2.17.1.nupkg.sha512"}, "MongoDB.Driver.Core/2.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-lfuuQvCXcco6mG096PL8xRO+dBdHsDTR3DiYfK/ICHgFlL5RfKlBuE0xClEGKtaZ4Spe28/fF/GUcrrA/yfoAQ==", "path": "mongodb.driver.core/2.17.1", "hashPath": "mongodb.driver.core.2.17.1.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-0DV4l2PjXirSJHD/b+LpOK3IfUDvkbpvvZBM2w1aYL6E/4vbhfyr/FP5laDNx2zRylqN1hIZO+EL7NgO/5GpVg==", "path": "mongodb.libmongocrypt/1.5.5", "hashPath": "mongodb.libmongocrypt.1.5.5.nupkg.sha512"}, "NLog/4.7.15": {"type": "package", "serviceable": true, "sha512": "sha512-vXMmsK17hXnMsKd8BdCrJzvb8JMT0+cPgBGRfnqcXIb6y8b6RMIZexO4hMhQgifcTHgU7dbubvCR5A5wX6Ne+A==", "path": "nlog/4.7.15", "hashPath": "nlog.4.7.15.nupkg.sha512"}, "Quartz/3.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8mY6S0FWbtuEFbStUEZAkkGpZLbSuTT0us1w0DUQqhi+vwg/7XXS36TeH3o34zZoqYGyXvk6jrKnW/p6kS8sg==", "path": "quartz/3.14.0", "hashPath": "quartz.3.14.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SharpZipLib/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-N8+hwhsKZm25tDJfWpBSW7EGhH/R7EMuiX+KJ4C4u+fCWVc1lJ5zg1u3S1RPPVYgTqhx/C3hxrqUpi6RwK5+Tg==", "path": "sharpziplib/1.3.3", "hashPath": "sharpziplib.1.3.3.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Drawing.Common/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SztFwAnpfKC8+sEKXAFxCBWhKQaEd97EiOL7oZJZP56zbqnLpmxACWA8aGseaUExciuEAUuR9dY8f7HkTRAdnw==", "path": "system.drawing.common/5.0.0", "hashPath": "system.drawing.common.5.0.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9TPLGjBCGKmNvG8pjwPeuYy0SMVmGZRwlTZvyPHDbYv/DRkoeumJdfumaaDNQzVGMEmbWtg07zUpSW9q70IlDQ==", "path": "system.security.cryptography.pkcs/5.0.0", "hashPath": "system.security.cryptography.pkcs.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Share.Analyzer/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Share.SourceGenerator/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ThirdParty/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Core.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "ThirdParty.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}