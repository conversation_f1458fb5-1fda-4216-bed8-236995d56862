using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdateBagMsg)]
  public partial class ServerUpdateBagMsg : MaoYouMessage, ILocationMessage
  {
    public bool isAll = false;
    public bool isCoin = false;
    public long coinNum = 0, catBeanNum = 0, catEyeNum = 0, packCapNum = 0;
    public List<Thing> updateList;
    public List<long> removeThingIds;
    public BagDaoInfo bag;
  }
}