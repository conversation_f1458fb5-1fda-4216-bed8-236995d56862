using MemoryPack;
using MongoDB.Bson;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientSubmitDaTaoShaJiangZhangMsg)]
  public partial class ClientSubmitDaTaoShaJiangZhangMsg : MaoYouInMessage, ILocationMessage
  {
    public int daTaoShaJiangZhangNum;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientAddFriendMsg)]
  public partial class ClientAddFriendMsg : MaoYouInMessage, ISocialMessage
  {
    public AddFriendTypeEnum addFriendType;
    public string TargetUserId; // 添加的用户ID或者名字
    public string AddMsg; // 添加好友请求消息
  }
}