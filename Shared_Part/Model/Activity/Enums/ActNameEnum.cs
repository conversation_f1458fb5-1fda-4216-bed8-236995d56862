namespace MaoYouJi
{
  public enum ActNameEnum
  {
    None,
    <PERSON>_<PERSON><PERSON>ha, // ("大逃杀"),
    First_Test_Gift, // ("首次测试礼包"),
  }

  public enum ActExistType
  {
    Daily, // ("日常活动"),
    Special, // ("特殊活动"),
    Festival, // ("节日活动"),
    Forever, // ("永久活动"),
  }

  public enum ActStateEnum
  {
    Prepare, // ("预热中"),
    InProgress, // ("进行中"),
    Finished, // ("已完成"),
    Ended, // ("已结束"),
  }
}