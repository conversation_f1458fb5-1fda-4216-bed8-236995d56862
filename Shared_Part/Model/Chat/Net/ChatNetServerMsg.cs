using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerShowToastMsg)]
  public partial class ServerShowToastMsg : MaoYouMessage, ILocationMessage
  {
    [MemoryPackConstructor]
    public ServerShowToastMsg() { }

    public ServerShowToastMsg(string message)
    {
      this.message = message;
    }

    public string message;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerSendChatMsg)]
  public partial class ServerSendChatMsg : MaoYouMessage, ILocationMessage
  {
    public string content;
    public ChatType chatType = ChatType.Sys_Chat;
    public FightInfo fightInfo;
    public FightInfo targetFightInfo;
    public Dictionary<string, Thing> things;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerShowNoticeMsg)]
  public partial class ServerShowNoticeMsg : MaoYouMessage, ILocationMessage
  {
    public ChatType chatType;
    public string content;
    public string nickName;
  }
}