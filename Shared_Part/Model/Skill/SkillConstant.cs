using System.Collections.Generic;

namespace Mao<PERSON>ouJi
{
  [EnableClass]
  public class SkillConstant
  {
    [StaticField]
    public static Dictionary<MaoJob, SkillIdEnum> JobBaseSkillId = new Dictionary<MaoJob, SkillIdEnum>() {
      { MaoJob.<PERSON><PERSON><PERSON>_<PERSON>, SkillIdEnum.<PERSON><PERSON><PERSON>_BiRen },
      { MaoJob.<PERSON>_<PERSON>, SkillIdEnum.Ba<PERSON>an_<PERSON> },
      { MaoJob.<PERSON>, SkillIdEnum.<PERSON>Z<PERSON>_<PERSON> },
      { MaoJob.<PERSON>_<PERSON>, SkillIdEnum.JuFeng_DaoFa },
      { MaoJob.<PERSON>_<PERSON>, SkillIdEnum.YouMin_Zhua },
      { MaoJ<PERSON>.<PERSON>_<PERSON>, SkillIdEnum.HuoXi_FaShu },
      { MaoJ<PERSON>.<PERSON>_<PERSON>, SkillIdEnum.BinXi_FaShu },
      { MaoJ<PERSON>.<PERSON>, SkillIdEnum.FengXi_FaShu },
      { MaoJob.<PERSON>ian_<PERSON><PERSON><PERSON><PERSON>, SkillIdEnum.Sheng_MoFa },
      { <PERSON>J<PERSON>.<PERSON><PERSON>_<PERSON>, SkillIdEnum.An_MoFa },
    };

    public static MaoJob GetSkillMaoJob(SkillIdEnum skillId)
    {
      foreach (var item in JobBaseSkillId)
      {
        if (item.Value == skillId)
        {
          return item.Key;
        }
      }
      return MaoJob.None;
    }

    public static SkillIdEnum GetSkillIdByMaoJob(MaoJob job)
    {
      JobBaseSkillId.TryGetValue(job, out SkillIdEnum skillId);
      return skillId;
    }

    public static WeaponType GetJobWeaponType(MaoJob job)
    {
      JobWeaponType.TryGetValue(job, out WeaponType weaponType);
      return weaponType;
    }

    [StaticField]
    public static Dictionary<MaoJob, WeaponType> JobWeaponType = new Dictionary<MaoJob, WeaponType>() {
        // 战士
        { MaoJob.AnYin_CiKe, WeaponType.Dagger },
        { MaoJob.QianKun_QiangShou, WeaponType.Spear },
        { MaoJob.HuangJia_WeiShi, WeaponType.Sword },
        { MaoJob.JiFeng_LangKe, WeaponType.Knife },
        { MaoJob.NuLing_WuZhe, WeaponType.Claw },
        // 法师
        { MaoJob.Yan_ShuShi, WeaponType.Wand },
        { MaoJob.Bin_ShuShi, WeaponType.Wand },
        { MaoJob.Feng_ShuShi, WeaponType.Wand },
        { MaoJob.Tian_DaoShi, WeaponType.Wand },
        { MaoJob.Tian_MinShi, WeaponType.Wand },
    };
  }
}