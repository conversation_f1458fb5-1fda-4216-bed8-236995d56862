namespace MaoYouJi
{
  public static class CoroutineLockType
  {
    public const int None = 0;
    public const int Location = 1;                  // location进程上使用
    public const int MessageLocationSender = 2;       // MessageLocationSender中队列消息 
    public const int Mailbox = 3;                   // Mailbox中队列
    public const int UnitId = 4;                    // Map服务器上线下线时使用
    public const int DB = 5;
    public const int Resources = 6;
    public const int ResourcesLoader = 7;
    public const int UserLogIn = 8;
    public const int TransferUser = 9; // 传送用户，我这里直接在不同纤程之间传送用户，所以需要加锁
    public const int Social = 10;
    public const int DaTaoSha = 11; // 大逃杀活动

    public const int Max = 100; // 这个必须最大
  }
}