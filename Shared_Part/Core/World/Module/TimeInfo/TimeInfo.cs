using System;

namespace MaoYouJi
{
  public class TimeInfo : Singleton<TimeInfo>, ISingletonAwake
  {
    private int timeZone;

    public int TimeZone
    {
      get
      {
        return this.timeZone;
      }
      set
      {
        this.timeZone = value;
        dt = dt1970.AddHours(TimeZone);
      }
    }

    private DateTime dt1970;
    private DateTime dt;

    // ping消息会设置该值，原子操作
    public long ServerMinusClientTime { private get; set; }

    public long FrameTime { get; private set; }

    public void Awake()
    {
      this.dt1970 = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
      this.dt = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
      this.FrameTime = this.ClientNow();
      this.TimeZone = 8;
    }

    public void Update()
    {
      // 赋值long型是原子操作，线程安全
      this.FrameTime = this.ClientNow();
    }

    /// <summary> 
    /// 根据时间戳获取时间 
    /// </summary>  
    public DateTime ToDateTime(long timeStamp)
    {
      return dt.AddTicks(timeStamp * 10000);
    }

    public DateTime DelayDateTime(long timeStamp)
    {
      return dt1970.AddTicks((timeStamp + ServerNow()) * 10000);
    }

    // 线程安全
    public long ClientNow()
    {
      return (DateTime.UtcNow.Ticks - this.dt1970.Ticks) / 10000;
    }

    public long ServerNow()
    {
      return ClientNow() + this.ServerMinusClientTime;
    }

    public long ClientFrameTime()
    {
      return this.FrameTime;
    }

    public long ServerFrameTime()
    {
      return this.FrameTime + this.ServerMinusClientTime;
    }

    public long Transition(DateTime d)
    {
      return (d.Ticks - dt.Ticks) / 10000;
    }

    /// <summary>
    /// 将字符串时间（如"06:00"）转为今天对应的毫秒时间戳
    /// </summary>
    public long TodayTimeToTimestamp(string timeStr)
    {
      // 获取当前日期
      DateTime now = DateTime.UtcNow.AddHours(TimeZone);
      DateTime today = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0, DateTimeKind.Utc);

      // 解析字符串时间
      var parts = timeStr.Split(':');
      int hour = int.Parse(parts[0]);
      int minute = int.Parse(parts[1]);

      // 构造今天的目标时间
      DateTime target = today.AddHours(hour).AddMinutes(minute);

      // 转为时间戳
      return Transition(target);
    }
  }
}