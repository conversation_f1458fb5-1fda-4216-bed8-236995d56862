using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using ConcurrentCollections;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(BagComponent))]
  [FriendOf(typeof(BagComponent))]
  public static partial class BagGetSystem
  {
    [EntitySystem]
    private static void Awake(this BagComponent self, BagType bagType, int capacity)
    {
      self.bagType = bagType;
      self.capacity = capacity;
    }
    // 获得物品
    public static T GetThingInBag<T>(this BagComponent bag, long thingId) where T : Thing
    {
      if (bag.thingMap.TryGetValue(thingId, out Thing thing))
      {
        return thing as T;
      }
      return null;
    }
    // 根据ID列表获取背包中的物品
    public static List<Thing> GetThings(this BagComponent bag, params long[] thingIds)
    {
      List<Thing> rets = new();
      foreach (long thingId in thingIds)
      {
        if (bag.thingMap.TryGetValue(thingId, out Thing thing))
        {
          rets.Add(thing);
        }
      }
      return rets;
    }

    // 获得物品
    public static T GetThingInBag<T>(this BagComponent bag, ThingNameEnum thingName, ThingGrade thingGrade = ThingGrade.None, OwnType ownType = OwnType.None) where T : Thing
    {
      if (bag.nameIdMap.TryGetValue(thingName, out ConcurrentHashSet<long> thingIds))
      {
        foreach (long thingId in thingIds)
        {
          if (bag.thingMap.TryGetValue(thingId, out Thing thing))
          {
            if (thingGrade != ThingGrade.None && thing.grade != thingGrade)
            {
              continue;
            }
            if (ownType != OwnType.None && thing.ownType != ownType)
            {
              continue;
            }
            return thing as T;
          }
        }
      }
      return null;
    }

    // 获得物品总数
    public static int GetThingTotalNum(this BagComponent bag, ThingNameEnum thingName, ThingGrade thingGrade = ThingGrade.None, OwnType ownType = OwnType.None)
    {
      int totalNum = 0;
      if (bag.nameIdMap.TryGetValue(thingName, out ConcurrentHashSet<long> thingIds))
      {
        foreach (long thingId in thingIds)
        {
          if (bag.thingMap.TryGetValue(thingId, out Thing thing))
          {
            if (thingGrade != ThingGrade.None && thing.grade != thingGrade)
            {
              continue;
            }
            if (ownType != OwnType.None && thing.ownType != ownType)
            {
              continue;
            }
            totalNum += thing.num;
          }
        }
      }
      return totalNum;
    }

    public static BagDaoInfo GetBagDaoInfo(this BagComponent bag)
    {
      BagDaoInfo bagDaoInfo = new BagDaoInfo();
      bagDaoInfo.bagType = bag.bagType;
      bagDaoInfo.coin = bag.coin;
      bagDaoInfo.catBean = bag.catBean;
      bagDaoInfo.catEye = bag.catEye;
      bagDaoInfo.name = bag.name;
      bagDaoInfo.capacity = bag.capacity;
      bag.bagIdLock.EnterReadLock();
      try
      {
        bagDaoInfo.thingIds = new(bag.thingIds);
        bagDaoInfo.thingMap = new(bag.thingMap);
        foreach (var entry in bag.nameIdMap)
        {
          bagDaoInfo.nameIdMap[entry.Key] = new(entry.Value);
        }
      }
      finally
      {
        bag.bagIdLock.ExitReadLock();
      }
      return bagDaoInfo;
    }

    public static void ParseBagDaoInfo(this BagComponent bag, BagDaoInfo bagDaoInfo)
    {
      bag.bagType = bagDaoInfo.bagType;
      bag.coin = bagDaoInfo.coin;
      bag.catBean = bagDaoInfo.catBean;
      bag.catEye = bagDaoInfo.catEye;
      bag.name = bagDaoInfo.name;
      bag.capacity = bagDaoInfo.capacity;
      bag.bagIdLock.EnterWriteLock();
      try
      {
        bag.thingIds = bagDaoInfo.thingIds;
        bag.thingMap = new(bagDaoInfo.thingMap);
        foreach (var entry in bagDaoInfo.nameIdMap)
        {
          bag.nameIdMap[entry.Key] = new(entry.Value);
        }
      }
      finally
      {
        bag.bagIdLock.ExitWriteLock();
      }
    }

    public static bool CheckAllSameIds(this BagComponent bag, List<long> thingIds)
    {
      bag.bagIdLock.EnterReadLock();
      try
      {
        return bag.thingIds.SequenceEqual(thingIds);
      }
      finally
      {
        bag.bagIdLock.ExitReadLock();
      }
    }

    public static void ReOrderBag(this BagComponent bag)
    {
      bag.bagIdLock.EnterWriteLock();
      try
      {
        List<Thing> things = new(bag.thingMap.Values);
        things.Sort((a, b) =>
        {
          if (a.grade != b.grade)
          {
            return b.grade.CompareTo(a.grade);
          }
          if (a.thingType != b.thingType)
          {
            return b.thingType.CompareTo(a.thingType);
          }
          if (a.ownType != b.ownType)
          {
            return b.ownType.CompareTo(a.ownType);
          }
          return b.thingName.CompareTo(a.thingName);
        });
        List<long> newThingIds = new();
        ConcurrentDictionary<ThingNameEnum, ConcurrentHashSet<long>> newNameIdMap = new();
        foreach (Thing thing in things)
        {
          newThingIds.Add(thing.thingId);
          if (newNameIdMap.TryGetValue(thing.thingName, out ConcurrentHashSet<long> idSet))
          {
            idSet.Add(thing.thingId);
          }
          else
          {
            idSet = new ConcurrentHashSet<long> { thing.thingId };
            newNameIdMap[thing.thingName] = idSet;
          }
        }
        bag.thingIds = newThingIds;
        bag.nameIdMap = newNameIdMap;
      }
      finally
      {
        bag.bagIdLock.ExitWriteLock();
      }
    }
  }
}