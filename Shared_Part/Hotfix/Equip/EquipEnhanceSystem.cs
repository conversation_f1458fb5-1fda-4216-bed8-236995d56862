using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  public static class EquipEnhanceSystem
  {
    public static EquipMainAttrType[] GetEquipMainAndSubAttr(this Equipment equipment)
    {
      EquipMainAttrType mainAttr = EquipMainAttrType.None;
      EquipMainAttrType subAttr = EquipMainAttrType.None;
      int maxScore = 0, secondMaxScore = 0;

      // 武器和盾牌有固定主属性
      if (equipment.equipPart == EquipPart.Wu_Qi)
      {
        mainAttr = EquipMainAttrType.Attack;
        maxScore = int.MaxValue;
      }
      else if (equipment.equipPart == EquipPart.Dun_Pai)
      {
        mainAttr = EquipMainAttrType.Defense;
        maxScore = int.MaxValue;
      }

      EquipAttrInfo attrInfo = equipment.baseAttrInfo;
      Dictionary<EquipMainAttrType, int> scoreMap = new Dictionary<EquipMainAttrType, int>();

      // 计算所有属性的评分
      if (attrInfo.minAttack != 0 && attrInfo.maxAttack != 0)
      {
        scoreMap[EquipMainAttrType.Attack] = (attrInfo.maxAttack + attrInfo.minAttack) / 2 * 5;
      }
      if (attrInfo.realAttack != 0)
      {
        scoreMap[EquipMainAttrType.RealAttack] = attrInfo.realAttack * 20;
      }
      if (attrInfo.crit != 0)
      {
        scoreMap[EquipMainAttrType.Crit] = attrInfo.crit * 5;
      }
      if (attrInfo.critDamage != 0)
      {
        scoreMap[EquipMainAttrType.CritDamage] = attrInfo.critDamage * 5;
      }
      if (attrInfo.hitRate != 0)
      {
        scoreMap[EquipMainAttrType.HitRate] = attrInfo.hitRate * 5;
      }
      if (attrInfo.miss != 0)
      {
        scoreMap[EquipMainAttrType.MissRate] = attrInfo.miss * 5;
      }
      if (attrInfo.defense != 0)
      {
        scoreMap[EquipMainAttrType.Defense] = attrInfo.defense * 5;
      }
      if (attrInfo.magicDefense != 0)
      {
        int defenseScore = scoreMap.ContainsKey(EquipMainAttrType.Defense) ? scoreMap[EquipMainAttrType.Defense] : 0;
        scoreMap[EquipMainAttrType.Defense] = Math.Max(defenseScore, attrInfo.magicDefense * 5);
      }
      if (attrInfo.blood != 0)
      {
        scoreMap[EquipMainAttrType.Blood] = attrInfo.blood * 5;
      }
      if (attrInfo.blue != 0)
      {
        scoreMap[EquipMainAttrType.Blue] = attrInfo.blue * 5;
      }
      if (attrInfo.strength != 0)
      {
        scoreMap[EquipMainAttrType.Strength] = attrInfo.strength * 5;
      }
      if (attrInfo.power != 0)
      {
        scoreMap[EquipMainAttrType.Power] = attrInfo.power * 5;
      }
      if (attrInfo.quick != 0)
      {
        scoreMap[EquipMainAttrType.Quick] = attrInfo.quick * 5;
      }
      if (attrInfo.iq != 0)
      {
        scoreMap[EquipMainAttrType.Iq] = attrInfo.iq * 5;
      }
      if (attrInfo.mind != 0)
      {
        scoreMap[EquipMainAttrType.Mind] = attrInfo.mind * 5;
      }

      // 如果不是武器或盾牌，找出评分最高的两个属性
      if (mainAttr == EquipMainAttrType.None)
      {
        foreach (var entry in scoreMap)
        {
          if (entry.Value > maxScore)
          {
            secondMaxScore = maxScore;
            subAttr = mainAttr;
            maxScore = entry.Value;
            mainAttr = entry.Key;
          }
          else if (entry.Value > secondMaxScore)
          {
            secondMaxScore = entry.Value;
            subAttr = entry.Key;
          }
        }
      }
      else
      {
        // 如果是武器或盾牌，只需要找出评分第二高的属性
        foreach (var entry in scoreMap)
        {
          if (entry.Key != mainAttr && entry.Value > secondMaxScore)
          {
            secondMaxScore = entry.Value;
            subAttr = entry.Key;
          }
        }
      }
      return new EquipMainAttrType[] { mainAttr, subAttr };
    }

    public static double[] GetEnhanceSuccessRate()
    {
      return new double[] { 0.90, 0.80, 0.70, 0.64, 0.58, 0.50, 0.40, 0.33, 0.25, 0.18, 0.15, 0.13, 0.10, 0.08, 0.05 };
    }

    public static long[] GetEnhanceAddPercent()
    {
      return new long[] { 50, 60, 70, 80, 90, 100, 110, 120, 130, 150, 180, 210, 250, 320, 450 };
    }

    // 强化装备, 返回是否成功
    public static double GetEnhanceSuccessRate(this Equipment equip, Treasure shengJiShi)
    {
      double[] baseEnhanceSuccessRate = GetEnhanceSuccessRate();
      // 基础成功率
      double baseRate = equip.enhanceLevel >= baseEnhanceSuccessRate.Length ? baseEnhanceSuccessRate[baseEnhanceSuccessRate.Length - 1] : baseEnhanceSuccessRate[equip.enhanceLevel];

      // 根据装备品级调整成功率
      switch (equip.grade)
      {
        case ThingGrade.NORMAL:
          baseRate *= 1.0;
          break;
        case ThingGrade.GOOD:
          baseRate *= 0.9;
          break;
        case ThingGrade.EXCELLENT:
          baseRate *= 0.8;
          break;
        case ThingGrade.LEGEND:
          baseRate *= 0.65;
          break;
        case ThingGrade.MAGIC:
          baseRate *= 0.5;
          break;
      }

      // 根据装备等级调整成功率
      baseRate *= 1.0 - equip.level * 0.006; // 每级降低0.5%成功率

      // 根据升级石品质提升成功率
      double finalRate = baseRate;
      if (shengJiShi == null || shengJiShi.grade == ThingGrade.GOOD)
      {
        finalRate = baseRate;
      }
      else if (shengJiShi.grade == ThingGrade.EXCELLENT)
      {
        finalRate = baseRate * 1.2; // 高级升级石提升20%成功率
      }
      else if (shengJiShi.grade == ThingGrade.LEGEND)
      {
        finalRate = baseRate * 1.5; // 完美升级石提升50%成功率
      }
      // 确保最终概率在合理范围内
      finalRate = Math.Min(0.95, Math.Max(0.02, finalRate));
      return finalRate;
    }

    // 掉级概率
    public static double DropLevelRate(this Equipment equipment)
    {
      // 8级以下不掉级
      if (equipment.enhanceLevel < 8)
      {
        return 0;
      }

      // 计算掉级基础概率
      double dropRate = 0.35 + (equipment.enhanceLevel - 8) * 0.1;

      // 根据装备品级调整掉级概率
      switch (equipment.grade)
      {
        case ThingGrade.MAGIC:
          dropRate *= 1.2; // 神话装备掉级概率增加20%
          break;
        case ThingGrade.LEGEND:
          dropRate *= 1.1; // 传说装备掉级概率增加10%
          break;
        case ThingGrade.EXCELLENT:
          dropRate *= 1.0; // 精良装备保持原有概率
          break;
        case ThingGrade.GOOD:
          dropRate *= 0.9; // 优秀装备掉级概率降低10%
          break;
        case ThingGrade.NORMAL:
          dropRate *= 0.8; // 普通装备掉级概率降低20%
          break;
      }

      // 确保最终概率在合理范围内
      dropRate = Math.Min(0.95, Math.Max(0.35, dropRate));

      return dropRate;
    }

    // 获取强化增加属性的百分比，千分制
    public static long GetEnhanceAddPercent(int level)
    {
      long[] enhanceAddPercent = GetEnhanceAddPercent();
      long addPercent = 0;
      for (int i = 0; i < enhanceAddPercent.Length && i < level; i++)
      {
        addPercent += enhanceAddPercent[i];
      }
      return addPercent;
    }

    public static long GetEnhanceAddPercent(this Equipment equipment)
    {
      return GetEnhanceAddPercent(equipment.enhanceLevel);
    }

    public static void AddEnhanceAttr(EquipAttrInfo nowAttrInfo, EquipAttrInfo baseAttr, EquipMainAttrType attrType,
      long addPercent)
    {
      if (attrType == EquipMainAttrType.Attack)
      {
        nowAttrInfo.minAttack += (int)(baseAttr.minAttack * addPercent / 1000);
        nowAttrInfo.maxAttack += (int)(baseAttr.maxAttack * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.Defense)
      {
        if (baseAttr.defense != 0)
        {
          nowAttrInfo.defense += (int)(baseAttr.defense * addPercent / 1000);
        }
        if (baseAttr.magicDefense != 0)
        {
          nowAttrInfo.magicDefense += (int)(baseAttr.magicDefense * addPercent / 1000);
        }
      }
      else if (attrType == EquipMainAttrType.RealAttack)
      {
        nowAttrInfo.realAttack += (int)(baseAttr.realAttack * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.Crit)
      {
        nowAttrInfo.crit += (int)(baseAttr.crit * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.CritDamage)
      {
        nowAttrInfo.critDamage += (int)(baseAttr.critDamage * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.HitRate)
      {
        nowAttrInfo.hitRate += (int)(baseAttr.hitRate * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.MissRate)
      {
        nowAttrInfo.miss += (int)(baseAttr.miss * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.Blood)
      {
        nowAttrInfo.blood += (int)(baseAttr.blood * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.Blue)
      {
        nowAttrInfo.blue += (int)(baseAttr.blue * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.Strength)
      {
        nowAttrInfo.strength += (int)(baseAttr.strength * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.Power)
      {
        nowAttrInfo.power += (int)(baseAttr.power * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.Quick)
      {
        nowAttrInfo.quick += (int)(baseAttr.quick * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.Iq)
      {
        nowAttrInfo.iq += (int)(baseAttr.iq * addPercent / 1000);
      }
      else if (attrType == EquipMainAttrType.Mind)
      {
        nowAttrInfo.mind += (int)(baseAttr.mind * addPercent / 1000);
      }
    }

  }
}